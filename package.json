{"name": "mefarm", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.5", "@invertase/react-native-apple-authentication": "^2.4.0", "@microsoft/signalr": "^8.0.7", "@notifee/react-native": "^9.1.3", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.5", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^7.0.12", "@react-navigation/native": "^7.0.7", "@react-navigation/native-stack": "^7.1.8", "@reduxjs/toolkit": "^2.5.1", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@turf/boolean-overlap": "^7.1.0", "@turf/helpers": "^7.1.0", "@turf/turf": "^7.1.0", "@types/jwt-decode": "^3.1.0", "@types/react-native-actionsheet": "^2.4.7", "@types/react-native-maps": "^0.24.2", "@types/react-native-snap-carousel": "^3.8.11", "add": "^2.0.6", "axios": "^1.7.8", "crypto-js": "^4.2.0", "events": "^3.3.0", "jwt-decode": "^4.0.0", "lucide-react-native": "^0.475.0", "metro-config": "^0.81.0", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.3", "react-native-actions-sheet": "^0.9.7", "react-native-actionsheet": "^2.4.2", "react-native-app-intro-slider": "^4.0.4", "react-native-date-picker": "^5.0.7", "react-native-device-info": "^14.0.4", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-fbsdk-next": "^13.3.0", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.21.2", "react-native-image-crop-picker": "^0.41.6", "react-native-image-picker": "^7.1.2", "react-native-image-resizer": "^1.4.5", "react-native-image-viewing": "^0.2.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keychain": "^9.2.2", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.3.0", "react-native-maps": "^1.20.1", "react-native-mmkv": "^3.2.0", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.6.1", "react-native-paper": "^5.12.5", "react-native-permissions": "^5.3.0", "react-native-progress": "^5.0.1", "react-native-reanimated": "^3.17.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.3.0", "react-native-search-filter": "^0.1.5", "react-native-share": "^12.0.11", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.12.0", "react-native-swipeable": "^0.6.0", "react-native-tab-view": "^4.0.5", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.8.2", "react-native-vision-camera": "^4.6.4", "react-native-vlc-media-player": "^1.0.87", "react-native-webview": "^13.15.0", "react-redux": "^9.2.0", "reselect": "^5.1.1", "socket.io-client": "^4.8.1", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "^0.76.5", "@react-native/eslint-config": "0.76.3", "@react-native/gradle-plugin": "^0.77.0", "@react-native/metro-config": "^0.76.5", "@react-native/typescript-config": "0.76.3", "@types/lodash": "^4.17.15", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro": "0.76.0", "metro-react-native-babel-preset": "0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}