buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
        googlePlayServicesAuthVersion = "20.7.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()

        // เพิ่ม repository สำหรับ Google Maps API
        maven {
            url 'https://maps.googleapis.com/maps/api/android/'
        }
    }
}

apply plugin: "com.facebook.react.rootproject"

project.ext {
    set('react-native', [
        versions: [
            android: [
                minSdk    : 24,
                targetSdk : 34,
                compileSdk: 35,
            ],
            firebase: [
                bom: "32.7.2"
            ],
        ],
    ])
}
