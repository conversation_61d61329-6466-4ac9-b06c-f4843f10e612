import {
  Text,
  View,
  Modal,
  Alert,
  Platform,
  StatusBar,
  TextInput,
  ScrollView,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import RNFS from "react-native-fs";
import Video from "react-native-video";
import FastImage from "react-native-fast-image";
import * as Progress from "react-native-progress";
import { Header as HeaderRNE } from "@rneui/themed";
import React, { useState, useEffect, useRef } from "react";
import { scale, moderateScale } from "react-native-size-matters";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { MyImageComponent } from "../../components/cacheFiles/cache";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconError,
  iconDelete,
  iconEditImg,
  iconVideEdit,
  iconSavePost,
  iconCameraEdit,
  iconGalleryEdit,
} from "../../assets/svg/svg_other";
import { goBack_Bg, goBack_gay } from "../../assets/svg/svg_naviagte";
//Components
import { useOrientation } from "../../hooks/useOrientation";
import LoadingBar from "../../components/loading/loadingBar";
//Api
import { upDatePostApi } from "../../action/Mefarm_Social_API";
//Translation
import { useTranslation } from "../i18n";

export default function EditPost({ navigation, route }: any) {
  const orientation = useOrientation();
  //State
  const { t } = useTranslation();
  const [isDate, setDate] = useState<any>(new Date());
  //Null
  const inputRef: any = useRef(null);
  const [imageDimensions, setImageDimensions] = useState<any>(null);
  //String
  const [postId, setPostId] = useState<String>("");
  const [isMessage, setMessage] = useState<String>("");
  const [isProfile, setProfile] = useState<any>("");
  const [isFirstNames, setFirstNames] = useState<String>("");
  const [isEditMessage, setEditMessage] = useState<String>("");
  //Array
  const [imageUris, setImageUris] = useState<any>([]);
  const [fileNames, setFileNames] = useState<any>([]);
  const [typeNames, setTypeNames] = useState<any>([]);
  const [videoUris, setVideoUris] = useState<any>([]);
  const [isImgContents, setImgContents] = useState<any>([]);
  const [videoContents, setVideoContents] = useState<any>([]);
  const [videoFileNames, setVideoFileNames] = useState<any>([]);
  const [videoTypeNames, setVideoTypeNames] = useState<any>([]);
  const [deleteFileIds, setDeleteFileIds] = useState<string[]>([]);
  //Obj
  const [image, setImage] = useState<any>({});
  const [video, setVideo] = useState<any>({});
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isModalDelete, setModalDelete] = useState<boolean>(false);
  const [isModalDeleteVideo, setModalDeleteVideo] = useState<boolean>(false);
  //Number
  const [progress, setProgress] = useState<number>(0);
  //PERMISSIONS
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        // Alert.alert("❌ Camera permission denied");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  useEffect(() => {
    requestCameraPermission();
  }, []);

  //Function
  useEffect(() => {
    inputRef.current?.focus();
    const params = route.params || "";
    const docList = params.docList || "";
    const id = docList.id || "";
    const firstName = docList.firstName || "";
    const updatedAt = docList.updatedAt || "";
    const videoContents = docList.videoContents || "";
    const imageContents = docList.imageContents || "";
    const messageContent = docList.messageContent || "";
    const profileImageUrl = docList.profileImageUrl || "";
    // console.log(JSON.stringify(docList, null, 2));

    setPostId(id);
    setDate(updatedAt);
    setFirstNames(firstName);
    setMessage(messageContent);
    setProfile(profileImageUrl);
    setImgContents(imageContents);
    setVideoContents(videoContents);
  }, []);
  const onEditPost = async () => {
    try {
      setLoadIng(true);
      const totalSteps = imageUris.length + videoUris.length + 2;
      let currentStep = 0;

      const updateProgress = () => {
        currentStep++;
        const percent = Math.round((currentStep / totalSteps) * 100);
        setProgress(percent);
      };

      const imageIds = isImgContents.map((img: any) => img.id);
      // console.log(">>>", imageIds);

      const imageContent = await Promise.all(
        imageUris.map(async (uri: any, index: any) => {
          const base64Data = await convertImageToBase64(uri);
          updateProgress();
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );

      const videoContent = await Promise.all(
        videoUris.map(async (uri: any, index: any) => {
          const base64Data = await convertVideoToBase64(uri);
          updateProgress();
          return {
            fileName: videoFileNames[index] || "",
            contentType: videoTypeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );
      updateProgress();

      const req = {
        id: postId,
        messageContent: isEditMessage || isMessage,
        imageContent: imageContent || "",
        videoContent: videoContent || "",
        deleteFileIds: deleteFileIds,
      };

      // console.log("Request Payload:", JSON.stringify(req, null, 2));

      const res = await upDatePostApi(req);
      updateProgress();
      // console.log(JSON.stringify(data, null, 2));

      navigation.goBack();
    } catch (error) {
      console.error("Error updating post: ", error);
    } finally {
      setLoadIng(false);
    }
  };
  const openCamera = () => {
    const options: any = {
      mediaType: "photo",
      saveToPhotos: true,
      quality: 1,
    };

    launchCamera(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);
      }
    });
  };
  const selectImageFromLibrary = () => {
    launchImageLibrary(
      { mediaType: "photo", selectionLimit: 10 },
      (response: any) => {
        console.log("response: ", response);
        if (!response.didCancel) {
          let newImageUris: string[] = [];
          let newFileNames: string[] = [];
          let newTypeNames: string[] = [];

          if (response.assets) {
            response.assets.forEach((asset: any) => {
              newImageUris.push(asset.uri);
              newFileNames.push(asset.fileName);
              newTypeNames.push(asset.type);
            });
          } else if (response.uri) {
            newImageUris.push(response.uri);
            newFileNames.push(response.fileName);
            newTypeNames.push(response.type);
          }

          setImageUris((prev: any) => [...prev, ...newImageUris]);
          setFileNames((prev: any) => [...prev, ...newFileNames]);
          setTypeNames((prev: any) => [...prev, ...newTypeNames]);
        }
      }
    );
  };
  const selectVideoFromLibrary = () => {
    launchImageLibrary(
      { mediaType: "video", selectionLimit: 1 }, // กำหนด selectionLimit
      (response: any) => {
        if (!response.didCancel) {
          let newVideoUris: string[] = [];
          let newVideoFileNames: string[] = [];
          let newVideoTypeNames: string[] = [];
          // console.log(response);

          if (response.assets) {
            response.assets.forEach((asset: any) => {
              newVideoUris.push(asset.uri);
              newVideoFileNames.push(asset.fileName);
              newVideoTypeNames.push(asset.type);
            });
          } else if (response.uri) {
            newVideoUris.push(response.uri);
            newVideoFileNames.push(response.fileName);
            newVideoTypeNames.push(response.type);
          }

          // อัปเดต state ของวิดีโอ
          setVideoUris(newVideoUris);
          setVideoFileNames(newVideoFileNames);
          setVideoTypeNames(newVideoTypeNames);
        }
      }
    );
  };
  const deleteImage = (index: number) => {
    const updatedImageUris = [...imageUris];
    updatedImageUris.splice(index, 1);
    setImageUris(updatedImageUris);
  };
  const deleteVideo = (index: number) => {
    const updatedVideoUris = [...videoUris];
    updatedVideoUris.splice(index, 1);
    setVideoUris(updatedVideoUris);
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const convertVideoToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting video to Base64: ", error);
      return "";
    }
  };
  const handleDeleteFile_ = (image: any) => {
    console.log(image);

    if (!image) return; // ตรวจสอบว่า image ไม่เป็น null หรือ undefined

    Alert.alert(
      "ลบรูปภาพ",
      "คุณแน่ใจว่าต้องการลบรูปภาพนี้หรือไม่?",
      [
        { text: "ยกเลิก", style: "cancel" },
        {
          text: "ลบ",
          onPress: () => {
            if (Array.isArray(isImgContents)) {
              // ลบรูปภาพออกจาก state
              const updatedImages = isImgContents.filter(
                (item: any) => item.id !== image.id
              );

              // อัปเดต state ของรูปภาพที่เหลือ
              setImgContents(updatedImages);

              // อัปเดตรายการไฟล์ที่ต้องลบ
              setDeleteFileIds((prev: string[]) => [...prev, image.id]);

              if (updatedImages.length === 0) {
                console.log("ไม่มีรูปภาพที่เหลืออยู่");
              }
            } else {
              console.error("imageContents ไม่ใช่อาร์เรย์");
            }
          },
        },
      ],
      { cancelable: true }
    );
  };
  const handleDeleteVideo_ = (video: any) => {
    if (!video) return; // ตรวจสอบว่า image ไม่เป็น null หรือ undefined

    Alert.alert(
      "ลบวีดีโอ",
      "คุณแน่ใจว่าต้องการลบวีดีโอนี้หรือไม่?",
      [
        { text: "ยกเลิก", style: "cancel" },
        {
          text: "ลบ",
          onPress: () => {
            if (Array.isArray(videoContents)) {
              // ลบรูปภาพออกจาก state
              const updatedVideo = videoContents.filter(
                (item: any) => item.id !== video.id
              );

              // อัปเดต state ของรูปภาพที่เหลือ
              setVideoContents(updatedVideo);

              // อัปเดตรายการไฟล์ที่ต้องลบ
              setDeleteFileIds((prev: string[]) => [...prev, video.id]);

              if (updatedVideo.length === 0) {
                console.log("ไม่มีรูปภาพที่เหลืออยู่");
              }
            } else {
              console.error("imageContents ไม่ใช่อาร์เรย์");
            }
          },
        },
      ],
      { cancelable: true }
    );
  };
  const handleDeleteFile = () => {
    if (!image) return; // ตรวจสอบว่า image ไม่เป็น null หรือ undefined

    if (Array.isArray(isImgContents)) {
      // ลบรูปภาพออกจาก state
      const updatedImages = isImgContents.filter(
        (item: any) => item.id !== image.id
      );

      // อัปเดต state ของรูปภาพที่เหลือ
      setImgContents(updatedImages);

      // อัปเดตรายการไฟล์ที่ต้องลบ
      setDeleteFileIds((prev: string[]) => [...prev, image.id]);
      setModalDelete(false);

      if (updatedImages.length === 0) {
        console.log("ไม่มีรูปภาพที่เหลืออยู่");
      }
    } else {
      console.error("imageContents ไม่ใช่อาร์เรย์");
    }
  };
  const handleDeleteVideo = () => {
    if (!video) return; // ตรวจสอบว่า image ไม่เป็น null หรือ undefined

    if (Array.isArray(videoContents)) {
      // ลบรูปภาพออกจาก state
      const updatedVideo = videoContents.filter(
        (item: any) => item.id !== video.id
      );

      // อัปเดต state ของรูปภาพที่เหลือ
      setVideoContents(updatedVideo);

      // อัปเดตรายการไฟล์ที่ต้องลบ
      setDeleteFileIds((prev: string[]) => [...prev, video.id]);
      setModalDeleteVideo(false);

      if (updatedVideo.length === 0) {
        console.log("ไม่มีรูปภาพที่เหลืออยู่");
      }
    } else {
      console.error("imageContents ไม่ใช่อาร์เรย์");
    }
  };
  const onOpenDelete = (image: any) => {
    setModalDelete(true);
    setImage(image);
  };
  const onOpenDeleteVideo = (video: any) => {
    setModalDeleteVideo(true);
    setVideo(video);
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      placement="left"
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      rightComponent={rightHeader()}
      centerComponent={centerEdit()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => navigation.goBack()}>
      {goBack_gay()}
    </TouchableOpacity>
  );
  const rightHeader = () => {
    return (
      <>
        {/* iconCamera */}
        {isLoadIng ? (
          LoadingBar()
        ) : (
          <View
            style={{
              flexDirection: "row",
              marginTop: moderateScale(10),
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              style={{ paddingHorizontal: moderateScale(10) }}
              onPress={() => openCamera()}
            >
              {iconCameraEdit()}
            </TouchableOpacity>

            <TouchableOpacity
              style={{ paddingHorizontal: moderateScale(10) }}
              onPress={() => selectImageFromLibrary()}
            >
              {iconGalleryEdit()}
            </TouchableOpacity>

            <TouchableOpacity
              style={{ paddingHorizontal: moderateScale(10) }}
              onPress={() => selectVideoFromLibrary()}
            >
              {iconVideEdit()}
            </TouchableOpacity>

            <TouchableOpacity
              style={{ paddingHorizontal: moderateScale(10) }}
              onPress={() => onEditPost()}
            >
              {iconSavePost()}
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };
  const centerEdit = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("editPost")}
    </Text>
  );
  const renderVertical = () => {
    return (
      <>
        {isLoadIng && (
          <>
            <View style={{ width: "100%" }}>
              <Progress.Bar
                progress={progress / 100}
                width={null}
                style={{ width: "100%" }}
                color={BgColor.Bg_6A938D}
                borderRadius={0}
                borderWidth={0}
              />
            </View>
          </>
        )}

        <View style={oth.card_Vartical}>
          <View style={{ flexDirection: "row", padding: moderateScale(10) }}>
            <View style={ctn.ctn_profileVtcal}>
              <MyImageComponent imageUrl={isProfile} style={img.img_profile} />
            </View>
            <View style={ctn.ctn_TxtProfile}>
              <Text
                style={[txt.txt_Vtcal, fonstStyle.f14_bold, txt.txt_606060]}
              >
                {isFirstNames}
              </Text>
              <Text
                style={[txt.txt_time, fonstStyle.f12_light, txt.txt_606060]}
              >
                {moment(isDate).locale(t("locale")).fromNow()}
              </Text>
            </View>
          </View>

          <TextInput
            style={[
              txt.txt_cardPost,
              fonstStyle.f12_light,
              txt.txt_606060,
              { paddingHorizontal: moderateScale(10) },
            ]}
            onChangeText={setEditMessage}
            ref={inputRef}
            multiline
            placeholder={t("post_something")}
          >
            <Text
              style={[txt.txt_cardPost, fonstStyle.f12_light, txt.txt_606060]}
            >
              {isMessage}
            </Text>
          </TextInput>
          <View style={{ margin: moderateScale(5) }} />

          <ScrollView
            horizontal
            scrollEnabled
            nestedScrollEnabled
            showsHorizontalScrollIndicator={false}
          >
            {imageUris.map((item: any, index: number) => (
              <View key={index}>{renderImgPost({ item, index })}</View>
            ))}
            <View style={{ margin: moderateScale(2) }} />
          </ScrollView>

          <ScrollView
            horizontal
            scrollEnabled
            nestedScrollEnabled
            showsHorizontalScrollIndicator={false}
          >
            {videoUris.map((item: any, index: number) => (
              <View key={index}>{renderVideoPost({ item, index })}</View>
            ))}
            <View style={{ margin: moderateScale(2) }} />
          </ScrollView>

          {isImgContents.map((image: any) => (
            <View key={image.id}>
              <View style={ctn.ctn_editDelete}>
                <TouchableOpacity
                  style={oth.cardEditDelete}
                  // onPress={() => handleDeleteFile(image)}
                  onPress={() => onOpenDelete(image)}
                >
                  {iconEditImg()}
                </TouchableOpacity>
              </View>

              <TouchableOpacity style={{ position: "absolute" }}>
                {/* Icon ลบ */}
              </TouchableOpacity>

              <MyImageComponent
                imageUrl={image.url}
                style={[
                  img.img_imagePost,
                  imageDimensions && {
                    width: imageDimensions.width,
                    height: imageDimensions.height,
                  },
                ]}
              />
            </View>
          ))}

          {videoContents.map((video: any) => (
            <>
              <View style={ctn.ctn_editDelete}>
                <TouchableOpacity
                  style={oth.cardEditDelete}
                  onPress={() => onOpenDeleteVideo(video)}
                >
                  {iconEditImg()}
                </TouchableOpacity>
              </View>

              {/* <View style={img.vid_vidEdit}> */}
              <Video
                source={{ uri: video.url }}
                style={img.vid_vidEdit}
                resizeMode="cover"
                bufferConfig={{
                  minBufferMs: 3000,
                  maxBufferMs: 10000,
                  bufferForPlaybackMs: 1500,
                  bufferForPlaybackAfterRebufferMs: 3000,
                }}
                disableFocus={true} // ลดการใช้ CPU โดยไม่โฟกัส Player
                disableDisconnectError={true} // ปิด Error ถ้า Player หยุดโดยไม่มี buffer
              />
              {/* </View> */}
            </>
          ))}
        </View>
      </>
    );
  };
  const renderImgPost = ({ item, index }: any) => {
    return (
      <View style={{ margin: moderateScale(2) }}>
        <TouchableOpacity
          onPress={() => deleteImage(index)}
          style={ctn.ctn_imgPosting}
        >
          {iconDelete()}
        </TouchableOpacity>

        <FastImage
          style={img.img_posting}
          source={{ uri: item }}
          resizeMode={FastImage.resizeMode.cover}
          onLoadStart={() => setLoadIng(true)}
          onLoadEnd={() => setLoadIng(false)}
        />
      </View>
    );
  };
  const renderVideoPost = ({ item, index }: any) => {
    return (
      <View style={{ margin: moderateScale(2) }}>
        <TouchableOpacity
          onPress={() => deleteVideo(index)}
          style={ctn.ctn_vidPosting}
        >
          {iconDelete()}
        </TouchableOpacity>
        <Video
          source={{ uri: item }}
          style={img.vid_posting}
          resizeMode="cover"
          controls={true}
          muted
        />
      </View>
    );
  };
  const alertReject = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDelete}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("Confirm_delete_photo")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalDelete(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                onPress={() => handleDeleteFile()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertRejectVideo = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDeleteVideo}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("Confirm delete_video")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalDeleteVideo(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                onPress={() => handleDeleteVideo()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continue}>
          <ScrollView
            contentContainerStyle={{ backgroundColor: BgColor.Bg_FFFFFF }}
          >
            {renderVertical()}
            {alertReject()}
            {alertRejectVideo()}
          </ScrollView>
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <View style={ctn.continue}>
          <ScrollView
            contentContainerStyle={{ backgroundColor: BgColor.Bg_FFFFFF }}
          >
            {renderVertical()}
            {alertReject()}
            {alertRejectVideo()}
          </ScrollView>
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
