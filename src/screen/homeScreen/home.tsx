import {
  Text,
  View,
  Image,
  Alert,
  Animated,
  Platform,
  FlatList,
  TextInput,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import RNFS from "react-native-fs";
import Share from "react-native-share";
import Video from "react-native-video";
import FastImage from "react-native-fast-image";
import * as Progress from "react-native-progress";
import { HubConnection } from "@microsoft/signalr";
import { Header as HeaderRNE } from "@rneui/themed";
import { useIsFocused } from "@react-navigation/native";
import { scale, moderateScale } from "react-native-size-matters";
import React, { useRef, useState, useEffect, useCallback, useMemo } from "react";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconNext,
  iconVideo,
  iconCamera,
  iconDelete,
  iconGallery,
} from "../../assets/svg/svg_other";
//commponents
import Images from "../../utils/imageManager";
import PostItem from "../../components/PostItem";
import { useAuthTokens } from "../../hooks/useAuthTokens";
import { useProfileData } from "../../hooks/useProfileData";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import LoadingBar from "../../components/loading/loadingBar";
import { bottomTabEventEmitter } from "../../utils/eventEmitter";
import { setupNetworkListener } from "../../utils/networkManager";
import { refreshAuthTokenIfNeeded } from "../../utils/authManager";
import { setupSignalRConnection } from "../../utils/signalRManager";
import { MyImageComponent } from "../../components/cacheFiles/cache";

//Translation
import { useTranslation } from "../../screen/i18n";
//Api
import {
  shareSocial,
  newPostApi,
  deletePostApi,
} from "../../action/Mefarm_Social_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setDocListPost,
  updatePostLikes,
  updateMemberFollowStatus,
} from "../../Redux_Store/action";

export default function Home({ navigation, route }: any) {
  const routeParams = route?.params || {};
  const postId = routeParams.postId;
  const orientation = useOrientation();
  //State
  const { t } = useTranslation();
  const isFocused = useIsFocused();
  //Null
  const scrollRef = useRef<any>(null);
  const connectionRef = useRef<any>(null);
  const [isImgProfile, setImgProfile] = useState<any>(null);
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [imageDimensions, setImageDimensions] = useState<any>(null);
  const [connection, setConnection] = useState<null | HubConnection>(null);
  //Array
  const [imageUris, setImageUris] = useState<any>([]);
  const [fileNames, setFileNames] = useState<any>([]);
  const [typeNames, setTypeNames] = useState<any>([]);
  const [videoUris, setVideoUris] = useState<any>([]);
  const [videoFileNames, setVideoFileNames] = useState<any>([]);
  const [videoTypeNames, setVideoTypeNames] = useState<any>([]);
  //String
  const [isPost, setPost] = useState<string>("");
  //True & False
  const [showHeader, setShowHeader] = useState(true);
  const [isMuted, setIsMuted] = useState<boolean>(true);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isUserScrolled, setIsUserScrolled] = useState<boolean>(false);
  const [isLoadLikes, setLoadLikes] = useState<{ [key: number]: boolean }>({});
  const [isLoadFollows, setLoadFollows] = useState<{ [key: number]: boolean }>(
    {}
  );
  const [isLoadShare, setLoadShare] = useState<{ [key: number]: boolean }>({});
  const [videoLoading, setVideoLoading] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [imageLoading, setImageLoading] = useState<{ [key: string]: boolean }>(
    {}
  );
  //String
  const lastOffsetY = useRef(0);
  //Number
  const NUM_OF_LINES = 5;
  const [progress, setProgress] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(1);
  const scrollY = useRef(new Animated.Value(0)).current;
  //Dispatch
  const dispatch = useDispatch();
  const docListPost = useSelector((state: any) => state.docListPost);
  const docListFollow = useSelector((state: any) => state.docListFollow);
  const [isShowFullText, setIsShowFullText] = useState<boolean[]>(
    Array.isArray(docListPost) && docListPost.length > 0
      ? docListPost.map(() => false)
      : []
  );
  const { accessToken, userId } = useAuthTokens();
  const { fetchProfileData } = useProfileData();
  const hideBottomTab = () => {
    bottomTabEventEmitter.emit("bottomTabVisibility", { visible: false });
  };
  const showBottomTab = () => {
    bottomTabEventEmitter.emit("bottomTabVisibility", { visible: true });
  };
  const { width } = Dimensions.get("window");

  useEffect(() => {
    if (postId && docListPost && docListPost.length > 0) {
      const index = docListPost.findIndex((item: any) => item.id === postId);
      if (index !== -1 && scrollRef.current) {
        scrollRef.current.scrollToIndex({ index, animated: true });
        goDetailPost(docListPost[index], "seePost");
      }
      navigation.setParams({ postId: undefined });
    }
  }, [postId, docListPost]);

  useEffect(() => {
    fetchProfile();
    requestCameraPermission();
    refreshAuthTokenIfNeeded();
    unsubscribe();
    listener();
  }, [navigation]);
  useEffect(() => {
    let signalRCleanup: (() => void) | null = null;

    const initConnection = async () => {
      try {
        const result = await setupSignalRConnection(
          dispatch,
          (newConnection) => {
            setConnection(newConnection);
            connectionRef.current = newConnection;
            joinRoom();
          },
          { page: "home" }
        );

        signalRCleanup = result.cleanup;
      } catch (error) {
        console.error("Error setting up SignalR connection:", error);
      }
    };
    if (isFocused) {
      initConnection();
    }
    return () => {
      if (signalRCleanup) {
        console.log("Cleaning up SignalR connection on home screen unmount");
        signalRCleanup();
      }
    };
  }, [dispatch]);
  const joinRoom = async () => {
    if (!connection || connection.state !== "Connected") return;

    const joinData = {
      roomId: "dashboard",
      followerUserId: userId,
      accessToken: accessToken,
      followingUserId: null,
      isDescending: true,
      firstPageSize: pageSize,
      sendType: "normal",
    };
    try {
      await connection.invoke("JoinRoom", joinData);
    } catch (err) {
      console.error("Error invoking JoinRoom:", err);
    } finally {
    }
  };
  useEffect(() => {
    const unsubscribe = setupNetworkListener(
      isFocused,
      connection,
      joinRoom,
      navigation
    );

    return () => {
      unsubscribe();
    };
  }, [isFocused, connection]);
  const fetchProfile = useCallback(async () => {
    try {
      setLoadIng(true);
      const result = await fetchProfileData();
      if (result.success && result.data) {
        setImgProfile(result.data.profileImageUrl);
      }
    } catch (error) {
      console.log("err", error);
    } finally {
      setLoadIng(false);
    }
  }, [fetchProfileData]);
  const scrollToTop = () => {
    scrollRef.current?.scrollToOffset({ offset: 0, animated: true });
    setTimeout(() => {
      bottomTabEventEmitter.emit("bottomTabVisibility", {
        visible: true,
        headerVisible: true,
      });
      setShowHeader(true);
      setPageSize(1);
      lastOffsetY.current = 0;
    }, 100);
  };
  const unsubscribe = () => {
    const unsubscribe = navigation.addListener("tabPress", (e: any) => {
      scrollToTop();
    });

    return unsubscribe;
  };
  const listener = () => {
    const listener = scrollY.addListener(({ value }) => {
      setShowHeader(value < 100);
    });

    return () => scrollY.removeListener(listener);
  };
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        // Alert.alert("❌ Camera permission denied");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  const toggleMenu = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const deleteImage = (index: number) => {
    const updatedImageUris = [...imageUris];
    updatedImageUris.splice(index, 1);
    setImageUris(updatedImageUris);
  };
  const deleteVideo = (index: number) => {
    const updatedVideoUris = [...videoUris];
    updatedVideoUris.splice(index, 1);
    setVideoUris(updatedVideoUris);
  };

  //Libary base 64
  const openCamera = () => {
    const options: any = {
      mediaType: "photo",
      saveToPhotos: true,
      quality: 1,
    };

    launchCamera(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);
      }
    });
  };
  const selectImageFromLibrary = () => {
    launchImageLibrary(
      { mediaType: "photo", selectionLimit: 10 },
      (response: any) => {
        // console.log("response: ", response);
        if (!response.didCancel) {
          let newImageUris: string[] = [];
          let newFileNames: string[] = [];
          let newTypeNames: string[] = [];

          if (response.assets) {
            response.assets.forEach((asset: any) => {
              newImageUris.push(asset.uri);
              newFileNames.push(asset.fileName);
              newTypeNames.push(asset.type);
            });
          } else if (response.uri) {
            newImageUris.push(response.uri);
            newFileNames.push(response.fileName);
            newTypeNames.push(response.type);
          }
          setImageUris((prev: any) => [...prev, ...newImageUris]);
          setFileNames((prev: any) => [...prev, ...newFileNames]);
          setTypeNames((prev: any) => [...prev, ...newTypeNames]);
        }
      }
    );
  };
  const selectVideoFromLibrary = () => {
    launchImageLibrary(
      { mediaType: "video", selectionLimit: 1 },
      (response: any) => {
        if (!response.didCancel) {
          let newVideoUris: string[] = [];
          let newVideoFileNames: string[] = [];
          let newVideoTypeNames: string[] = [];
          // console.log(response);

          if (response.assets) {
            response.assets.forEach((asset: any) => {
              newVideoUris.push(asset.uri);
              newVideoFileNames.push(asset.fileName);
              newVideoTypeNames.push(asset.type);
            });
          } else if (response.uri) {
            newVideoUris.push(response.uri);
            newVideoFileNames.push(response.fileName);
            newVideoTypeNames.push(response.type);
          }

          // ปเดต state ของ
          setVideoUris(newVideoUris);
          setVideoFileNames(newVideoFileNames);
          setVideoTypeNames(newVideoTypeNames);
        }
      }
    );
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const convertVideoToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting video to Base64: ", error);
      return "";
    }
  };

  //On
  const onPostSocial = async () => {
    setImageUris([]);
    setVideoUris([]);
    setPost("");
    setProgress(0);
    try {
      setLoadIng(true);

      const totalSteps = imageUris.length + videoUris.length + 2;
      let currentStep = 0;

      const updateProgress = () => {
        currentStep++;
        const percent = Math.round((currentStep / totalSteps) * 100);
        setProgress(percent);
      };

      const imageContent = await Promise.all(
        imageUris.map(async (uri: any, index: any) => {
          const base64Data = await convertImageToBase64(uri);
          updateProgress();
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );

      const videoContent = await Promise.all(
        videoUris.map(async (uri: any, index: any) => {
          const base64Data = await convertVideoToBase64(uri);
          updateProgress();
          return {
            fileName: videoFileNames[index] || "",
            contentType: videoTypeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );

      updateProgress();

      // Construct the request object
      const req = {
        id: userId,
        messageContent: isPost || "",
        imageContent: imageContent || "",
        videoContent: videoContent || "",
      };

      // Call the API to send the post
      const res = await newPostApi(req);
      updateProgress();
      // console.log("Response: ", res);
    } catch (error) {
      console.error("Error during post social: ", error);
    } finally {
      setLoadIng(false);
    }
  };
  const onDelete = async (item: any, indexItem: number) => {
    try {
      setLoadIng(true);
      const socialPostId = item.id || "";
      let data = await deletePostApi(socialPostId);

      if (data.success) {
        // let deleteData = isDocListPost;
        let deleteData = docListPost;
        if (data.model.id == deleteData[indexItem].id) {
          deleteData.splice(indexItem, 1);
        }

        setDocListPost(deleteData);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setOpenIndex(null);
      joinRoom();
    }
  };
  const onShare = async (item: any, index: number) => {
    try {
      setLoadShare((prevState) => ({ ...prevState, [index]: true }));
      // console.log(JSON.stringify(item, null, 2));

      const req = await shareSocial(item.id);
      const data = req.model.sharedUrl || "";
      // console.log(req);
      const result = await Share.open({
        title: "Mefarm",
        message: "",
        url: data,
      });
      console.log("แชร์สำเร็จ:", result);
    } catch (error: any) {
      if (error.message !== "User did not share") {
        Alert.alert("แชร์ไม่สำเร็จ", error.message);
      }
    } finally {
      joinRoom();
      setLoadShare((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const loadMoreData = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 5);
    await joinRoom();
    setTimeout(() => {
      setLoadPosts(false);
    }, 1000);
  };
  const onRefreshHandler = useCallback(async () => {
    setLoadPosts(true);
    setIsUserScrolled(false);
    await joinRoom();
    setTimeout(() => {
      setLoadPosts(false);
    }, 1000);
  }, []);

  //Go to
  const goComment = (item: any) => {
    navigation.navigate("DetailPost", {
      docList: item,
    });
  };
  const goDetailPost = (item: any, seePost: string) => {
    navigation.navigate("DetailPost", {
      docList: item,
      seePost: seePost,
    });
  };
  const goLike = async (item: any, index: number) => {
    try {
      setLoadLikes((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("LikePostMessage", item.id);
        dispatch(updatePostLikes(item.id));
      }
    } catch (error) {
      console.log(error);
    } finally {
      joinRoom();
      setLoadLikes((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goFllowerUser = async (item: any, index: number) => {
    try {
      setLoadFollows((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("FollowMemberMessage", item.userId);
        dispatch(updateMemberFollowStatus(item.userId));
      }
    } catch (error) {
      console.error("An error occurred in goFllowerUser:", error);
    } finally {
      joinRoom();
      setLoadFollows((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goEditPost = (item: any) => {
    navigation.navigate("EditPost", {
      docList: item,
    });
    setOpenIndex(null);
  };
  const goFriend = (item: any) => {
    navigation.navigate("Profilefriend", { item: item });
  };

  //FlasList
  const FlasListImgPost = () => (
    <FlatList
      data={imageUris}
      horizontal={true}
      nestedScrollEnabled={true}
      keyExtractor={(item, index) => index.toString()}
      renderItem={renderImgPost}
      showsHorizontalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={5}
      windowSize={8}
      initialNumToRender={3}
      updateCellsBatchingPeriod={50}
    />
  );
  const FlasListVideoPost = () => (
    <FlatList
      data={videoUris}
      horizontal={true}
      nestedScrollEnabled={true}
      keyExtractor={(item, index) => index.toString()}
      renderItem={renderVideoPost}
      showsHorizontalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={2}
      windowSize={5}
      initialNumToRender={1}
      updateCellsBatchingPeriod={50}
    />
  );

  //Ui
  const contentPost = () => (
    <>
      {isLoadIng && (
        <>
          <View style={{ width: "100%" }}>
            <Progress.Bar
              progress={progress / 100}
              width={null}
              style={{ width: "100%" }}
              color={BgColor.Bg_6A938D}
              borderRadius={0}
              borderWidth={0}
            />
          </View>
        </>
      )}
      <View style={oth.card_ctp}>
        <View style={{ flexDirection: "row" }}>
          <TouchableOpacity
            onPress={() => navigation.navigate("ProfileUser")}
            style={{ padding: moderateScale(10) }}
          >
            <View style={img.img_profile}>
              <MyImageComponent
                imageUrl={isImgProfile}
                style={img.img_profile}
              />
            </View>
          </TouchableOpacity>
          <TextInput
            style={[txt.txt_inputPost, fonstStyle.f12_light, txt.txt_606060]}
            placeholder={t("post_something")}
            onChangeText={setPost}
            value={isPost}
            editable
            multiline
          />
        </View>
        <View style={{ margin: scale(5) }} />
        {FlasListImgPost()}
        {FlasListVideoPost()}
        <View style={ctn.ctn_selectPost}>
          <View style={ctn.ctn_space}>
            <TouchableOpacity
              style={btn.btn_selectPost}
              onPress={() => openCamera()}
            >
              {iconCamera()}
            </TouchableOpacity>
            <View style={{ margin: scale(2) }} />
            <TouchableOpacity
              style={btn.btn_selectPost}
              onPress={() => selectImageFromLibrary()}
            >
              {iconGallery()}
            </TouchableOpacity>
            <View style={{ margin: scale(2) }} />
            <TouchableOpacity
              style={videoUris.length >= 1 ? btn.btn_selectPostDis : btn.btn_selectPost}
              disabled={videoUris.length >= 1}
              onPress={() => selectVideoFromLibrary()}
            >
              {iconVideo()}
            </TouchableOpacity>
          </View>
          {isPost != "" || imageUris != "" || videoUris != "" ? (
            <TouchableOpacity
              style={btn.btn_Post}
              onPress={() => onPostSocial()}
            >
              <Text style={[txt.txt_post, fonstStyle.f12_bold]}>
                {t("postNews")}
              </Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </>
  );
  // Memoized render functions for better performance
  const renderFollowItem = useCallback(({ item, index }: any) => (
    <TouchableOpacity
      key={index.toString()}
      style={oth.care_HorizonTal}
      onPress={() => goFriend(item)}
    >
      <View
        style={
          item?.coverImageUrl == null ? img.img_coverNon : img.img_cover
        }
      >
        <MyImageComponent
          imageUrl={item?.coverImageUrl}
          style={
            item?.coverImageUrl == null ? img.img_coverNon : img.img_cover
          }
        />
      </View>
      <View style={ctn.ctn_profileHrzt}>
        <MyImageComponent
          imageUrl={item?.profileImageUrl}
          style={img.img_profileFollow}
        />
      </View>
      <TouchableOpacity onPress={() => goFriend(item)}>
        <Text
          numberOfLines={1}
          style={[txt.txt_Hrzt, fonstStyle.f12_bold, txt.txt_606060]}
        >
          {item?.firstName ? item?.firstName : "No Name"}
        </Text>
      </TouchableOpacity>
      <Text
        style={[txt.txt_FollowHrzt, fonstStyle.f10_light, txt.txt_606060]}
      >
        {t("followers")} {item?.numberOfFollower}
      </Text>
      <TouchableOpacity
        style={
          item?.isFollower == true
            ? btn.btn_UnFollowHrzt
            : btn.btn_FollowHrzt
        }
        onPress={() => goFllowerUser(item, index)}
      >
        <Text
          style={[
            item?.isFollower == true ? txt.txt_UnBtnHrzt : txt.txt_BtnHrzt,
            fonstStyle.f10_medium,
          ]}
        >
          {isLoadFollows[index] ? (
            <ActivityIndicator size="small" color={BgColor.Bg_FFFFFF} />
          ) : (
            <>{item?.isFollower == true ? t("unfollow") : t("follows")}</>
          )}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  ), [isLoadFollows, t]);

  const FlasListHorizonTal = () => (
    <FlatList
      data={docListFollow}
      horizontal
      keyExtractor={(item, index) => item.id?.toString() || index.toString()}
      renderItem={renderFollowItem}
      ListFooterComponent={
        <View style={{ padding: moderateScale(5) }}>
          <View style={ctn.ctn_horizontalBottom}>
            <TouchableOpacity
              onPress={() => navigation.navigate("Contact")}
              style={{ padding: moderateScale(15) }}
            >
              {iconNext()}
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f12_light, txt.txt_horizontalPlus]}>
                {t("see_all")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      }
      showsHorizontalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={5}
      windowSize={8}
      initialNumToRender={3}
      updateCellsBatchingPeriod={50}
    />
  );
  const renderImgPost = ({ item, index }: any) => {
    return (
      <View style={{ margin: moderateScale(2) }}>
        <TouchableOpacity
          onPress={() => deleteImage(index)}
          style={ctn.ctn_imgPosting}
        >
          {iconDelete()}
        </TouchableOpacity>

        <View style={{ position: "relative" }}>
          {imageLoading[index] && (
            <View style={oth.loadingPostmedai}>
              <ActivityIndicator size="large" color={BgColor.Bg_84B8A2} />
            </View>
          )}

          <FastImage
            style={img.img_posting}
            source={{ uri: item }}
            resizeMode={FastImage.resizeMode.cover}
            onLoadStart={() => {
              setImageLoading((prev) => ({ ...prev, [index]: true }));
            }}
            onLoad={() => {
              setImageLoading((prev) => ({ ...prev, [index]: false }));
            }}
            onError={() => {
              setImageLoading((prev) => ({ ...prev, [index]: false }));
            }}
          />
        </View>
      </View>
    );
  };
  const renderVideoPost = ({ item, index }: any) => {
    return (
      <View style={{ position: "relative" }}>
        <TouchableOpacity
          onPress={() => deleteVideo(index)}
          style={ctn.ctn_vidPosting}
        >
          {iconDelete()}
        </TouchableOpacity>
        {videoLoading[index] && (
          <View style={oth.loadingPostmedai}>
            <ActivityIndicator size="large" color={BgColor.Bg_84B8A2} />
          </View>
        )}
        <Video
          source={{ uri: item }}
          style={{ width: width, height: moderateScale(209) }}
          resizeMode="cover"
          controls={true}
          muted
          bufferConfig={{
            minBufferMs: 3000,
            maxBufferMs: 10000,
            bufferForPlaybackMs: 1500,
            bufferForPlaybackAfterRebufferMs: 3000,
          }}
          disableFocus={true}
          disableDisconnectError={true}
          onLoadStart={() => {
            setVideoLoading((prev) => ({ ...prev, [index]: true }));
          }}
          onLoad={() => {
            setVideoLoading((prev) => ({ ...prev, [index]: false }));
          }}
          onError={() => {
            setVideoLoading((prev) => ({ ...prev, [index]: false }));
          }}
        />
      </View>
      // </View>
    );
  };
  const header = () => {
    return (
      <HeaderRNE
        backgroundColor={BgColor.Bg_84B8A2}
        leftComponent={titleHeader()}
        rightComponent={headerRight()}
        backgroundImage={Images.bgApp}
      />
    );
  };
  const titleHeader = () => (
    <Image
      style={{ width: 100, height: 40 }}
      source={Images.textMefarm}
      resizeMode="contain"
    />
  );
  const headerRight = () => (
    <View style={{ position: "absolute" }}>
      {isLoadIng ? LoadingBar() : null}
    </View>
  );

  const renderPostItem = useCallback(({ item, index }: any) => (
    <PostItem
      item={item}
      index={index}
      isUserId={userId}
      isShowFullText={isShowFullText}
      toggleFullText={(idx) => {
        const newState = [...isShowFullText];
        newState[idx] = !newState[idx];
        setIsShowFullText(newState);
      }}
      openIndex={openIndex}
      toggleMenu={toggleMenu}
      isMuted={isMuted}
      setIsMuted={setIsMuted}
      isLoadLikes={isLoadLikes}
      isLoadShare={isLoadShare}
      setLoadIng={setLoadIng}
      imageDimensions={imageDimensions}
      setImageDimensions={setImageDimensions}
      NUM_OF_LINES={NUM_OF_LINES}
      t={t}
      goDetailPost={goDetailPost}
      goLike={goLike}
      goComment={goComment}
      onShare={onShare}
      goEditPost={goEditPost}
      onDelete={onDelete}
      goFriend={goFriend}
      navigation={navigation}
    />
  ), [
    userId, isShowFullText, openIndex, isMuted, isLoadLikes, isLoadShare,
    imageDimensions, NUM_OF_LINES, t, navigation
  ]);
  const getItemLayout = useCallback((data: any, index: number) => ({
    length: 400, // Estimated height of each post item
    offset: 400 * index,
    index,
  }), []);

  //Main
  const mainPortrait = () => {
    return (
      <FlatList
        ref={scrollRef}
        data={docListPost}
        keyExtractor={(item, index) => `post-${item.id || index}`}
        renderItem={renderPostItem}
        getItemLayout={getItemLayout}
        ListHeaderComponent={
          <>
            {header()}
            {contentPost()}
            {FlasListHorizonTal()}
          </>
        }
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          <>
            <View style={{ margin: moderateScale(20) }} />
            {isLoadPosts ? LoadingApp() : null}
            <View style={{ margin: moderateScale(120) }} />
          </>
        }
        refreshing={isLoadPosts}
        onRefresh={onRefreshHandler}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.2}
        onScrollBeginDrag={hideBottomTab}
        onMomentumScrollEnd={showBottomTab}
        onScrollEndDrag={showBottomTab}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        updateCellsBatchingPeriod={50}
      />
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <FlatList
          ref={scrollRef}
          data={docListPost}
          keyExtractor={(item, index) => `post-${item.id || index}`}
          renderItem={renderPostItem}
          getItemLayout={getItemLayout}
          ListHeaderComponent={
            <>
              {contentPost()}
              {FlasListHorizonTal()}
            </>
          }
          showsVerticalScrollIndicator={false}
          ListFooterComponent={
            <>
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts ? LoadingApp() : null}
              <View style={{ margin: moderateScale(120) }} />
            </>
          }
          refreshing={isLoadPosts}
          onRefresh={onRefreshHandler}
          onEndReached={loadMoreData}
          onEndReachedThreshold={0.2}
          onScrollBeginDrag={hideBottomTab}
          onMomentumScrollEnd={showBottomTab}
          onScrollEndDrag={showBottomTab}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={5}
          updateCellsBatchingPeriod={50}
        />
      </SafeAreaView>
    );
  };

  return <>{orientation === "portrait" ? mainPortrait() : mainlandscape()}</>;
}
