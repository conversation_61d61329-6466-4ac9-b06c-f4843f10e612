import {
  Text,
  View,
  StatusBar,
  ScrollView,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { Header as HeaderRNE } from "@rneui/themed";
import NetInfo from "@react-native-community/netinfo";
import { useIsFocused } from "@react-navigation/native";
import React, { useRef, useState, useEffect, useCallback } from "react";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import {
  LogLevel,
  HubConnection,
  HubConnectionBuilder,
} from "@microsoft/signalr";
import { moderateScale } from "react-native-size-matters";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//commponents
import { useAuthTokens } from "../../hooks/useAuthTokens";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import { setupNetworkListener } from "../../utils/networkManager";
import {
  joinSignalRRoom,
  setupSignalRConnection,
} from "../../utils/signalRManager";
//Svg
import { goBack_gay } from "../../assets/svg/svg_naviagte";
//Translation
import { useTranslation } from "../i18n";
//Api
import { WEBSOCKET_ROOT } from "../../constants/api";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setDocListFollow,
  updateMemberFollowStatus,
} from "../../Redux_Store/action";

export default function Contact({ navigation }: any) {
  const orientation = useOrientation();
  const { accessToken, userId } = useAuthTokens();
  const { t } = useTranslation();
  const isFocused = useIsFocused();
  //Null
  const connectionRef = useRef<any>(null);
  const [connection, setConnection] = useState<null | HubConnection>(null);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isLoadFollows, setLoadFollows] = useState<{ [key: number]: boolean }>(
    {}
  );
  //Number
  const [pageSize, setPageSize] = useState<number>(10);
  //Dispatch
  const dispatch = useDispatch();
  const docListFollow = useSelector((state: any) => state.docListFollow);

  useEffect(() => {
    let cleanup: (() => void) | null = null;

    const initConnection = async () => {
      try {
        const result = await setupSignalRConnection(
          dispatch,
          (newConnection) => {
            // เมื่อเชื่อมต่อสำเร็จ
            setConnection(newConnection);
            connectionRef.current = newConnection;

            // เข้าร่วมห้อง
            joinRoom();
          }
        );

        cleanup = result.cleanup;
      } catch (error) {
        console.error("Error setting up SignalR connection:", error);
      }
    };

    initConnection();

    // ทำความสะอาดเมื่อ component unmount
    return () => {
      if (cleanup) cleanup();
    };
  }, [dispatch]);
  const joinRoom = async () => {
    if (!connection || connection.state !== "Connected") return;

    const joinData = {
      roomId: "dashboard",
      followerUserId: userId,
      accessToken: accessToken,
      followingUserId: null,
      isDescending: true,
      firstPageSize: pageSize,
      sendType: "normal",
    };
    try {
      setLoadIng(true);
      await connection.invoke("JoinRoom", joinData);
    } catch (err) {
      console.error("Error invoking JoinRoom:", err);
    } finally {
      setLoadIng(false);
    }
  };
  useEffect(() => {
    const unsubscribe = setupNetworkListener(
      isFocused,
      connection,
      joinRoom,
      navigation
    );

    return () => {
      unsubscribe();
      // console.log("Network listener cleaned up.");
    };
  }, [isFocused, connection]);

  //Go Next
  const goFllowerUser = async (item: any, index: number) => {
    try {
      setLoadFollows((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("FollowMemberMessage", item.userId);
        dispatch(updateMemberFollowStatus(item.userId));
      }
    } catch (error) {
      console.error("An error occurred in goFllowerUser:", error);
    } finally {
      joinRoom();
      setLoadFollows((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goFriend = (item: any) => {
    navigation.navigate("Profilefriend", { item: item });
  };
  const loadMoreData = async () => {
    setLoadPosts(true);

    setPageSize((prev) => prev + 10);
    await joinRoom();
    setLoadPosts(false);
  };
  const onRefreshHandler = useCallback(async () => {
    setLoadPosts(true);
    setLoadIng(false);
    await joinRoom();
    setLoadPosts(false);
  }, []);

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={center()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => navigation.goBack()}>
      {goBack_gay()}
    </TouchableOpacity>
  );
  const center = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("friends")}
    </Text>
  );
  const renderFriends = ({ item, index }: any) => {
    return (
      <>
        <TouchableOpacity
          style={{
            padding: 20,
            flexDirection: "row",
            alignItems: "flex-end",
            justifyContent: "space-between",
            backgroundColor: BgColor.Bg_FFFFFF,
          }}
          onPress={() => goFriend(item)}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View style={ctn.ctn_profileVtcal}>
              <MyImageComponent
                imageUrl={item?.profileImageUrl}
                style={img.img_profile}
              />
            </View>
            <View style={{ flexDirection: "column" }}>
              <Text
                style={[txt.txt_Vtcal, fonstStyle.f12_bold, txt.txt_606060]}
                numberOfLines={5}
              >
                {item.firstName ? item.firstName : "No Name"}{" "}
              </Text>
              <Text
                style={[txt.txt_Vtcal, fonstStyle.f12_light, txt.txt_606060]}
              >
                {t("followers")} {item?.numberOfFollower}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={
              item.isFollower == true
                ? btn.btn_UnFollowHrztAll
                : btn.btn_FollowHrztAll
            }
            onPress={() => goFllowerUser(item, index)}
          >
            <Text
              style={[
                item.isFollower == true ? txt.txt_UnBtnHrzt : txt.txt_BtnHrzt,
                fonstStyle.f10_bold,
              ]}
            >
              {/* {item.isFollower == true ? t("unfollow") : t("follows")} */}
              {isLoadFollows[index] ? (
                <ActivityIndicator size="small" color={BgColor.Bg_FFFFFF} />
              ) : (
                <>{item?.isFollower == true ? t("unfollow") : t("follows")}</>
              )}
            </Text>
          </TouchableOpacity>
        </TouchableOpacity>
        <View style={oth.line_notiNon} />
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <ScrollView
          nestedScrollEnabled={true}
          style={[ctn.continue]}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            // Destructure nativeEvent immediately to avoid synthetic event pooling issues
            const nativeEvent = event.nativeEvent;
            const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={isLoadPosts}
              onRefresh={onRefreshHandler}
            />
          }
        >
          {docListFollow.map((item: any, index: number) => (
            <>{renderFriends({ item, index })}</>
          ))}
          <View style={{ margin: moderateScale(20) }} />
          {isLoadPosts ? LoadingApp() : null}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <ScrollView
          nestedScrollEnabled={true}
          style={[ctn.continue]}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            // Destructure nativeEvent immediately to avoid synthetic event pooling issues
            const nativeEvent = event.nativeEvent;
            const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={isLoadPosts}
              onRefresh={onRefreshHandler}
            />
          }
        >
          {docListFollow.map((item: any, index: number) => (
            <>{renderFriends({ item, index })}</>
          ))}
          <View style={{ margin: moderateScale(20) }} />
          {isLoadPosts ? LoadingApp() : null}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
