import React from "react";
import moment from "moment";
import DeviceInfo from "react-native-device-info";
import { moderateScale } from "react-native-size-matters";
import { Text, View, TouchableOpacity } from "react-native";
//Style Sheet
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import fonstStyle from "../../../styleSheet/style_Custom";
//Components
import SettingBar from "../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../i18n";

export default function Version({ navigation }: any) {
  const { t } = useTranslation();
  //DivceInfo
  const appVersion = DeviceInfo.getVersion(); // ดึง versionName (Android) หรือ CFBundleShortVersionString (iOS)
  const buildNumber = DeviceInfo.getBuildNumber(); // ดึง buildNumber

  const content = () => {
    return (
      <View
        style={{
          marginTop: moderateScale(10),
          paddingHorizontal: moderateScale(20),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>{t("application")}</Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_light, txt.txt_606060]}>Mefarm</Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>{t("version")}</Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_light, txt.txt_606060]}>{appVersion}</Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("last_updated")}
        </Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
          30 {t('June')} {moment().locale(t("locale")).format("YYYY")}
        </Text>
      </View>
    );
  };
  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("about_system")}
      />
      <View style={ctn.continueMain}>{content()}</View>
    </View>
  );
}
