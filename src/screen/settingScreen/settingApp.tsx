import { moderateScale } from "react-native-size-matters";
import { Text, View, SafeAreaView, TouchableOpacity } from "react-native";
//Style Sheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import txt from "../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconInfo,
  iconRight,
  iconUserEdit,
  iconSettingApp,
  iconMobileSystem,
} from "../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../hooks/useOrientation";
import SettingBar from "../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../i18n";

export default function SettingApp({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t, setLanguage } = useTranslation();

  //Ui
  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        {/* {แก้ไขข้อมูลบัญชี} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Menu_edit_account")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconUserEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Edit_account_information")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        {/* {บันทึกกิจกรรม} */}
        {/* <TouchableOpacity style={btn.btn_settingApp}>
          <View style={{flexDirection: 'row'}}>
            {iconFile()}
            <View style={{margin: moderateScale(5)}} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_light]}>{t('Activity_log')}</Text>
              <View style={{justifyContent: 'center'}}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{margin: moderateScale(5)}} /> */}

        {/* {การตั้งค่าระบบ} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("System")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconSettingApp()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("setting_system")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        {/* {เกี่ยวกับระบบ} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Version")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconMobileSystem()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("about_system")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        {/* {ความช่วยเหลือและการสนับสนุน} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("menu_help_support")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconInfo()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Help_and_support")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
      </View>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <View style={{ flex: 1 }}>
      <SettingBar onBack={() => navigation.goBack()} pageName={t("setting")} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
