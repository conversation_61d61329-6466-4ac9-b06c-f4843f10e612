import { moderateScale } from "react-native-size-matters";
import { Text, View, SafeAreaView, TouchableOpacity } from "react-native";
//Style Sheet
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../styleSheet/style_Custom";
//Svg
import {
  iconKey,
  iconFile,
  iconInfo,
  iconRight,
  iconUserEdit,
  iconSettingApp,
} from "../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../hooks/useOrientation";
import SettingBar from "../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../i18n";
export default function Menu_edit_account({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        {/* {แก้ไขข้อมูลบัญชี} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Change_password")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconKey()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("change_password")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        {/* {การตั้งค่าระบบ} */}
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Conected")}
        >
          <View style={{ flexDirection: "row" }}>
            {iconUserEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("connected_account")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
      </View>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("edit_account_information")}
      />
         {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
