import { MMKV } from "react-native-mmkv";
import FastImage from "react-native-fast-image";
import React, { useState, useEffect } from "react";
import { moderateScale } from "react-native-size-matters";
import {
  GoogleSignin,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { appleAuth } from "@invertase/react-native-apple-authentication";
import {
  Text,
  View,
  Switch,
  Alert,
  Platform,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
//Style Sheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import txt from "../../../../styleSheet/txt";
import img from "../../../../styleSheet/img";
import fonstStyle, {BgColor} from "../../../../styleSheet/style_Custom";
import {
  iconKey,
  iconFile,
  iconInfo,
  iconRight,
  iconUserEdit,
  iconSettingApp,
} from "../../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import Loading from "../../../../components/loading/loading";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";
//Api
import {
  logInApi,
  registerApi,
  logInThirdParty,
  registerThirdparty,
} from "../../../../action/Mefarm_Identity_API";
import { notiRegister } from "../../../../action/Mefarm_Realtime_API";
import Images from "../../../../utils/imageManager";

GoogleSignin.configure({
  webClientId:
    "1027814423505-99l0e5l9d6hbl8tku5gadpegct8kks84.apps.googleusercontent.com",
    
  offlineAccess: true,
});

export default function Conected({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  //True & false
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);

  const storage = new MMKV();

  //Login Facebook, Google & Apple
  const loginGoogle = async () => {
    try {
      setLoading(true);
      await GoogleSignin.hasPlayServices();
      const userInfoGoogle = await GoogleSignin.signIn();
      const idToken = userInfoGoogle.data?.idToken || "";
      const req = {
        provider: "Google",
        accessToken: idToken,
      };
      const obj = await logInThirdParty(req);
      await storage.set("successLogin", obj.message || "");
      await AsyncStorage.setItem(
        "userIdLogin",
        obj.model.userInfo.userId || ""
      );
      await AsyncStorage.setItem(
        "tokenLogin",
        obj.model.tokenInfo.access_token || ""
      );
      console.log(JSON.stringify(obj, null, 2));
      if (obj.message === "Login Successfully") {
        //Fcm register
        const token = await AsyncStorage.getItem("fcmToken");
        const req = {
          userId: "",
          deviceTokenId: token,
        };
        const rsp = await notiRegister(req);
        // console.log("???????....", rsp);
        navigation.navigate("Bottom_Tab");
      } else {
        Alert.alert(t("noti"), t("error"));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  const loginApple = async () => {
    try {
      setLoading(true);
      if (!appleAuth.isSupported) {
        Alert.alert(
          "Error",
          "Sign In with Apple is not supported on this device."
        );
        return;
      }
      const appleAuthResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      const idToken = appleAuthResponse.identityToken || "";

      const { identityToken, email, fullName, user } = appleAuthResponse;
      if (identityToken) {
        // Alert.alert('Success', `User ID: ${user}\nEmail: ${email}`);
        // console.log("Success", `User ID: ${user}\nEmail: ${email}`);
        try {
          const req = {
            provider: "Apple",
            accessToken: idToken,
          };
          const obj = await logInThirdParty(req);
          // await AsyncStorage.setItem("successLogin", obj.message || "");
          await storage.set("successLogin", obj.message || "");
          await AsyncStorage.setItem(
            "userIdLogin",
            obj.model.userInfo.userId || ""
          );
          await AsyncStorage.setItem(
            "tokenLogin",
            obj.model.tokenInfo.access_token || ""
          );
          console.log("User registered successfully:", obj);
          if (obj.message === "Login Successfully") {
            //Fcm register
            const token = await AsyncStorage.getItem("fcmToken");
            const req = {
              userId: "",
              deviceTokenId: token,
            };
            const rsp = await notiRegister(req);
            // console.log("???????....", rsp);
            navigation.navigate("Bottom_Tab");
          } else {
            console.log("Close");
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        Alert.alert("Error", "Failed to retrieve identity token.");
      }
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
      // Alert.alert('Error', 'An error occurred during Sign In with Apple.');
    } finally {
      setLoading(false);
    }
  };

  //Sing Facebook, Google & Apple
  const signInGoogle = async () => {
    try {
      setLoading(true);

      // ตรวจสอบบริการ Google Play ก่อน
      await GoogleSignin.hasPlayServices();
      const userInfoGoogle = await GoogleSignin.signIn();
      const idToken = userInfoGoogle.data?.idToken || "";
      console.log("idToken...", idToken);

      // ตรวจสอบ Token และส่งไปยัง API
      const req = {
        provider: "Google",
        accessToken: idToken,
        acceptDetail: {
          isAcceptAgreement: true,
          isAcceptUserName: true,
          isAcceptName: true,
          isAcceptMobileNo: true,
          isAcceptPhoneNo: true,
          isAcceptEmail: true,
          isAcceptLineId: true,
          isAcceptAddress: true,
          isAcceptBirthDate: true,
          isAcceptJoinDate: true,
          isAcceptImageUser: true,
        },
      };

      const res = await registerThirdparty(req);
      // console.log("API Response:", res);
      if (res.message === "Login Successfully") {
        // หากลงทะเบียนสำเร็จ
        await storage.delete("successLogin");
        await AsyncStorage.removeItem("userIdLogin");
        await AsyncStorage.removeItem("fcmToken");
        Alert.alert(t("signup_button"), t("success_message"), [
          {
            text: t("login_button"),
            onPress: () => loginGoogle(),
          },
        ]);
      } else {
        Alert.alert(t("noti"), t("login_button"), [
          {
            text: t("cancel"),
            onPress: () => console.log("Cancel Pressed"),
            style: "cancel",
          },
          {
            text: t("login_button"),
            onPress: () => loginGoogle(),
          },
        ]);
      }

      // ทำการนำทางหรือแสดงข้อความให้ผู้ใช้
    } catch (error: unknown) {
      console.error("Error during Google sign-in:", error);

      // ตรวจสอบว่า error เป็น Error หรือไม่
      if (error instanceof Error) {
        // ถ้า error เป็นประเภท Error (เช่นข้อผิดพลาดจาก GoogleSignin)
        if (error.message === "CANCELED") {
          console.log("User canceled Google sign-in");
        } else if (error.message === "PLAY_SERVICES_NOT_AVAILABLE") {
          console.log("Google Play Services not available");
          Alert.alert(
            "ข้อผิดพลาด",
            "ไม่สามารถเชื่อมต่อกับ Google Play Services"
          );
        } else {
          Alert.alert("Error", "เกิดข้อผิดพลาดในการลงทะเบียน");
        }
      } else {
        // หาก error ไม่ใช่ประเภท Error ให้แสดงข้อผิดพลาดทั่วไป
        Alert.alert("Error", "เกิดข้อผิดพลาดที่ไม่ทราบประเภท");
      }
    } finally {
      setLoading(false);
    }
  };
  const signInApple = async () => {
    try {
      setLoading(true);
      if (!appleAuth.isSupported) {
        Alert.alert(
          "Error",
          "Sign In with Apple is not supported on this device."
        );
        return;
      }
      const appleAuthResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      const idToken = appleAuthResponse.identityToken || "";

      const { identityToken, email, fullName, user } = appleAuthResponse;
      if (identityToken) {
        // Alert.alert('Success', `User ID: ${user}\nEmail: ${email}`);
        console.log("Success", `User ID: ${user}\nEmail: ${email}`);
        try {
          const req = {
            provider: "Apple",
            accessToken: idToken,
            acceptDetail: {
              isAcceptAgreement: true,
              isAcceptUserName: true,
              isAcceptName: true,
              isAcceptMobileNo: true,
              isAcceptPhoneNo: true,
              isAcceptEmail: true,
              isAcceptLineId: true,
              isAcceptAddress: true,
              isAcceptBirthDate: true,
              isAcceptJoinDate: true,
              isAcceptImageUser: true,
            },
          };

          const dataReq = await registerThirdparty(req);
          // console.log("User registered successfully:", dataReq);
          await storage.delete("successLogin");
          await AsyncStorage.removeItem("userIdLogin");
          await AsyncStorage.removeItem("fcmToken");
          if (dataReq.message === "Login Successfully") {
            Alert.alert(t("signup_button"), t("success_message"), [
              {
                text: t("login_button"),
                onPress: () => loginApple(),
              },
            ]);
          } else {
            Alert.alert(
              "แจ้งเตือน",
              "คุณเคยใช้บัญชีนี้ในการสมัคแล้ว\nกรุณาลองใหม่อีกครั้ง หรือ ทำการเข้าสู่ระบบ",
              [
                {
                  text: t("cancel"),
                  onPress: () => console.log("Cancel Pressed"),
                  style: "cancel",
                },
                {
                  text: t("login_button"),
                  onPress: () => loginApple(),
                },
              ]
            );
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        Alert.alert("Error", "Failed to retrieve identity token.");
      }
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
      // Alert.alert('Error', 'An error occurred during Sign In with Apple.');
    } finally {
      setLoading(false);
    }
  };
  const contentSetting = () => (
    <View style={{ padding: moderateScale(20) }}>
      {/* {Facebook} */}
      {/* <View style={btn.btn_settingApp}>
        <View style={{ flexDirection: "row" }}>
          <FastImage
            style={img.img_GooGle}
            source={FaceBook}
            resizeMode={FastImage.resizeMode.contain}
          />
          <View style={{ margin: moderateScale(5) }} />
          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_light, { margin: moderateScale(5) }]}>
              Facebook
            </Text>
            <Switch
              trackColor={{ false: "#767577", true: "#81b0ff" }}
              thumbColor={isEnabled ? "#f5dd4b" : "#f4f3f4"}
              ios_backgroundColor="#3e3e3e"
              onValueChange={toggleSwitch}
              value={isEnabled}
            />
          </View>
        </View>
      </View>
      <View style={{ margin: moderateScale(5) }} /> */}

      {/* {Google} */}
      <TouchableOpacity
        style={btn.btn_settingApp}
        onPress={() => signInGoogle()}
      >
        <View style={{ flexDirection: "row" }}>
          <FastImage
            style={img.img_GooGle}
            source={Images.GooGle}
            resizeMode={FastImage.resizeMode.contain}
          />
          <View style={{ margin: moderateScale(5) }} />
          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060,{ margin: moderateScale(5) }]}>
              Google
            </Text>
            <View style={{ justifyContent: "center" }}>{iconRight()}</View>
          </View>
        </View>
      </TouchableOpacity>
      <View style={{ margin: moderateScale(5) }} />

      {/* {Apple} */}
      {Platform.OS === "ios" ? (
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => signInApple()}
        >
          <View style={{ flexDirection: "row" }}>
            <FastImage
              style={img.img_Apple}
              source={Images.Apple}
              resizeMode={FastImage.resizeMode.contain}
            />
            <View style={ctn.ctn_editAccount}>
              <Text
                style={[
                  fonstStyle.f14_bold,
                  txt.txt_606060,
                  { margin: moderateScale(6), top: moderateScale(5) },
                ]}
              >
                Apple
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
      ) : null}
      <View style={{ margin: moderateScale(5) }} />
    </View>
  );

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <>
      {isLoading ? <Loading /> : null}
      <View style={{ flex: 1 }}>
        <SettingBar
          onBack={() => navigation.goBack()}
          pageName={t("connected_account")}
        />
       {orientation === "portrait" ? mainPortrait() : mainlandscape()}
      </View>
    </>
  );
}
