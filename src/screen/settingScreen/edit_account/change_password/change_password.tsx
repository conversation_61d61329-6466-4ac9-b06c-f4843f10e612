import { MMKV } from "react-native-mmkv";
import React, { useState } from "react";
import { moderateScale } from "react-native-size-matters";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  Text,
  View,
  Alert,
  TextInput,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
//Style Sheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import txt from "../../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Svg
import { iconKey } from "../../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import Loading from "../../../../components/loading/loading";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";
//Api
import { encrypt } from "../../../../action/encryption";
import { ChangePass } from "../../../../action/Mefarm_Identity_API";

export default function Change_password({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  const storage = new MMKV();
  //String
  const [currentPass, setCurrentPasss] = useState<string>("");
  const [changePass, setChangePass] = useState<string>("");
  const [checkPass, setCheckPass] = useState<string>("");
  //True & false
  const [isLoading, setLoading] = useState<boolean>(false);

  const isPasswordValid =
    currentPass.length >= 8 &&
    /[0-9]/.test(currentPass) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(currentPass) &&
    /[A-Z]/.test(currentPass);

  const isNewPassword =
    changePass.length >= 8 &&
    /[0-9]/.test(changePass) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(changePass) &&
    /[A-Z]/.test(changePass);

  const isConfirmValid =
    checkPass.length >= 8 &&
    /[0-9]/.test(checkPass) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(checkPass) &&
    /[A-Z]/.test(checkPass);

  const isButtonDisabled =
    !currentPass || !changePass || !checkPass || changePass !== checkPass;

  //Function
  const onLogOut = async () => {
    await storage.delete("successLogin");
    await AsyncStorage.removeItem("userIdLogin");
    navigation.reset({
      index: 1,
      routes: [{ name: "Select_login" }],
    });
  };
  const onReset = () => {
    setCurrentPasss("");
    setChangePass("");
    setCheckPass("");
  };
  const goChangePass = async () => {
    try {
      setLoading(true);
      const myCurrentPass = await encrypt(currentPass);
      const myChangePass = await encrypt(changePass);
      const myCheckPass = await encrypt(checkPass);
      const req = {
        userName: "",
        currentPassword: myCurrentPass,
        password: myChangePass,
        confirmPassword: myCheckPass,
      };

      const res = await ChangePass(req);
      console.log(res);
      
      if (res.message === "Change Username Password Successfully") {
        Alert.alert(t("change_password"), t("success_message"), [
          {
            text: t("agree"),
            onPress: () => onLogOut(),
          },
        ]);
      } else {
        Alert.alert(t("error"), t("something"), [
          {
            text: t("agree"),
            onPress: () => onReset(),
          },
        ]);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  //Ui
  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        {/* รหัสปัจจุบัน */}
        <View style={ctn.ctn_spaceBet}>
          <Text style={[txt.txt_HeaderInput, fonstStyle.f12_bold]}>
            {t("old_password")}
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
            {!isPasswordValid && currentPass.length > 0
              ? t("incomplete_password")
              : ""}
          </Text>
        </View>
        <View style={{ margin: moderateScale(2) }} />
        <TextInput
          style={[txt.txt_InputChangePass, fonstStyle.f12_light]}
          onChangeText={(text) => setCurrentPasss(text)}
          value={currentPass}
        />
        <View style={{ margin: moderateScale(5) }} />

        {/* รหัสใหม่ */}
        <View style={ctn.ctn_spaceBet}>
          <Text style={[txt.txt_HeaderInput, fonstStyle.f12_bold]}>
            {t("new_password")}
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
            {!isNewPassword && changePass.length > 0
              ? t("incomplete_password")
              : ""}
          </Text>
        </View>
        <View style={{ margin: moderateScale(2) }} />
        <TextInput
          style={[txt.txt_InputChangePass, fonstStyle.f12_light]}
          onChangeText={(text) => setChangePass(text)}
          value={changePass}
        />
        <View style={{ margin: moderateScale(5) }} />

        {/* ยืนยันรหัสผ่านใหม่ */}
        <View style={ctn.ctn_spaceBet}>
          <Text style={[txt.txt_HeaderInput, fonstStyle.f12_bold]}>
            {t("confirm_password")}
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
            {!isConfirmValid && checkPass.length > 0
              ? t("incomplete_password")
              : ""}
            {changePass != checkPass ? t("error_password") : ""}
          </Text>
        </View>
        <View style={{ margin: moderateScale(2) }} />
        <TextInput
          style={[txt.txt_InputChangePass, fonstStyle.f12_light]}
          onChangeText={(text) => setCheckPass(text)}
          value={checkPass}
        />

        <View style={{ margin: moderateScale(10) }} />
        <TouchableOpacity
          style={[btn.btn_SignUp, { opacity: isButtonDisabled ? 0.5 : 1 }]}
          disabled={isButtonDisabled}
          onPress={() => goChangePass()}
        >
          <Text style={[txt.txt_SignUp, fonstStyle.f14_bold]}>
            {t("confirm")}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const mainPortrait = () => {
    return (
      <View style={{ flex: 1 }}>
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </View>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };

  return (
    <>
      {isLoading ? <Loading /> : null}
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("change_password")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
