import { moderateScale } from "react-native-size-matters";
import { Text, View, Linking, ScrollView, SafeAreaView } from "react-native";
//Style Sheet
import ctn from "../../../../styleSheet/ctn";
import txt from "../../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";

export default function Contact_us({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const openLink = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log(`Don't know how to open this URL: ${url}`);
      }
    } catch (error) {
      console.error("Error opening URL:", error);
    }
  };
  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("company_location")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              txt.txt_606060,
              { textAlign: "justify" },
            ]}
          >
            {t("company_detail")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />

          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("email")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            <EMAIL>
          </Text>
          <View style={{ margin: moderateScale(5) }} />

          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("phone_nember")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            ************
          </Text>

          <View style={{ margin: moderateScale(5) }} />

          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("website")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />

          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("http://www.cana.co.th/")}
          >
            http://www.cana.co.th/
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("http://www.mefarmhug.com/")}
          >
            http://www.mefarmhug.com/
          </Text>
        </ScrollView>
      </View>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("contact_us")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
