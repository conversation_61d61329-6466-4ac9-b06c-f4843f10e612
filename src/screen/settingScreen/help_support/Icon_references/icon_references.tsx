import { moderateScale } from "react-native-size-matters";
import { Text, View, ScrollView, Linking, SafeAreaView } from "react-native";
//Style Sheet
import ctn from "../../../../styleSheet/ctn";
import txt from "../../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Svg
import { iconRight } from "../../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";

export default function Icon_references({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const openLink = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log(`Don't know how to open this URL: ${url}`);
      }
    } catch (error) {
      console.error("Error opening URL:", error);
    }
  };

  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink(
                "https://icon-icons.com/th/users/Sd7ZnWgGn1MEUZX2dSHmD/icon-sets/"
              )
            }
          >
            https://icon-icons.com/th/users/Sd7ZnWgGn1MEUZX2dSHmD/icon-sets/
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/pixel-perfect")
            }
          >
            https://www.flaticon.com/authors/pixel-perfect
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/wr-graphic-garage")
            }
          >
            https://www.flaticon.com/authors/wr-graphic-garage
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/iconbaandar")
            }
          >
            https://www.flaticon.com/authors/iconbaandar
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/arkinasi")
            }
          >
            https://www.flaticon.com/authors/arkinasi
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/leremy")}
          >
            https://www.flaticon.com/authors/leremy
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/victoruler")
            }
          >
            https://www.flaticon.com/authors/victoruler
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/design-circle")
            }
          >
            https://www.flaticon.com/authors/design-circle
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink(
                "https://www.flaticon.com/authors/maxim-basinski-premium"
              )
            }
          >
            https://www.flaticon.com/authors/maxim-basinski-premium
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/roundicons")
            }
          >
            https://www.flaticon.com/authors/roundicons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/pixel-perfect")
            }
          >
            https://www.flaticon.com/authors/pixel-perfect
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/andinur")}
          >
            https://www.flaticon.com/authors/andinur
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/pixelmeetup")
            }
          >
            https://www.flaticon.com/authors/pixelmeetup
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/creatype")
            }
          >
            https://www.flaticon.com/authors/creatype
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/smashicons")
            }
          >
            https://www.flaticon.com/authors/smashicons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink(
                "https://www.freepik.com/?_gl=1*1lypj89*test_ga*NTA5NTQ1NTkxLjE3MTg3MDE5NjI.*test_ga_523JXC6VL7*MTcyMDc1MzMwMy42MS4xLjE3MjA3NTYxMjkuNjAuMC4w*fp_ga*NTA5NTQ1NTkxLjE3MTg3MDE5NjI.*fp_ga_1ZY8468CQB*MTcyMDc1MzMwMy42MS4xLjE3MjA3NTYxMjkuNjAuMC4w"
              )
            }
          >
            https://www.freepik.com/?_gl=1*1lypj89*test_ga*NTA5NTQ1NTkxLjE3MTg3MDE5NjI.*test_ga_523JXC6VL7*MTcyMDc1MzMwMy42MS4xLjE3MjA3NTYxMjkuNjAuMC4w*fp_ga*NTA5NTQ1NTkxLjE3MTg3MDE5NjI.*fp_ga_1ZY8468CQB*MTcyMDc1MzMwMy42MS4xLjE3MjA3NTYxMjkuNjAuMC4w
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/pixel-perfect")
            }
          >
            https://www.flaticon.com/authors/pixel-perfect
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink(
                "www.freepik.com/icon/cctv_8665818#fromView=search&page=1&position=38&uuid=e1666e80-7bfb-4ef6-bbd4-5f14f590ac2b"
              )
            }
          >
            www.freepik.com/icon/cctv_8665818#fromView=search&page=1&position=38&uuid=e1666e80-7bfb-4ef6-bbd4-5f14f590ac2b
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/wanicon")}
          >
            https://www.flaticon.com/authors/wanicon
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/roundicons")
            }
          >
            https://www.flaticon.com/authors/roundicons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/prosymbols-premium")
            }
          >
            https://www.flaticon.com/authors/prosymbols-premium
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/mnauliady")
            }
          >
            https://www.flaticon.com/authors/mnauliady
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/maxicons")
            }
          >
            https://www.flaticon.com/authors/maxicons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/lafs")}
          >
            https://www.flaticon.com/authors/lafs
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/lafs")}
          >
            https://www.flaticon.com/authors/lafs
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/ilham-fitrotul-hayat")
            }
          >
            https://www.flaticon.com/authors/ilham-fitrotul-hayat
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/smashicons")
            }
          >
            https://www.flaticon.com/authors/smashicons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/graphics-plazza")
            }
          >
            https://www.flaticon.com/authors/graphics-plazza
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/mihimihi")
            }
          >
            https://www.flaticon.com/authors/mihimihi
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/ian-june")
            }
          >
            https://www.flaticon.com/authors/ian-june
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/freepik")}
          >
            https://www.flaticon.com/authors/freepik
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/mayor-icons")
            }
          >
            https://www.flaticon.com/authors/mayor-icons
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() => openLink("https://www.flaticon.com/authors/anggara")}
          >
            https://www.flaticon.com/authors/anggara
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/phoenix-group")
            }
          >
            https://www.flaticon.com/authors/phoenix-group
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/assetwave")
            }
          >
            https://www.flaticon.com/authors/assetwave
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/assetwave")
            }
          >
            https://www.flaticon.com/authors/assetwave
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/graphics-plazza")
            }
          >
            https://www.flaticon.com/authors/graphics-plazza
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/dinosoftlabs")
            }
          >
            https://www.flaticon.com/authors/dinosoftlabs
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/dinosoftlabs")
            }
          >
            https://www.flaticon.com/authors/dinosoftlabs
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink("https://www.flaticon.com/authors/sonnycandra")
            }
          >
            https://www.flaticon.com/authors/sonnycandra
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              { color: "blue", textDecorationLine: "underline" },
            ]}
            onPress={() =>
              openLink(" https://www.flaticon.com/authors/indra-maulana-yusuf")
            }
          >
            https://www.flaticon.com/authors/indra-maulana-yusuf
          </Text>
          <View style={{ margin: moderateScale(2) }} />
        </ScrollView>
      </View>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("icon_references")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
