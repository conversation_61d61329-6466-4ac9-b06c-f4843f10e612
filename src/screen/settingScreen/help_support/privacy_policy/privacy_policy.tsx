import React, { useEffect, useState } from "react";
import { moderateScale } from "react-native-size-matters";
import { Text, View, FlatList, <PERSON>rollView, SafeAreaView } from "react-native";
//Style Sheet
import ctn from "../../../../styleSheet/ctn";
import txt from "../../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";
//API
import { getPolicy } from "../../../../action/Mefarm_Admin_API";

export default function Privacy_policy({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const [policy, setPolicy] = useState<any[]>([]);

  useEffect(() => {
    const dataPlicy = async () => {
      const res = await getPolicy();
      const data = res.model || [];
      setPolicy(data);
    };
    dataPlicy();
  }, []);

  const stripHtml = (html: string) => {
    return html
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<\/p>/gi, "\n\n")
      .replace(/<[^>]+>/g, "")
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/\n{3,}/g, "\n\n")
      .trim();
  };

  const contentSetting = () => {
    return (
      <ScrollView style={{ flex: 1 }}>
        <View style={{ padding: moderateScale(20) }}>
          {policy.map((item: any, idx: number) => (
            <Text
              key={idx}
              style={[
                fonstStyle.f12_light,
                txt.txt_606060,
                { marginBottom: moderateScale(16) },
              ]}
            >
              {stripHtml(item.description)}
            </Text>
          ))}
        </View>
      </ScrollView>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("privacy_policy")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
