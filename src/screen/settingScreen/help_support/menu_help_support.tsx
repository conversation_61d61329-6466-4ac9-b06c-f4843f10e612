import { moderateScale } from "react-native-size-matters";
import { Text, View, SafeAreaView, TouchableOpacity } from "react-native";
//Style Sheet
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../../styleSheet/style_Custom";
//Svg
import { iconRight } from "../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../hooks/useOrientation";
import SettingBar from "../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../i18n";

export default function Menu_help_support({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const contentSetting = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Privacy_policy")}
        >
          <View style={{ flexDirection: "row" }}>
            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("privacy_policy")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Contact_us")}
        >
          <View style={{ flexDirection: "row" }}>
            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("contact_us")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />

        <TouchableOpacity
          style={btn.btn_settingApp}
          onPress={() => navigation.navigate("Icon_references")}
        >
          <View style={{ flexDirection: "row" }}>
            <View style={ctn.ctn_editAccount}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("icon_references")}
              </Text>
              <View style={{ justifyContent: "center" }}>{iconRight()}</View>
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
      </View>
    );
  };

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <View style={{ flex: 1 }}>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("help_and_support")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </View>
  );
}
