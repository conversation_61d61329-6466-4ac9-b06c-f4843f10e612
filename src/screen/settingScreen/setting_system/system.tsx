import { Text, View, SafeAreaView, TouchableOpacity } from "react-native";
import React from "react";
//Style Sheet
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import img from "../../../styleSheet/img";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../../styleSheet/style_Custom";
//Components
import { useOrientation } from "../../../hooks/useOrientation";
import SettingBar from "../../../components/appBar/setting_Bar";
//Svg
import {
  iconBell,
  iconRight,
  iconLanguage,
  iconLikeSetting,
} from "../../../assets/svg/svg_other";
//Translation
import { useTranslation } from "../../i18n";
import { moderateScale } from "react-native-size-matters";

export default function System({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  //Ui
  const contentSetting = () => (
    <View style={{ padding: moderateScale(20) }}>
      {/* {ภาษา} */}
      <TouchableOpacity
        style={btn.btn_settingApp}
        onPress={() => navigation.navigate("Language")}
      >
        <View style={{ flexDirection: "row" }}>
          {iconLanguage()}
          <View style={{ margin: moderateScale(5) }} />

          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("language")}
            </Text>
            <View style={{ justifyContent: "center" }}>{iconRight()}</View>
          </View>
        </View>
      </TouchableOpacity>
      <View style={{ margin: moderateScale(5) }} />

      {/* {การอนุญาต} */}
      <TouchableOpacity
        style={btn.btn_settingApp}
        onPress={() => navigation.navigate("Permission")}
      >
        <View style={{ flexDirection: "row" }}>
          {iconLikeSetting()}
          <View style={{ margin: moderateScale(5) }} />

          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("Permission")}
            </Text>
            <View style={{ justifyContent: "center" }}>{iconRight()}</View>
          </View>
        </View>
      </TouchableOpacity>
      <View style={{ margin: moderateScale(5) }} />

      {/* {การแจ้งเตือน} */}
      {/* <TouchableOpacity
        style={btn.btn_settingApp}
        onPress={() => navigation.navigate('System')}>
        <View style={{flexDirection: 'row'}}>
          {iconBell()}
          <View style={{margin: moderateScale(5)}} />

          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_light]}>{t('Notification')}</Text>
            <View style={{justifyContent: 'center'}}>{iconRight()}</View>
          </View>
        </View>
      </TouchableOpacity>
      <View style={{margin: moderateScale(5)}} /> */}
    </View>
  );

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("setting_system")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
