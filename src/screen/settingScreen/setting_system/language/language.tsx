import { CheckBox } from "@rneui/themed";
import React, { useState, useEffect, useRef } from "react";
import { Text, View, SafeAreaView, TouchableOpacity } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as RNLocalize from "react-native-localize";
//Style Sheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import img from "../../../../styleSheet/img";
import txt from "../../../../styleSheet/txt";
import oth from "../../../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Components
import SettingBar from "../../../../components/appBar/setting_Bar";
//Svg
import { iconLanguage } from "../../../../assets/svg/svg_other";
//Translation
import { useTranslation } from "../../../i18n";
import { moderateScale } from "react-native-size-matters";
import { useOrientation } from "../../../../hooks/useOrientation";

export default function Language({ navigation }: any) {
  const orientation = useOrientation();
  const ACTIVE_BUTTON_KEY = "activeButton";
  const { t, setLanguage } = useTranslation();
  const [activeButton, setActiveButton] = useState<any>(null);

  //Function
  useEffect(() => {
    const loadActiveButton = async () => {
      const storedButton = await AsyncStorage.getItem(ACTIVE_BUTTON_KEY);
      if (storedButton !== null) {
        setActiveButton(parseInt(storedButton, 10));
      }
    };
    loadActiveButton();
  }, []);
  const handleButtonClick = async (buttonIndex: any) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
      await AsyncStorage.setItem(ACTIVE_BUTTON_KEY, buttonIndex.toString());
      if (buttonIndex === 1) {
        const deviceLanguage = RNLocalize.getLocales()[0].languageCode;
        setLanguage(deviceLanguage === "th" ? "th" : "en");
      } else if (buttonIndex === 2) {
        setLanguage("th");
      } else if (buttonIndex == 3) {
        setLanguage("en");
      }
    }
  };

  //Ui
  const contentSetting = () => (
    <View style={{ padding: moderateScale(20) }}>
      {/* {ภาษาของณ์} */}
      <View style={btn.btn_settingApp}>
        <View style={{ flexDirection: "row" }}>
          {iconLanguage()}
          <View style={{ margin: moderateScale(5) }} />

          <View style={ctn.ctn_editAccount}>
            <View style={{ flexDirection: "column" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Device_language")}
              </Text>
              {/* <Text style={[fonstStyle.f12_light, txt.txt_directed]}>
                Device language
              </Text> */}
            </View>
            {/* {radio} */}
            <TouchableOpacity
              onPress={() => handleButtonClick(1)}
              disabled={activeButton === 1 ? true : false}
              style={ctn.ctn_Radio}
            >
              {activeButton === 1 && <View style={oth.radio_Language} />}
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View style={{ margin: moderateScale(5) }} />

      {/* {ภาษา} */}
      <View style={btn.btn_settingApp}>
        <View style={{ flexDirection: "row" }}>
          {iconLanguage()}
          <View style={{ margin: moderateScale(5) }} />

          <View style={ctn.ctn_editAccount}>
            <View style={{ flexDirection: "column" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Thai")}
              </Text>
              {/* <Text style={[fonstStyle.f12_light, txt.txt_directed]}>Thai</Text> */}
            </View>
            {/* {radio} */}
            <TouchableOpacity
              onPress={() => handleButtonClick(2)}
              disabled={activeButton === 2 ? true : false}
              style={ctn.ctn_Radio}
            >
              {activeButton === 2 && <View style={oth.radio_Language} />}
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View style={{ margin: moderateScale(5) }} />

      <View style={btn.btn_settingApp}>
        <View style={{ flexDirection: "row" }}>
          {iconLanguage()}
          <View style={{ margin: moderateScale(5) }} />

          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("English")}
            </Text>
            {/* {radio} */}
            <TouchableOpacity
              onPress={() => handleButtonClick(3)}
              disabled={activeButton === 3 ? true : false}
              style={ctn.ctn_Radio}
            >
              {activeButton === 3 && <View style={oth.radio_Language} />}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );

  //Main
  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <>
      <SettingBar onBack={() => navigation.goBack()} pageName={t("language")} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
