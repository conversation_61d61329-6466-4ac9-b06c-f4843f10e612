import { moderateScale } from "react-native-size-matters";
import React, { useState, useEffect, useRef } from "react";
import Geolocation from "@react-native-community/geolocation";
import {
  Text,
  View,
  Switch,
  Alert,
  SafeAreaView,
  TouchableOpacity,
  Platform,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
//Style Sheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import img from "../../../../styleSheet/img";
import txt from "../../../../styleSheet/txt";
import oth from "../../../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import SettingBar from "../../../../components/appBar/setting_Bar";
//Translation
import { useTranslation } from "../../../i18n";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";

export default function Permission({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  const [isSwitchNoti, setSwitchNoti] = useState<boolean>(false);
  const [isSwitchLocation, setSwitchLocation] = useState<boolean>(false);
  const [isSwitchCamera, setSwitchCamera] = useState<boolean>(false);

  // Load AsyncStorage values on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const notiPermission = await AsyncStorage.getItem(
          "notificationPermission"
        );

        console.log("Loaded notificationPermission:", notiPermission);

        // อัปเดตสถานะ Switch
        setSwitchNoti(notiPermission === "granted"); // ตรวจสอบค่า notification
      } catch (error) {
        console.error("Error loading settings:", error);
      }
    };

    loadSettings();
  }, []);

  // โหลดสถานะ permission กล้องจาก AsyncStorage
  useEffect(() => {
    const loadCameraPermission = async () => {
      try {
        const cameraPermission = await AsyncStorage.getItem("cameraPermission");
        setSwitchCamera(cameraPermission === "granted");
      } catch (error) {
        console.error("Error loading camera permission:", error);
      }
    };
    loadCameraPermission();
  }, []);

  const toggleNoti = async () => {
    try {
      const newState = !isSwitchNoti;
      setSwitchNoti(newState);
      if (newState) {
        await AsyncStorage.setItem("notificationPermission", "granted"); // บันทึก "granted"
      } else {
        await AsyncStorage.removeItem("notificationPermission"); // ลบค่าเมื่อปิด
      }
    } catch (error) {
      console.error("Error updating notification setting:", error);
    }
  };
  const toggleLocation = async () => {
    try {
      const newState = !isSwitchLocation;
      setSwitchLocation(newState);

      if (newState) {
        // ดึงตำแหน่งปัจจุบัน
        Geolocation.getCurrentPosition(
          async (position: any) => {
            const { latitude, longitude } = position.coords;
            const currentLocation = JSON.stringify({ latitude, longitude });

            // บันทึกตำแหน่งปัจจุบันลงใน AsyncStorage
            await AsyncStorage.setItem("userLocation", currentLocation);
            console.log("Saved current location:", currentLocation);
          },
          (error: any) => {
            console.error("Error fetching current location:", error);
            Alert.alert(
              "Error",
              "Unable to fetch location. Please try again or check your location settings."
            );
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      } else {
        // ลบข้อมูลพิกัดเมื่อ Switch ปิด
        await AsyncStorage.removeItem("userLocation");
        console.log("Location permission removed.");
      }
    } catch (error) {
      console.error("Error updating location setting:", error);
    }
  };

  //Ui
  const contentSetting = () => (
    <View style={{ padding: moderateScale(20) }}>
      {/* {การแจ้งเตือนแอพ} */}
      <View style={btn.btn_settingApp}>
        <View style={{ flexDirection: "row" }}>
          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_bold]}>{t("Notification")}</Text>
            <Switch
              trackColor={{
                false: BgColor.Bg_D6D6D6,
                true: BgColor.Bg_84B8A2,
              }}
              thumbColor={isSwitchNoti ? BgColor.Bg_FFFFFF : BgColor.Bg_FFFFFF}
              onValueChange={toggleNoti}
              value={isSwitchNoti}
            />
          </View>
        </View>
      </View>
      <View style={{ margin: moderateScale(5) }} />

      {/* {การเข้าถึงตำแหน่งที่ตั้ง} */}
      {/* <TouchableOpacity
        style={btn.btn_settingApp}
        onPress={() => navigation.navigate("Language")}
      >
        <View style={{ flexDirection: "row" }}>
          <View style={ctn.ctn_editAccount}>
            <Text style={[fonstStyle.f14_light]}>{t("location_access")}</Text>
            <Switch
              trackColor={{
                false: BgColor.Bg_D6D6D6,
                true: BgColor.Bg_84B8A2,
              }}
              thumbColor={
                isSwitchLocation ? BgColor.Bg_FFFFFF : BgColor.Bg_FFFFFF
              }
              onValueChange={toggleLocation}
              value={isSwitchLocation}
            />
          </View>
        </View>
      </TouchableOpacity>
      <View style={{ margin: moderateScale(5) }} /> */}
    </View>
  );

  const mainPortrait = () => {
    return <View style={ctn.continueMain}>{contentSetting()}</View>;
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>{contentSetting()}</View>
      </SafeAreaView>
    );
  };
  return (
    <>
      <SettingBar
        onBack={() => navigation.goBack()}
        pageName={t("Permission")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
