import { useState, useEffect } from 'react';
import * as RNLocalize from 'react-native-localize';
import AsyncStorage from '@react-native-async-storage/async-storage';
import th from '../json/th.json';
import en from '../json/en.json';
import RNRestart from 'react-native-restart';
// Define the translation types based on the structure of en.json and th.json
type TranslationKeys = keyof typeof en;

const translations: Record<string, Record<TranslationKeys, string>> = {
  en,
  th,
};

const LANGUAGE_KEY = 'appLanguage';

const getDeviceLanguage = async (): Promise<string> => {
  const storedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
  if (storedLanguage) {
    return storedLanguage;
  }

  const locale = RNLocalize.getLocales()[0].languageCode;
  return translations[locale] ? locale : 'en';
};

export const useTranslation = () => {
  const [language, setLanguage] = useState<string>('en');

  useEffect(() => {
    const loadLanguage = async () => {
      const defaultLanguage = await getDeviceLanguage();
      // console.log("defaultLanguage>>>>", defaultLanguage);
      setLanguage(defaultLanguage);
    };

    loadLanguage();
  }, []);

  const saveLanguage = async (selectedLanguage: string) => {
    await AsyncStorage.setItem(LANGUAGE_KEY, selectedLanguage);
    setLanguage(selectedLanguage);
        RNRestart.Restart();
  };

  const translate = (key: TranslationKeys): string => {
    return translations[language][key] || key;
  };

  return { t: translate, setLanguage: saveLanguage };
};
