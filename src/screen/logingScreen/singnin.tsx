import {
  Text,
  View,
  Modal,
  Alert,
  Platform,
  StatusBar,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  KeyboardAvoidingView,
} from "react-native";
import moment from "moment";
import React, { useState } from "react";
import { MMKV } from "react-native-mmkv";
import FastImage from "react-native-fast-image";
import { TextInput } from "react-native-paper";
import {
  statusCodes,
  GoogleSignin,
} from "@react-native-google-signin/google-signin";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { appleAuth } from "@invertase/react-native-apple-authentication";
import { verticalScale, moderateScale } from "react-native-size-matters";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle from "../../styleSheet/style_Custom";
import Images from "../../utils/imageManager";
//Svg
import {
  iconError,
  iconCheck,
  iconEyeOpen,
  iconEyeClose,
} from "../../assets/svg/svg_other";
//Components
import Loading from "../../components/loading/loading";
import Default_Bar from "../../components/appBar/default_Bar";
//Translation
import { useTranslation } from "../i18n";
//Api
import { encrypt } from "../../action/encryption";
import {
  logInApi,
  registerApi,
  logInThirdParty,
  registerThirdparty,
} from "../../action/Mefarm_Identity_API";

GoogleSignin.configure({
  webClientId:
    "1027814423505-99l0e5l9d6hbl8tku5gadpegct8kks84.apps.googleusercontent.com",
  offlineAccess: true,
});

export default function Login({ navigation, route }: any) {
  // Route
  const params = route.params || "";
  const typeSingnin = params.typeSingnin || "";
  const typeGoogle = params.typeGoogle || "";
  const infoGoogle = params.infoGoogle || "";

  const storage = new MMKV();

  //Translation
  const { t } = useTranslation();
  //State
  const [isEmail, setEmail] = useState<string>("");
  const [isMyPhone, setMyPhone] = useState<string>("");
  const [isPassWord, setPassWord] = useState<string>("");
  const [isConfirmPass, setConfirmPass] = useState<string>("");
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isShowPass, SetShowPass] = useState<boolean>(false);
  const [isModalTrue, setModalTrue] = useState<boolean>(false);
  const [isModalFalse, setModalFalse] = useState<boolean>(false);
  const [isModalError, setModalError] = useState<boolean>(false);
  const [isModalRegis, setModalRegis] = useState<boolean>(false);
  const [isModalLoging, setModalLoging] = useState<boolean>(false);
  const [isModalErrorPass, setModalErrorPass] = useState<boolean>(false);
  const [isShowConFirmPass, SetShowConFirmPass] = useState<boolean>(false);

  const isPasswordValid =
    isPassWord.length >= 8 &&
    /[0-9]/.test(isPassWord) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(isPassWord) &&
    /[A-Z]/.test(isPassWord);

  const isConfirmValid =
    isConfirmPass.length >= 8 &&
    /[0-9]/.test(isConfirmPass) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(isConfirmPass) &&
    /[A-Z]/.test(isConfirmPass);

  const isPasswordMatch = isPassWord === isConfirmPass;

  const isPhoneValid = isMyPhone.length === 10;
  const isEmailValid = isEmail.includes("@");

  // เงื่อนไขสำหรับปุ่ม Disabled
  const isButtonDisabled = typeGoogle
    ? !isPhoneValid // หากเป็น typeGoogle ให้ตรวจเฉพาะ isPhoneValid
    : !(
        isPasswordMatch &&
        isPasswordValid &&
        isConfirmValid &&
        isPhoneValid &&
        isEmail &&
        isEmailValid
      ); // เงื่อนไขปกติ

  // เงื่อนไขสำหรับปุ่ม Login
  const isButtonLogin = typeGoogle
    ? !isPhoneValid // หากเป็น typeGoogle ให้ตรวจเฉพาะ isPhoneValid
    : !(isPasswordValid && isPhoneValid); // เงื่อนไขปกติ

  //Function
  const toggleShowPassword = () => {
    SetShowPass(!isShowPass);
  };
  const toggleShowConFirmPassword = () => {
    SetShowConFirmPass(!isShowConFirmPass);
  };
  const goHome = () => {
    setModalTrue(false);
    navigation.navigate("Select_login");
  };
  const postLogin = async () => {
    try {
      setLoading(true);
      const myUserName = await encrypt(isMyPhone);
      const myPassWord = await encrypt(isPassWord);
      //obj
      const req = {
        userName: myUserName,
        password: myPassWord,
      };
      if (!isMyPhone || !isPassWord) {
        setModalFalse(true);
      } else if (isMyPhone.length < 10) {
        setModalError(true);
      } else if (isPassWord.length < 6) {
        setModalError(true);
      } else {
        const obj = await logInApi(req);
        await storage.set("successLogin", obj.message || "");
        // await AsyncStorage.setItem("successLogin", obj.message || "");
        await AsyncStorage.setItem(
          "userIdLogin",
          obj.model.userInfo.userId || ""
        );
        await AsyncStorage.setItem(
          "tokenLogin",
          obj.model.tokenInfo.access_token || ""
        );
        if (obj.message === "Login Successfully") {
          //Fcm register
          // const tokenDevice = await AsyncStorage.getItem("fcmToken");
          // const token = storage.getString("fcmToken");
          // console.log("tokenDevice...", token);
          // const req = {
          //   userId: "",
          //   deviceTokenId: token,
          // };
          // const rsp = await notiRegister(req);
          // console.log("???????....", obj.model);
          navigation.navigate("Bottom_Tab");
        } else {
          setModalLoging(true);
        }
      }
    } catch (error) {
      console.log(error);
      setModalErrorPass(true);
    } finally {
      setLoading(false);
    }
  };
  const postRegister = async () => {
    try {
      setLoading(true);
      const dateTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      const myUserName = await encrypt(isMyPhone);
      const myPhone = await encrypt(isMyPhone);
      const myEmail = await encrypt(isEmail);
      const myPassWord = await encrypt(isPassWord);
      const myConFirmPass = await encrypt(isConfirmPass);
      const myDateTime = await encrypt(dateTime);
      //obj
      const req = {
        userName: myUserName,
        password: myPassWord,
        confirmPassword: myConFirmPass,
        firstName: myUserName,
        mobileNo: myPhone,
        telephoneNo: myPhone,
        email: myEmail,
        dateOfBirth: myDateTime,
        dateOfJoin: myDateTime,
        isAuthenFacebook: true,
        facebookId: "",
        isAuthenGoogle: true,
        googleId: "",
        isApple: true,
        appleId: "",
        isAcceptAgreement: true,
        isAcceptUserName: true,
        isAcceptName: true,
        isAcceptMobileNo: true,
        isAcceptPhoneNo: true,
        isAcceptEmail: true,
        isAcceptLineId: true,
        isAcceptAddress: true,
        isAcceptBirthDate: true,
        isAcceptJoinDate: true,
        isAcceptImageUser: true,
      };

      const obj = await registerApi(req);
      if (obj.message) {
        // setModalRegis(true);
        Alert.alert(t("noti"), t("mobile_again"));
      } else {
        await AsyncStorage.setItem("successLogin", obj.message || "");
        // setModalTrue(true);
        Alert.alert(t("signup_button"), t("success_message"), [
          {
            text: t("login_button"),
            onPress: () => postLogin(),
          },
        ]);
      }
    } catch (error) {
      console.log(error);
      Alert.alert("Error", "An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };
  const signInGoogle = async () => {
    try {
      setLoading(true);
      // ตรวจสอบว่าบริการ Google Play พร้อมใช้งาน
      await GoogleSignin.hasPlayServices();
      // เริ่มต้นเข้าสู่ระบบ
      const userInfoGoogle = await GoogleSignin.signIn();
      const idToken = userInfoGoogle.data?.idToken || "";

      // ตรวจสอบ Token และส่งไปยัง API
      const req = {
        provider: "Google",
        accessToken: idToken,
        acceptDetail: {
          isAcceptAgreement: true,
          isAcceptUserName: true,
          isAcceptName: true,
          isAcceptMobileNo: true,
          isAcceptPhoneNo: true,
          isAcceptEmail: true,
          isAcceptLineId: true,
          isAcceptAddress: true,
          isAcceptBirthDate: true,
          isAcceptJoinDate: true,
          isAcceptImageUser: true,
        },
      };

      const res = await registerThirdparty(req);

      // ตรวจสอบผลลัพธ์
      if (res.message === "Login Successfully") {
        Alert.alert(t("signup_button"), t("success_message"), [
          {
            text: t("login_button"),
            onPress: () => loginGoogle(),
          },
        ]);
      } else {
        Alert.alert(t("noti"), t("login_button"), [
          {
            text: t("cancel"),
            onPress: () => console.log("Cancel Pressed"),
            style: "cancel",
          },
          {
            text: t("login_button"),
            onPress: () => loginGoogle(),
          },
        ]);
      }
    } catch (error) {
      if (error instanceof Error && error.hasOwnProperty("code")) {
        const errorCode = (error as any).code;

        // จัดการข้อผิดพลาดแต่ละกรณี
        switch (errorCode) {
          case statusCodes.SIGN_IN_CANCELLED:
            console.log("User cancelled the sign-in process");
            // หยุดการทำงานโดยไม่เรียก registerThirdparty
            return;

          case statusCodes.IN_PROGRESS:
            console.log("Sign-in is already in progress");
            break;

          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            console.log("Google Play services not available or outdated");
            break;

          default:
            console.log("An unknown error occurred during sign-in:", error);
            break;
        }
      } else {
        console.error("Unexpected error:", error);
      }
    } finally {
      setLoading(false);
    }
  };
  const signInApple = async () => {
    try {
      setLoading(true);
      if (!appleAuth.isSupported) {
        Alert.alert(
          "Error",
          "Sign In with Apple is not supported on this device."
        );
        return;
      }
      const appleAuthResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      const idToken = appleAuthResponse.identityToken || "";

      const { identityToken, email, fullName, user } = appleAuthResponse;
      if (identityToken) {
        // Alert.alert('Success', `User ID: ${user}\nEmail: ${email}`);
        console.log("Success", `User ID: ${user}\nEmail: ${email}`);
        try {
          const req = {
            provider: "Apple",
            accessToken: idToken,
            acceptDetail: {
              isAcceptAgreement: true,
              isAcceptUserName: true,
              isAcceptName: true,
              isAcceptMobileNo: true,
              isAcceptPhoneNo: true,
              isAcceptEmail: true,
              isAcceptLineId: true,
              isAcceptAddress: true,
              isAcceptBirthDate: true,
              isAcceptJoinDate: true,
              isAcceptImageUser: true,
            },
          };

          const dataReq = await registerThirdparty(req);
          console.log("User registered successfully:", dataReq);
          if (dataReq.message === "Login Successfully") {
            Alert.alert(t("signup_button"), t("success_message"), [
              {
                text: t("login_button"),
                onPress: () => loginApple(),
              },
            ]);
          } else {
            Alert.alert(t("noti"), t("login_button"), [
              {
                text: t("cancel"),
                onPress: () => console.log("Cancel Pressed"),
                style: "cancel",
              },
              {
                text: t("login_button"),
                onPress: () => loginApple(),
              },
            ]);
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        Alert.alert("Error", "Failed to retrieve identity token.");
      }
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
      // Alert.alert('Error', 'An error occurred during Sign In with Apple.');
    } finally {
      setLoading(false);
    }
  };

  const loginGoogle = async () => {
    try {
      setLoading(true);
      await GoogleSignin.hasPlayServices();
      const userInfoGoogle = await GoogleSignin.signIn();
      const idToken = userInfoGoogle.data?.idToken || "";
      const req = {
        provider: "Google",
        accessToken: idToken,
      };
      const obj = await logInThirdParty(req);
      await storage.set("successLogin", obj.message || "");
      await AsyncStorage.setItem(
        "userIdLogin",
        obj.model.userInfo.userId || ""
      );
      await AsyncStorage.setItem(
        "tokenLogin",
        obj.model.tokenInfo.access_token || ""
      );
      console.log(JSON.stringify(obj, null, 2));
      if (obj.message === "Login Successfully") {
        //Fcm register
        // const token = await AsyncStorage.getItem("fcmToken");
        // const req = {
        //   userId: "",
        //   deviceTokenId: token,
        // };
        // const rsp = await notiRegister(req);
        // console.log("???????....", rsp);
        navigation.navigate("Bottom_Tab");
      } else {
        Alert.alert(t("noti"), t("error"));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  const loginApple = async () => {
    try {
      setLoading(true);
      if (!appleAuth.isSupported) {
        Alert.alert(
          "Error",
          "Sign In with Apple is not supported on this device."
        );
        return;
      }
      const appleAuthResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      const idToken = appleAuthResponse.identityToken || "";

      const { identityToken, email, fullName, user } = appleAuthResponse;
      if (identityToken) {
        // Alert.alert('Success', `User ID: ${user}\nEmail: ${email}`);
        // console.log("Success", `User ID: ${user}\nEmail: ${email}`);
        try {
          const req = {
            provider: "Apple",
            accessToken: idToken,
          };
          const obj = await logInThirdParty(req);
          // await AsyncStorage.setItem("successLogin", obj.message || "");
          await storage.set("successLogin", obj.message || "");
          await AsyncStorage.setItem(
            "userIdLogin",
            obj.model.userInfo.userId || ""
          );
          await AsyncStorage.setItem(
            "tokenLogin",
            obj.model.tokenInfo.access_token || ""
          );
          console.log("User registered successfully:", obj);
          if (obj.message === "Login Successfully") {
            //Fcm register
            // const token = await AsyncStorage.getItem("fcmToken");
            // const req = {
            //   userId: "",
            //   deviceTokenId: token,
            // };
            // const rsp = await notiRegister(req);
            // console.log("???????....", rsp);
            navigation.navigate("Bottom_Tab");
          } else {
            console.log("Close");
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        Alert.alert("Error", "Failed to retrieve identity token.");
      }
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
      // Alert.alert('Error', 'An error occurred during Sign In with Apple.');
    } finally {
      setLoading(false);
    }
  };

  //Ui
  const contentSingnin = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      {/* <ImageBackground
        source={Images.bgApp}
        style={{ flex: 1 }}
        resizeMode="cover"
      > */}
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={ctn.ctn_RabbitSignUp}>
            <FastImage
              style={img.img_RabbitSignUp}
              source={Images.LogoMeFarmHug}
              resizeMode={FastImage.resizeMode.contain}
            />
          </View>

          {/* {Phome Number} */}
          <View style={ctn.ctn_inPutSignUp}>
            {isMyPhone.length > 0 && (
              <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
                {isMyPhone.length < 10 && isMyPhone.length > 0
                  ? t("incomplete_phone")
                  : ""}
              </Text>
            )}
            <View style={{ margin: moderateScale(2) }} />
            <TextInput
              label={t("phone_nember")}
              value={isMyPhone}
              onChangeText={(text) => setMyPhone(text)}
              mode="outlined"
              contentStyle={[fonstStyle.f12_light]}
              outlineColor="#84B8A2"
              activeOutlineColor="#84B8A2"
              keyboardType="numeric"
              autoCapitalize="none"
              maxLength={10}
              right={<TextInput.Affix text={`${isMyPhone.length}/10`} />}
            />
            <View style={{ margin: moderateScale(2) }} />

            {/* {Email} */}
            {!typeSingnin && !typeGoogle ? (
              <>
                <TextInput
                  label={t("email")}
                  value={isEmail}
                  onChangeText={setEmail}
                  mode="outlined"
                  style={[fonstStyle.f12_light]}
                  outlineColor="#84B8A2"
                  activeOutlineColor="#84B8A2"
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                <View style={{ margin: moderateScale(2) }} />
              </>
            ) : null}

            {/* {Password} */}
            {!typeGoogle ? (
              <>
                {isPassWord.length > 0 && (
                  <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
                    {!isPasswordValid && isPassWord.length > 0
                      ? t("incomplete_password")
                      : ""}
                  </Text>
                )}
                <View style={{ margin: moderateScale(2) }} />
                <TextInput
                  label={t("password")}
                  value={isPassWord}
                  onChangeText={(textPass) => setPassWord(textPass)}
                  mode="outlined"
                  style={[fonstStyle.f12_light]}
                  outlineColor="#84B8A2"
                  activeOutlineColor="#84B8A2"
                  keyboardType="default"
                  autoCapitalize="none"
                  secureTextEntry={!isShowPass}
                />
                <TouchableOpacity
                  style={ctn.ctn_iconEye}
                  onPress={toggleShowPassword}
                >
                  {!isShowPass ? iconEyeClose() : iconEyeOpen()}
                </TouchableOpacity>
                <View style={{ margin: moderateScale(2) }} />
              </>
            ) : null}

            {/* {Password Confirm} */}
            {!typeSingnin && !typeGoogle ? (
              <>
                {isConfirmPass.length > 0 && (
                  <Text style={[txt.txt_PasswordSix, fonstStyle.f10_light]}>
                    {!isConfirmValid && isConfirmPass.length > 0
                      ? t("incomplete_password")
                      : ""}
                    {isPassWord != isConfirmPass ? t("error_password") : ""}
                  </Text>
                )}
                <View style={{ margin: moderateScale(2) }} />
                <TextInput
                  label={t("confirm_password")}
                  value={isConfirmPass}
                  onChangeText={(textConFirm) => {
                    if (textConFirm.length <= 6 || textConFirm.length <= 20) {
                      setConfirmPass(textConFirm);
                    }
                  }}
                  mode="outlined"
                  style={[fonstStyle.f12_light]}
                  outlineColor="#84B8A2"
                  activeOutlineColor="#84B8A2"
                  keyboardType="default"
                  autoCapitalize="none"
                  secureTextEntry={!isShowConFirmPass}
                />
                <TouchableOpacity
                  style={ctn.ctn_iconEye}
                  onPress={toggleShowConFirmPassword}
                >
                  {!isShowConFirmPass ? iconEyeClose() : iconEyeOpen()}
                </TouchableOpacity>
              </>
            ) : null}
          </View>
          {/* Buttom login & Singnin */}
          {typeSingnin ? (
            <View style={{ paddingHorizontal: moderateScale(20) }}>
              <View style={{ margin: moderateScale(10) }} />
              <TouchableOpacity
                style={[btn.btn_SignUp, { opacity: isButtonLogin ? 0.5 : 1 }]}
                disabled={isButtonLogin}
                onPress={() => postLogin()}
              >
                <Text style={[txt.txt_SignUp, fonstStyle.f14_bold]}>
                  {t("login_button")}
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={{ paddingHorizontal: moderateScale(20) }}>
              <View style={{ margin: moderateScale(10) }} />
              <TouchableOpacity
                style={[
                  btn.btn_SignUp,
                  { opacity: isButtonDisabled ? 0.5 : 1 },
                ]}
                disabled={isButtonDisabled}
                onPress={() => (typeSingnin ? postLogin() : postRegister())}
              >
                <Text style={[txt.txt_SignUp, fonstStyle.f14_bold]}>
                  {t("signup_button")}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={{ margin: moderateScale(10) }} />
          <View style={[oth.ling_loging]} />
          <View style={{ alignItems: "center" }}>
            <Text style={[txt.txt_or, fonstStyle.f12_light]}>{t("or")}</Text>
          </View>
          <View style={ctn.ctn_social}>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                onPress={() => (typeSingnin ? loginGoogle() : signInGoogle())}
                style={btn.btn_loginSocial}
              >
                <FastImage
                  style={img.img_GooGle}
                  source={Images.GooGle}
                  resizeMode={FastImage.resizeMode.contain}
                />
              </TouchableOpacity>
              {Platform.OS === "ios" ? (
                <>
                  <View style={{ margin: verticalScale(2) }} />
                  <TouchableOpacity
                    onPress={() => (typeSingnin ? loginApple() : signInApple())}
                    style={btn.btn_loginSocial}
                  >
                    <FastImage
                      style={img.img_Apple}
                      source={Images.Apple}
                      resizeMode={FastImage.resizeMode.contain}
                    />
                  </TouchableOpacity>
                </>
              ) : null}
            </View>
          </View>
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
      {/* </ImageBackground> */}
    </KeyboardAvoidingView>
  );
  const alertFlase = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalFalse}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_medium]}>
                {t("incomplete_message")}
              </Text>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("please_check")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => setModalFalse(false)}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  const alertTrue = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalTrue}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modTrue, fonstStyle.f14_light]}>
                {t("signup_button")}
              </Text>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_medium]}>
                {t("success_message")}
              </Text>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_light]}>
                {t("building_farm")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => goHome()}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("login_button")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  const alertError = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalError}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_medium]}>
                {isMyPhone.length < 10
                  ? t("incomplete_phone")
                  : isPassWord.length < 6 || isConfirmPass.length < 6
                  ? t("incomplete_password")
                  : t("password_incorrect")}
              </Text>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("please_check")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => setModalError(false)}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  const alertPassword = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalErrorPass}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_medium]}>
                {t("error_password")}
              </Text>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("please_check")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => setModalErrorPass(false)}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  const alertRegistration = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalRegis}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_medium]}>
                {t("registration_error")}
              </Text>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("please_check")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => setModalRegis(false)}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  const alertErrorLoing = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalLoging}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_medium]}>
                {t("loging_error")}
              </Text>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("please_check")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseLoging]}
              onPress={() => setModalLoging(false)}
            >
              <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {isLoading ? <Loading /> : null}
      <Default_Bar
        onBack={() => navigation.goBack()}
        paRams={typeSingnin}
        title={typeSingnin ? t("login_button") : t("create_account")}
      />
      {contentSingnin()}
      {alertFlase()}
      {alertTrue()}
      {alertError()}
      {alertRegistration()}
      {alertErrorLoing()}
      {alertPassword()}
    </>
  );
}
