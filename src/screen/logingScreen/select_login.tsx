import {
  Text,
  View,
  Image,
  StatusBar,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
} from "react-native";
import React from "react";
import FastImage from "react-native-fast-image";
import { verticalScale } from "react-native-size-matters";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import fonstStyle from "../../styleSheet/style_Custom";
import Images from "../../utils/imageManager";
//Translation
import { useTranslation } from "../i18n";

export default function Select_login({ navigation }: any) {
  //Translation
  const { t } = useTranslation();
  //Function
  const handlePress = (buttonType: any) => {
    if (buttonType === "register") {
      navigation.navigate("Singnin", { typeRegis: buttonType });
    } else if (buttonType === "signin") {
      navigation.navigate("Singnin", { typeSingnin: buttonType });
    }
  };

  //UiLogoMeFarmHug
  const contentSelect = () => (
    <ImageBackground
      source={Images.bgApp}
      style={{ flex: 1 }}
      resizeMode="cover"
    >
      <View style={{flex: 1}}>
        <View style={ctn.ctn_logoLoging}>
          <FastImage
            style={{ width: 250, height: 40 }}
            source={Images.textStartMefarm}
            resizeMode={FastImage.resizeMode.cover}
          />
          <View style={{ margin: verticalScale(20) }} />
          <FastImage
            style={img.img_Rabbit}
            source={Images.LogoMeFarmHug}
            resizeMode={FastImage.resizeMode.contain}
          />
        </View>
        <View style={ctn.ctn_btmLoging}>
          <ScrollView>
            <TouchableOpacity
              style={btn.btn_Login}
              onPress={() => handlePress("signin")}
            >
              <Text style={[txt.txt_Singnin, fonstStyle.f14_bold]}>
                {t("login_button")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: verticalScale(5) }} />
            <TouchableOpacity
              style={btn.btn_Register}
              onPress={() => handlePress("register")}
            >
              <Text style={[txt.txt_Register, fonstStyle.f14_bold]}>
                {t("signup_button")}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </ImageBackground>
  );

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {contentSelect()}
    </>
  );
}
