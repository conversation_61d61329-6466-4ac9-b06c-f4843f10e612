import {
  View,
  Text,
  Modal,
  Image,
  FlatList,
  Platform,
  TextInput,
  StatusBar,
  ScrollView,
  Dimensions,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from "react-native";
import moment from "moment";
import { Badge } from "@rneui/themed";
import Svg, { Circle } from "react-native-svg";
import FastImage from "react-native-fast-image";
import DatePicker from "react-native-date-picker";
import DeviceInfo from "react-native-device-info";
import LinearGradient from "react-native-linear-gradient";
import { useFocusEffect } from "@react-navigation/native";
import AppIntroSlider from "react-native-app-intro-slider";
import Orientation from "react-native-orientation-locker";
import React, { useEffect, useState, useRef, useMemo } from "react";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { verticalScale, moderateScale } from "react-native-size-matters";
import MapView, { <PERSON><PERSON>, <PERSON>ygon, PROVIDER_GOOGLE } from "react-native-maps";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import map from "../../styleSheet/map";
import fonstStyle, {
  BgColor,
  LgColor,
  colorsPH,
  BgOpacity,
  linearDis,
  linearSky,
  linearPink,
  lineOrange,
  linearGreen,
  linearPurple,
  linearDanger,
} from "../../styleSheet/style_Custom";
//Svg
import {
  iconEdit,
  iconPlant,
  iconNoImg,
  iconBanking,
  iconStartEnd,
  iconPlusPlant,
  iconUpProduct,
  iconCheckArea,
  iconDeleteFarm,
  iconScreenPlus,
  iconDownProduct,
  iconScreenClose,
  iconSaveFarmName,
  iconEditFarmName,
  iconBankingChang,
  iconButtomMarker,
  iconDeleteOrange,
  iconDeletePayment,
  iconCancleFarmName,
  iconLocationMarker,
  iconSavePoltName,
  iconCanclePoltName,
  iconEditPoltName,
} from "../../assets/svg/svg_other";
import { goBack_Bg } from "../../assets/svg/svg_naviagte";
//Api
import {
  seedApi,
  getWaitList,
  plantingApi,
  plantOverlap,
  deleteInvoice,
  deleteAreaApi,
  getPayMentApi,
  updatePlotName,
  getMyFarmDetail,
  confirmPlanting,
} from "../../action/Mefarm_Farm_API";
import {
  waterState,
  wateringApi,
  deleteConfig,
  wateringAddApi,
  updateWaterApi,
  wateringStatus,
} from "../../action/Mefarm_IoT_API";
//Components
import Images from "../../utils/imageManager";
import SelectChat from "../../components/chat/select-chat";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import LoadingFarm from "../../components/loading/loadingFarm";
import { pusCountFarm } from "../../action/Mefarm_Realtime_API";
import EditPoltname from "../../components/manageFarm/editPoltname";
import PayMentManage from "../../components/payMent/payMentManage";
// import { ModalDetailArea } from "../../components/modal/modal";
//Translation
import { useTranslation } from "../i18n";
//Redux
import {
  setRegionData,
  setPolygonData,
  setFarmUserPlotId,
  setPolygonsPlanting,
  setNotificationCountFram,
} from "./../../Redux_Store/action";
import { useDispatch, useSelector } from "react-redux";

export default function ManageFarmUser({ navigation, route }: any) {
  const { t } = useTranslation();
  const orientation = useOrientation();
  const isTablet = DeviceInfo.isTablet();
  //Route
  const params = route.params || "";
  const docDetailMyfarm = params.docDetailMyfarm || "";
  const isPages = params.isPages || "";
  //Rudux
  const dispatch = useDispatch();
  const notificationCountFram = useSelector(
    (state: any) => state.notificationCountFram
  );

  const region = useSelector((state: any) => state.region);
  const polygon = useSelector((state: any) => state.polygon);
  const polygonsPlanting = useSelector((state: any) => state.polygons);
  const farmUserPlotId = useSelector((state: any) => state.farmUserPlotId);

  //Array - Optimized with memory limits
  const [finish, setFinish] = useState<any>([]);
  const [service, setService] = useState<any>([]);
  const [docList, setDocList] = useState<any>([]);
  const [waiting, setWaiting] = useState<any>([]);
  const [harvest, setHarvest] = useState<any>([]);
  const [polygons, setPolygons] = useState<any>([]);
  const [waitList, setWaitList] = useState<any>([]);
  const [plotDetail, setPlotDetail] = useState<any>([]);
  const [inProgress, setInprogress] = useState<any>([]);
  const [docSeedlist, setDocSeedList] = useState<any>([]);
  const [coordinates, setCoordinates] = useState<any>([]);
  const [listWatering, setListWatering] = useState<any>([]);
  const [docSelectPlans, setDocSelectPlans] = useState<any>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [selectedManage, setSelectedManage] = useState<any[]>([]);
  const [waterListStatus, setWaterListStatus] = useState<any>([]);
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [confirmList, setConfirmList] = useState<boolean[]>([]);

  // Memory management constants
  const MAX_WATER_STATUS_ITEMS = 100;
  const MAX_WAIT_LIST_ITEMS = 50;
  const MAX_COORDINATES = 200;

  // Memory cleanup function
  const performMemoryCleanup = () => {
    // Limit array sizes
    if (waterListStatus.length > MAX_WATER_STATUS_ITEMS) {
      setWaterListStatus((prev: any) => prev.slice(0, MAX_WATER_STATUS_ITEMS));
    }
    if (waitList.length > MAX_WAIT_LIST_ITEMS) {
      setWaitList((prev: any) => prev.slice(0, MAX_WAIT_LIST_ITEMS));
    }
    if (coordinates.length > MAX_COORDINATES) {
      setCoordinates((prev: any) => prev.slice(0, MAX_COORDINATES));
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  };
  //OBJ
  const [paymentData, setPaymentData] = useState<any>({});
  //Null
  const mapRef = useRef<MapView>(null);
  const [imageUrl, setImageUrl] = useState<any>(null);
  const [imageEdit, setImageEdit] = useState<any>(null);
  const [centroidCen, setCentroid] = useState<any>(null);
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const [openIndexPay, setOpenIndexPay] = useState<number | null>(null);
  const [openIndexPayMent, setOpenIndexPayMent] = useState<number | null>(null);
  const [openIndexProduct, setIndexProduct] = useState<number | null>(null);
  //True & False
  const [open, setOpen] = useState<boolean>(false);
  const [isPlus, setPlus] = useState<boolean>(false);
  const [isDrag, setDrag] = useState<boolean>(false);
  const [Intro, setIntro] = useState<boolean>(false);
  const [openEnd, setOpenEnd] = useState<boolean>(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [mapReady, setMapReady] = useState<boolean>(false);
  const [isChatFarm, setChatFarm] = useState<boolean>(false);
  const [isWatering, setWatering] = useState<boolean>(false);
  const [modalOutArea, setOutArea] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [modalError, setModalError] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [isMenuPlant, setMenuPlant] = useState<boolean>(false);
  const [isModalMenu, setModalMenu] = useState<boolean>(false);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  const [polygonMode, setPolygonMode] = useState<boolean>(false);
  const [modalDetail, setModalDetail] = useState<boolean>(false);
  const [modalManage, setModalManage] = useState<boolean>(false);
  const [modalInFrom, setModalInFrom] = useState<boolean>(false);
  const [isManagement, setManagement] = useState<boolean>(false);
  const [isMenuHeader, setMenuHeader] = useState<boolean>(false);
  const [modalSaveName, setModalSaveName] = useState<boolean>(false);
  const [isEditFramName, setEditFramName] = useState<boolean>(false);
  const [loadingDelete, setLoadingDelete] = useState<boolean>(false);
  const [modalSaveArea, setModalSaveArea] = useState<boolean>(false);
  const [modalMaxPlans, setModalMaxPlans] = useState<boolean>(false);
  const [modalBuildArea, setModalBuildArea] = useState<boolean>(false);
  const [modalDetailArea, setModalDetailArea] = useState<boolean>(false);
  const [modalOverPolygon, setModalOverPolygon] = useState<boolean>(false);
  const [modalSelectPlans, setModalSelectPlans] = useState<boolean>(false);
  const [modalDeleteConfig, setModalDelectConfig] = useState<boolean>(false);
  const [modalPlantingNoti, setModalPlantingNoti] = useState<boolean>(false);
  //Number
  const [value, setValue] = useState<any>(0);
  const [humidity, setHumidity] = useState<any>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [activeTools, setActiveTools] = useState<any>(1);
  const [maxPlanting, setMaxPlanting] = useState<any>(0);
  const [temperature, setTemperature] = useState<any>(0);
  const [activeStutes, setActiveStutes] = useState<any>(3);
  const [activeButton, setActiveButton] = useState<any>(0);
  const [activeManage, setActiveManage] = useState<any>(1);
  const [forceReload, setForceReload] = useState<number>(0);
  const [totalPlanting, setTotalPlanting] = useState<any>(0);
  const [activePay, setActivePay] = useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  //String
  const [n, setN] = useState<string>("");
  const [p, setP] = useState<string>("");
  const [k, setK] = useState<string>("");
  const [ph, setPh] = useState<string>("");
  const [edit, setEdit] = useState<string>("");
  const [endNow, setEndNow] = useState<string>("");
  const [idEdit, setIdEdit] = useState<string>("");
  const [endTime, setEndTime] = useState<string>("");
  const [startNow, setStartNow] = useState<string>("");
  const [editArea, setEditArea] = useState<string>("");
  const [sunlight, setSunlight] = useState<string>("");
  const [plotName, setPlotName] = useState<string>("");
  const [farmName, setFarmName] = useState<string>("");
  const [stutesNow, setStutesNow] = useState<string>("");
  const [startTime, setStartTime] = useState<string>("");
  const [stutesWater, setStutesWater] = useState<string>("");
  const [idDeleteArea, setDeleteArea] = useState<string>("");
  const [framPlotName, setFramPlotName] = useState<string>("");
  const [idSelectPlans, setSelectPlans] = useState<string>("");
  const [checkAreaPant, setCheckAreaPant] = useState<string>("");
  const [selectPlansName, setSelectPlansName] = useState<string>("");
  const [farmPlotDeviceId, setFarmPlotDeviceId] = useState<string>("");
  const [farmUserPlotIdEdit, setFarmUserPlotIdEdit] = useState<string>("");
  const [plantingProcessTypeIdEdit, setplantingProcessTypeIdEdit] =
    useState<string>("");
  //Day
  const initialDate = new Date();
  initialDate.setHours(0, 0, 0, 0);
  const [date, setDate] = useState<Date>(initialDate);
  const [dateEnd, setDateEnd] = useState<Date>(initialDate);
  const [isMonday, setIsMonday] = useState(false);
  const [isTuesday, setIsTuesday] = useState(false);
  const [isWednesday, setIsWednesday] = useState(false);
  const [isThursday, setIsThursday] = useState(false);
  const [isFriday, setIsFriday] = useState(false);
  const [isSaturday, setIsSaturday] = useState(false);
  const [isSunday, setIsSunday] = useState(false);
  //State Countdown
  const [countdown, setCountdown] = useState(0);
  const startCountdown = () => {
    setCountdown(15);
  };
  const getRemainingTime = () => {
    const now = moment();
    const end = moment(endTime || endNow);
    return end.diff(now, "seconds");
  };
  const { width, height } = Dimensions.get("window");
  const [remainingTime, setRemainingTime] = useState(getRemainingTime());
  const bottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ["50%", "80%"], []);
  const snapPointsComfirm = useMemo(() => ["50%", "80%"], []);
  const size = 70;
  const strokeWidth = 8;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const progress = (temperature / 70) * circumference;

  const slides = [
    {
      key: "1",
      title: "ต้อนรับ",
      text: "แอปของเราใช้งานง่ายและสะดวกมาก!",
      // image: require("../assets/intro1.png"),
      backgroundColor: "#22bcb5",
    },
    {
      key: "2",
      title: "ฟาร์ม",
      text: "ในฟาร์มได้แบบลไทม์",
      // image: require("../assets/intro2.png"),
      backgroundColor: "#3395ff",
    },
    {
      key: "3",
      title: "เริ่มต้นเลย!",
      text: "พร้อมแล้วใช่ไหม? ไปเลย",
      // image: require("../assets/intro3.png"),
      backgroundColor: "#febe29",
    },
  ];
  const days = [
    {
      name: t("Mon"),
      isSelected: isMonday,
      toggle: () => setIsMonday((prev) => !prev),
    },
    {
      name: t("Tue"),
      isSelected: isTuesday,
      toggle: () => setIsTuesday((prev) => !prev),
    },
    {
      name: t("Wed"),
      isSelected: isWednesday,
      toggle: () => setIsWednesday((prev) => !prev),
    },
    {
      name: t("Thu"),
      isSelected: isThursday,
      toggle: () => setIsThursday((prev) => !prev),
    },
    {
      name: t("Fri"),
      isSelected: isFriday,
      toggle: () => setIsFriday((prev) => !prev),
    },
    {
      name: t("Sat"),
      isSelected: isSaturday,
      toggle: () => setIsSaturday((prev) => !prev),
    },
    {
      name: t("Sun"),
      isSelected: isSunday,
      toggle: () => setIsSunday((prev) => !prev),
    },
  ];
  const isSaveDisabled =
    !date ||
    !dateEnd ||
    !(
      isMonday ||
      isTuesday ||
      isWednesday ||
      isThursday ||
      isFriday ||
      isSaturday ||
      isSunday
    );

  //InitialState
  const initialState = {
    region: {
      latitude: 0,
      longitude: 0,
      latitudeDelta: 0.0005,
      longitudeDelta: 0.0005,
    },
    polygon: [],
    polygons: [],
  };
  interface Coordinate {
    latitude: number;
    longitude: number;
  }

  useEffect(() => {
    if (activeButton === 1) {
      Orientation.lockToPortrait(); // ล็อกแนวตั้ง
    } else {
      Orientation.unlockAllOrientations(); // ปลดล็อก ใช้ได้ทั้งหมด
    }
  }, [activeButton]);
  useFocusEffect(
    React.useCallback(() => {
      const loadData = async () => {
        dataDocDetail();
        callSeed();
      };

      loadData();

      return () => {
        dispatch(setRegionData(initialState.region));
        dispatch(setPolygonData(initialState.polygon));
        dispatch(setPolygonsPlanting(initialState.polygons));
      };
    }, [dispatch])
  );
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!mapReady) {
        setForceReload((prev) => prev + 1);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [mapReady, forceReload]);
  useEffect(() => {
    if (countdown === 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(timer);
          callWaterState();
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);
  useEffect(() => {
    if (stutesWater !== "In Progress") return;

    const interval = setInterval(() => {
      const diff = getRemainingTime();

      if (diff <= 0) {
        clearInterval(interval);
        setStutesWater("");
      } else {
        setRemainingTime(diff);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [endTime, endNow, stutesWater]);
  useEffect(() => {
    const callPayMent = async () => {
      try {
        setLoadIng(true);
        const response = await getPayMentApi();
        const payMentData = response.model || "";
        // console.log(JSON.stringify(payMentData, null, 2));
        setPaymentMethods(payMentData);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    };
    callPayMent();
  }, []);
  useEffect(() => {
    const checkIntroStatus = async () => {
      const hasSeenIntro = await AsyncStorage.getItem("hasSeenIntro");

      if (hasSeenIntro === "true") {
        setIntro(false); // เคยเห็น ข้ามไปหน้า Home
      } else if (isPages === "MaketFram") {
        setIntro(false);
      } else {
        setIntro(true); // ไม่เคย แสดง Intro
      }
    };
    checkIntroStatus();
  }, []);
  const handleReload = (deletedId: any) => {
    setLoadingDelete(true);
    const timeoutId = setTimeout(() => {
      const updatedData = listWatering.filter(
        (item: any) => item.id !== deletedId
      );
      setListWatering(updatedData);
      setLoadingDelete(false);
    }, 2000); // ลดเวลาจาก 5000 เป็น 2000

    // Cleanup timeout if component unmounts
    return () => clearTimeout(timeoutId);
  };

  const handleReloadPay = (deletedId: any) => {
    setLoadingDelete(true);
    const timeoutId = setTimeout(() => {
      const updatedDate = waitList
        .slice(0, MAX_WAIT_LIST_ITEMS)
        .filter((item: any) => item.invoiceNumber !== deletedId);
      setWaitList(updatedDate);
      dataDocDetail();
      setLoadingDelete(false);
    }, 2000); // ลดเวลาจาก 5000 เป็น 2000

    // Cleanup timeout if component unmounts
    return () => clearTimeout(timeoutId);
  };
  const refreshWateringData = async () => {
    setLoadingDelete(true);
    try {
      const response = await getMyFarmDetail(farmUserPlotId);
      const newData = response?.model?.management?.watering || [];
      setListWatering(newData);
      callWaterListStatus();
    } catch (error) {
      console.error("Error fetching new data:", error);
    } finally {
      setLoadingDelete(false);
    }
  };
  const dataDocDetail = async () => {
    try {
      // ดึงข้อมูล userId จาก AsyncStorage
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";

      // ดึงข้อมูลจาก route params
      const params = route.params || {};
      const docDetailMyfarm = params.docDetailMyfarm || {};
      // const farmUserPlotId = docDetailMyfarm.id || "";
      const farmUserPlotId =
        docDetailMyfarm && docDetailMyfarm.id ? docDetailMyfarm.id : "";
      // console.log(JSON.stringify(farmUserPlotId, null, 2));

      // ตรวจสอบว่ามี farmUserPlotId หรือไม่
      if (!farmUserPlotId) {
        console.error("Missing farmUserPlotId");
        setLoadIng(false);
        return;
      }

      // บันทึก farmUserPlotId ลง Redux
      dispatch(setFarmUserPlotId(farmUserPlotId));

      // ดึงข้อมูลฟาร์มและการแจ้งเตือนพร้อมกัน
      const [farmResponse, notificationResponse] = await Promise.all([
        getMyFarmDetail(farmUserPlotId),
        pusCountFarm(userIdLogin, farmUserPlotId),
      ]);

      // ตรวจสอบการตอบกลับ
      if (!farmResponse?.success) {
        console.error("Failed to fetch farm details:", farmResponse);
        setLoadIng(false);
        return;
      }

      // ข้อมูลการแจ้งเตือน
      const totalNotifications = notificationResponse?.model || 0;
      dispatch(setNotificationCountFram(totalNotifications));

      // ข้อมูลฟาร์ม
      const docDetail = farmResponse.model || {};

      // ข้อมูลพื้นฐาน
      setDocList(docDetail);
      setFarmName(docDetail.farmName || "");
      setPlotName(docDetail.plotName || "");
      setWatering(docDetail.isWatering || false);
      setManagement(docDetail.isManagement || false);
      setMaxPlanting(docDetail.maxPlanting || 0);
      setTotalPlanting(docDetail.totalPlanting || 0);
      //  console.log(JSON.stringify(docDetail, null, 2));

      // ข้อมูลการรดน้ำ
      const wateringState = docDetail.management?.wateringState || {};
      setStutesWater(wateringState.state || "");
      setStartTime(wateringState.startTime || "");
      setEndTime(wateringState.endTime || "");

      // ข้อมูลความชื้นและสภาพแวดล้อม
      const plotDetail = docDetail.plotDetail || {};
      setFarmPlotDeviceId(plotDetail.farmPlotDeviceId || "");
      setHumidity(plotDetail.humidity || 0);
      setTemperature(plotDetail.temperature || 0);
      setSunlight(plotDetail.sunlight || 0);
      setPh(plotDetail.ph || 0);
      setN(plotDetail.n || 0);
      setP(plotDetail.p || 0);
      setK(plotDetail.k || 0);
      setValue(plotDetail.ph || 0);

      // ข้อมูลพิกัด (Geometries)
      const geometries = docDetail.geometries || [];
      if (Array.isArray(geometries) && geometries.length > 0) {
        try {
          const newPolygon = geometries.map((mapData) => ({
            latitude: Number(mapData.lat),
            longitude: Number(mapData.lng),
          }));

          // ตรวจสอบความถูกต้องของพิกัด
          if (isNaN(newPolygon[0].latitude) || isNaN(newPolygon[0].longitude)) {
            throw new Error("Invalid coordinates");
          }

          if (newPolygon.length > 0) {
            const initialRegion = {
              latitude: newPolygon[0].latitude,
              longitude: newPolygon[0].longitude,
              latitudeDelta: 0.0005,
              longitudeDelta: 0.0005,
            };
            dispatch(setRegionData(initialRegion));
            dispatch(setPolygonData(newPolygon));
          }
        } catch (error) {
          console.error("Error processing geometries:", error);
        }
      }

      // ข้อมูลการปลูก (Planting Geometries)
      const planting = Array.isArray(docDetail.planting)
        ? docDetail.planting
        : [];

      try {
        const polygons = planting.map((item: any) => {
          // ตรวจสอบว่ามี geometries หรือไม่
          if (!Array.isArray(item.geometries) || item.geometries.length === 0) {
            return {
              coordinates: [],
              id: item.id || "",
              imageUrl: item.imageUrl || null,
              plantName: item.plantName || "",
              isCanEdit: item.isCanEdit || false,
              plantType: item.plantType || "",
            };
          }

          // แปลงพิกัดให้อยู่ในรูปแบบที่ถูกต้อง
          const coordinates = item.geometries.map((mapData: any) => ({
            latitude: Number(mapData.lat),
            longitude: Number(mapData.lng),
          }));

          return {
            coordinates,
            id: item.id || "",
            imageUrl: item.imageUrl || null,
            plantName: item.plantName || "",
            isCanEdit: item.isCanEdit || false,
            plantType: item.plantType || "",
          };
        });

        dispatch(setPolygonsPlanting(polygons));
      } catch (error) {
        console.error("Error processing planting geometries:", error);
        dispatch(setPolygonsPlanting([]));
      }
    } catch (error) {
      console.error("Error in dataDocDetail:", error);
    }
  };
  const callSeed = async () => {
    try {
      setLoadIng(true);
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await seedApi(req);
      const seedData = response.model || "";
      setDocSeedList(seedData);
      // console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callMangeCovert = async () => {
    try {
      setLoadIng(true);
      const docMangeCovert = docList || "";
      const management = docMangeCovert.management || "";
      const service = management.service || "";
      setService(service);
      // console.log(JSON.stringify(service, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callPlotDetail = async () => {
    try {
      setLoadIng(true);
      const plantList = docList.planting || "";
      const confirmList = plantList.map((plant: any) => plant.isConfirm);
      setPlotDetail(plantList);
      setConfirmList(confirmList);
      // console.log(JSON.stringify(plantList, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callWater = async () => {
    try {
      setLoadIng(true);
      const docMangeCovert = docList || "";
      const management = docMangeCovert.management || "";
      const watering = management.watering || "";
      setListWatering(watering);
      // console.log(JSON.stringify(watering, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callWaterListStatus = async () => {
    try {
      setLoadIng(true);
      const rsp = await wateringStatus(farmUserPlotId, pageSize);
      const data = rsp.model || "";
      setWaterListStatus(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callHarvest = async () => {
    try {
      setLoadIng(true);
      const docMangeCovert = docList || "";
      const management = docMangeCovert.management || "";
      const harvest = management.harvest || "";
      setHarvest(harvest);
      // console.log(JSON.stringify(harvest, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callStatus = async () => {
    try {
      setLoadIng(true);
      const docMangeCovert = docList || "";
      const management = docMangeCovert.management || "";
      const status = management.status || "";
      const inProgress = status.inProgress || "";
      const waiting = status.waiting || "";
      const finish = status.finish || "";
      // setHarvest(harvest);
      setInprogress(inProgress);
      setWaiting(waiting);
      setFinish(finish);
      // console.log(JSON.stringify(inProgress, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callwateringIot = async () => {
    try {
      setLoadIng(true);
      const response = await wateringApi(farmUserPlotId);
      // console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callWaterState = async () => {
    try {
      setLoadIng(true);
      const res = await waterState(farmUserPlotId);
      const data = res.model || "";
      const stutes = data.state || "";
      const startTime = data.startTime || "";
      const endTime = data.endTime || "";
      dataDocDetail();
      setStutesNow(stutes);
      setStartNow(startTime);
      setEndNow(endTime);
    } catch (error) {
    } finally {
      setLoadIng(false);
    }
  };
  const callDeleteArea = async () => {
    try {
      setLoadIng(true);
      const response = await deleteAreaApi(idDeleteArea);
      // console.log("response...", response);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setModalDelete(false);
      setModalDetailArea(false);
      setActiveButton(null);
      onRefresh();
    }
  };
  const callConfirmPlanting = async () => {
    try {
      setLoadIng(true);
      const response = await confirmPlanting(farmUserPlotId);
      if (response?.success) {
        setModalPlantingNoti(false);
        setActiveButton(null);
        onRefresh();
      } else {
        setModalError(true);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callWaitList = async () => {
    try {
      setLoadIng(true);
      const response = await getWaitList(farmUserPlotId);
      const waitListData = response.model || "";
      const noti = waitListData.length;
      setWaitList(waitListData);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setModalPlantingNoti(false);
      onRefresh();
    }
  };
  const callDeleteInvoice = async (item: any) => {
    try {
      setLoadIng(true);
      const response = await deleteInvoice(item.invoiceNumber);
      if (response?.success) {
        await handleReloadPay(item.invoiceNumber);
      } else {
        setModalError(true);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setOpenIndexPay(null);
    }
  };
  const callDeleteConfig = async (item: any) => {
    try {
      setLoadIng(true);
      const res = await deleteConfig(item.id);
      console.log(res);

      if (res?.success) {
        await handleReload(item.id);
      } else {
        setModalError(true);
      }
    } catch (error) {
      console.error("Error deleting config:", error);
    } finally {
      setLoadIng(false);
      setOpenIndex(null);
    }
  };
  const callSetTimeWater = async () => {
    if (edit == "typeEdit") {
      try {
        setLoadIng(true);
        const req = {
          farmUserPlotId: farmUserPlotId,
          configs: [
            {
              id: idEdit,
              farmUserPlotId: farmUserPlotId,
              masterPlantingProcessTypeId: plantingProcessTypeIdEdit,
              startTime: moment(date).format("HH:mm:ss"),
              endTime: moment(dateEnd).format("HH:mm:ss"),
              monday: isMonday,
              tuesday: isTuesday,
              wednesday: isWednesday,
              thursday: isThursday,
              friday: isFriday,
              saturday: isSaturday,
              sunday: isSunday,
            },
          ],
        };
        console.log("edit...", req);
        const res = await updateWaterApi(req);
        console.log(res);
        if (res?.success) {
          setListWatering((prev: any) => [...prev, req]);
          await refreshWateringData();
        } else {
          setModalError(true);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
        setPlus(false);
        setIsMonday(false);
        setIsTuesday(false);
        setIsWednesday(false);
        setIsThursday(false);
        setIsFriday(false);
        setIsSaturday(false);
        setIsSunday(false);
        setEdit("");
        setDate(initialDate);
        setDateEnd(initialDate);
      }
    } else {
      try {
        setLoadIng(true);
        const req = {
          farmUserPlotId: farmUserPlotId,
          configs: [
            {
              id: "",
              farmUserPlotId: farmUserPlotId,
              masterPlantingProcessTypeId: "",
              startTime: moment(date).format("HH:mm:ss"),
              endTime: moment(dateEnd).format("HH:mm:ss"),
              monday: isMonday,
              tuesday: isTuesday,
              wednesday: isWednesday,
              thursday: isThursday,
              friday: isFriday,
              saturday: isSaturday,
              sunday: isSunday,
            },
          ],
        };
        const res = await wateringAddApi(req);
        // console.log("res....", res);
        if (res?.success) {
          setListWatering((prev: any) => [...prev, req]);
          await refreshWateringData();
        } else {
          setModalError(true);
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoadIng(false);
        setPlus(false);
        setIsMonday(false);
        setIsTuesday(false);
        setIsWednesday(false);
        setIsThursday(false);
        setIsFriday(false);
        setIsSaturday(false);
        setIsSunday(false);
        setEdit("");
        setDate(initialDate);
        setDateEnd(initialDate);
      }
    }
  };
  const callUpdatePlotName = async () => {
    try {
      setLoadIng(true);
      const res = {
        farmUserPlotId: farmUserPlotId,
        plotNameTh: framPlotName,
        plotNameEn: "",
      };
      const req = await updatePlotName(res);
      const response = await getMyFarmDetail(farmUserPlotId);
      const docDetail = response.model || "";
      const plotName = docDetail.plotName || "";
      setPlotName(plotName);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setModalSaveName(false);
      setFramPlotName("");
      setEditFramName(!isEditFramName);
    }
  };
  const isPointInPolygon = (point: Coordinate, polygon: any[]): boolean => {
    let isInside = false;
    const { latitude, longitude } = point;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i].latitude,
        yi = polygon[i].longitude;
      const xj = polygon[j].latitude,
        yj = polygon[j].longitude;

      const intersect =
        yi > longitude !== yj > longitude &&
        latitude < ((xj - xi) * (longitude - yi)) / (yj - yi) + xi;

      if (intersect) isInside = !isInside;
    }

    return isInside;
  };
  const handleMapPress = (event: {
    nativeEvent: { coordinate: Coordinate };
  }) => {
    // Destructure coordinate immediately to avoid synthetic event pooling issues
    const coordinate = event?.nativeEvent?.coordinate;
    if (!coordinate) return;

    const { latitude, longitude } = coordinate;
    const point: Coordinate = { latitude, longitude };

    if (activeButton === 2) {
      if (maxPlanting === 0) {
        setModalMaxPlans(true);
        return;
      }

      if (!polygon || polygon.length === 0) {
        console.log("Polygon is not defined");
        return;
      }

      if (coordinates.length === 0) {
        if (isPointInPolygon(point, polygon)) {
          setCoordinates([...coordinates, point]);
        } else {
          setOutArea(true);
        }
      } else {
        if (isPointInPolygon(point, polygon)) {
          setCoordinates([...coordinates, point]);
        } else {
          setOutArea(true);
        }
      }
    }
  };
  const handleMarkerDragEnd = (index: number, e: any) => {
    const { coordinate } = e.nativeEvent;
    const newCoordinates = [...coordinates];
    newCoordinates[index] = coordinate;
    setCoordinates(newCoordinates);
  };
  const handleCleanPolygon = () => {
    setDocSelectPlans([]);
    selectPlans([]);
    if (coordinates.length > 1) {
      setCoordinates(coordinates.slice(0, -1));
    } else if (coordinates.length === 1) {
      setCoordinates([]);
      setPolygons(polygons.slice(0, -1));
    }
  };
  const handleCleanArea = () => {
    setCoordinates([]);
    selectPlans([]);
  };
  const handleButtonClick = (buttonIndex: any) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };
  const handleButtonTool = (buttonIndex: any) => {
    if (activeTools === buttonIndex) {
      setActiveTools(null);
    } else {
      setActiveTools(buttonIndex);
    }
  };
  const handleButtonManage = (buttonIndex: any) => {
    if (activeManage === buttonIndex) {
      setActiveManage(null);
    } else {
      setActiveManage(buttonIndex);
    }
  };
  const handleButtonStutes = (buttonIndex: any) => {
    if (activeStutes === buttonIndex) {
      setActiveStutes(null);
    } else {
      setActiveStutes(buttonIndex);
    }
  };
  const handleReturnToRegion = () => {
    if (mapRef.current) {
      mapRef.current.animateToRegion(region, 1000);
    }
  };
  const toggleManage = (item: any) => {
    setSelectedManage((prevSelectedItems) => {
      const itemExists = prevSelectedItems.some(
        (selectedItem) => selectedItem.id === item.id
      );

      if (itemExists) {
        const updatedItems = prevSelectedItems.filter(
          (selectedItem) => selectedItem.id !== item.id
        );
        console.log(JSON.stringify(updatedItems, null, 2));
        return updatedItems;
      } else {
        const updatedItems = [...prevSelectedItems, item];
        return updatedItems;
      }
    });
  };
  const toggleProduct = (item: any) => {
    setSelectedItems((prevSelectedItems) => {
      const itemExists = prevSelectedItems.some(
        (selectedItem) => selectedItem.id === item.id
      );

      if (itemExists) {
        const updatedItems = prevSelectedItems.filter(
          (selectedItem) => selectedItem.id !== item.id
        );
        return updatedItems;
      } else {
        const updatedItems = [...prevSelectedItems, item];
        return updatedItems;
      }
    });
  };
  const toggleDelete = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const toggleDeletePayment = (index: number) => {
    setOpenIndexPayMent(null);
    setOpenIndexPay(openIndexPay === index ? null : index);
  };
  const togglePayment = (index: number) => {
    setOpenIndexPay(null);
    setOpenIndexPayMent(openIndexPayMent === index ? null : index);
  };
  const toggleOpenProduct = (index: number) => {
    setOpenIndexPay(null);
    setOpenIndexPayMent(null);
    setIndexProduct(openIndexProduct === index ? null : index);
  };
  const selectPlans = (item: any) => {
    setDocSelectPlans(item);
    setSelectPlans(item.id);
    setImageUrl(item.imageUrl);
    setImageEdit(item.imageUrl);
    setSelectPlansName(item.plantName);
    setModalSelectPlans(false);
  };
  const cleanDocSelect = () => {
    setDocSelectPlans([]);
    setSelectPlans("");
    setImageUrl(null);
    setSelectPlansName("");
    setModalSelectPlans(false);
  };
  const selectArea = (item: any) => {
    setCheckAreaPant(item.id);
  };
  const cleanArea = () => {
    setCheckAreaPant("");
  };
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    // Destructure nativeEvent immediately to avoid synthetic event pooling issues
    const nativeEvent = event.nativeEvent;
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;

    const isScrolledToEnd =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (isScrolledToEnd && !isLoadPosts) {
      loadMoreData();
      callWaterListStatus();
    }
  };
  const loadMoreData = async () => {
    if (isLoadPosts) return; // Prevent multiple calls

    setLoadPosts(true);
    const timeoutId = setTimeout(async () => {
      setPageSize((prev) => Math.min(prev + 10, MAX_WATER_STATUS_ITEMS)); // จำกัดขนาด
      setLoadPosts(false);
    }, 500); // ลดเวลาจาก 1000 เป็น 500

    // Cleanup timeout if component unmounts
    return () => clearTimeout(timeoutId);
  };
  const selectPayMent = (item: any, index: number) => {
    setSelectedIndex(index);
    setActivePay(index);
    setPaymentData(item);
  };
  const handleSavePolygon = () => {
    if (coordinates.length > 2) {
      const centroid = calculateCentroid(coordinates);
      if (centroid) {
        setLatitude(centroid.latitude);
        setLongitude(centroid.longitude);
      }
      const newPolygon = coordinates.map((coord: any) => ({
        lat: coord.latitude,
        lng: coord.longitude,
      }));
      setPolygons(newPolygon);
      setModalSaveArea(true);
      setActiveButton(null);
    } else {
      console.log("Please select at least 3 points to create a polygon.");
    }
  };
  const handleSaveData = async () => {
    try {
      setLoadIng(true);
      const req = {
        farmUserPlotId: farmUserPlotId,
        numberOfPlanting: 1,
        plantingList: [
          {
            farmSeedId: idSelectPlans || "",
            seedName: selectPlansName || "",
            pointLatitude: latitude || 0,
            pointLongitude: longitude || 0,
            geometries: polygons || [],
          },
        ],
      };
      const response = await plantingApi(req);
      dataDocDetail();
      setCoordinates([]);
      setDocSelectPlans([]);
      setSelectPlans("");
    } catch (error) {
      console.log(error);
    } finally {
      setModalSaveArea(false);
      setLoadIng(false);
    }
  };

  //Go to
  const goListManageFarm = () => {
    navigation.navigate("listManageFarm", {
      selectedManage: selectedManage,
      farmUserPlotId: farmUserPlotId,
      docDetailMyfarm: docDetailMyfarm,
    });
  };
  const goOperation = (type: any) => {
    navigation.navigate("Operation", {
      docList: docList,
      farmUserPlotId: farmUserPlotId,
      type: type,
      isPages: isPages,
    });
  };
  const goDelivery = () => {
    setActiveButton(0);
    setModalManage(false);
    navigation.navigate("Delivery", {
      selectedItems: selectedItems,
      farmUserPlotId: farmUserPlotId,
      docDetailMyfarm: docDetailMyfarm,
    });
  };
  const goPayMent = (item: any, typePay: any) => {
    setActiveButton(0);
    setModalManage(false);
    navigation.navigate("QrCodepay", {
      type: typePay || "",
      itemDocStutesPay: item || "",
      AfterinvoiceNumber: item.invoiceNumber || "",
      docDetailMyfarm: docDetailMyfarm || "",
      paymentData: paymentData,
    });
  };
  const goBack = () => {
    if (isPages === "MaketFram") {
      navigation.navigate("Bottom_Tab", {
        screen: "MaketFram",
      });
    } else {
      navigation.navigate("Bottom_Tab", {
        screen: "PlusArea",
      });
    }
  };
  // const goChat = () => {
  //   navigation.navigate("ChatSystem", { docList: docList });
  // };
  const goDrawmap = () => {
    navigation.navigate("drawMap", { docList: docList });
  };
  const onDrag = () => {
    setDrag(true);
  };
  const onMenu = () => {
    setModalMenu(!isModalMenu);
    setMenuHeader(false);
    callPlotDetail();
  };
  const onMenuHerder = () => {
    setMenuHeader(!isMenuHeader);
    setModalMenu(false);
  };
  const onChantFarm = () => {
    setChatFarm(true);
  };
  const onMenuClose = () => {
    setModalMenu(!isModalMenu);
    setPolygonMode(false);
    setActiveButton(0);
  };
  const onCloseWater = () => {
    setModalManage(false);
    setActiveButton(0);
  };
  const onConfirmWater = () => {
    callwateringIot();
    startCountdown();
  };
  const onCloseMange = () => {
    setModalManage(false);
    setActiveButton(0);
  };
  const onSelectMange = () => {
    setModalManage(false);
    setActiveButton(0);
    navigation.navigate("listManageFarm", {
      selectedManage: selectedManage,
      farmUserPlotId: farmUserPlotId,
      docDetailMyfarm: docDetailMyfarm,
    });
  };
  const onConfirmDelete = (item: any) => {
    setDeleteArea(item.id);
    setModalDelete(true);
  };
  const onModalDetail = () => {
    callPlotDetail();
    setModalDetail(true);
  };
  const onModalPlantingNoti = () => {
    setModalPlantingNoti(true);
  };
  const onRefresh = () => {
    setRefreshing(true);
    setLoadIng(true);
    Promise.all([
      (async () => {
        try {
          await dataDocDetail();
        } catch (error) {
          console.log("Error refreshing data:", error);
        }
      })(),
    ]).finally(() => {
      setTimeout(() => {
        setRefreshing(false);
        setLoadIng(false);
        setCoordinates([]);
        setDocSelectPlans([]);
        setSelectPlans("");
        setImageUrl(null);
        setEditArea("");
        setEdit("");

        // Perform memory cleanup
        performMemoryCleanup();
      }, 1000);
    });
  };
  const onCancleEditName = () => {
    setFramPlotName("");
    setEditFramName(!isEditFramName);
  };
  const onOpenNewName = () => {
    setModalSaveName(true);
  };
  const onDoneIntro = async () => {
    await AsyncStorage.setItem("hasSeenIntro", "true");
    setIntro(false);
  };

  //Menu All เพราะ, แบ่งแปลง, แจ้ง, แปลง
  const onBottonType1 = async () => {
    setPolygonMode(false);
    setModalMenu(false);
    handleButtonClick(1);
    try {
      setLoadIng(true);
      if (coordinates.length > 2) {
        const centroid = calculateCentroid(coordinates);
        if (centroid) {
          setLatitude(centroid.latitude);
          setLongitude(centroid.longitude);
        }
        const newPolygon = coordinates.map((coord: any) => ({
          lat: coord.latitude,
          lng: coord.longitude,
        }));

        const req = {
          farmUserPlotId: farmUserPlotId,
          geometries: newPolygon || [],
        };

        const res = await plantOverlap(req);
        if (res?.success) {
          setPolygonMode(false);
          setModalMenu(false);
          handleButtonClick(1);
        } else {
          setModalOverPolygon(true);
        }
      } else {
        console.log("Please select at least 3 points to create a polygon.");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const onBottonType2 = () => {
    setPolygonMode(true);
    setModalMenu(false);
    handleButtonClick(2);
    setMenuPlant(false);
  };
  const onBottonType3 = () => {
    callPlotDetail();
    setPolygonMode(false);
    setModalInFrom(true);
    setModalMenu(false);
    handleButtonClick(3);
    setMenuPlant(false);
  };
  const onBottonType4 = () => {
    handleButtonClick(4);
    setPolygonMode(false);
    setModalManage(true);
    setModalMenu(false);
    setMenuPlant(false);
    callWater();
    callMangeCovert();
    callHarvest();
    callStatus();
    callWaitList();
    callWaterListStatus();
  };
  const onTypeclose = () => {
    setActiveButton(null);
    setCheckAreaPant("");
  };

  //Menu รดน้ำ, แปลง, เก็บผล, สถานะ อยู่ใน onBottonType4
  const onBottonManage1 = () => {
    handleButtonManage(1);
  };
  const onBottonManage2 = () => {
    handleButtonManage(2);
  };
  const onBottonManage3 = () => {
    handleButtonManage(3);
  };
  const onBottonManage4 = () => {
    handleButtonManage(4);
  };

  //เป็น Menu ย่อยใน Tap รดน้ำ อยู่ใน onBottonManage1
  //onBottonTools1 รดน้ำ
  //onBottonTools2 ตั้งเวลารดน้ำ
  const onBottonTools1 = () => {
    handleButtonTool(1);
  };
  const onBottonTools2 = () => {
    handleButtonTool(2);
  };
  const onBottonTools3 = () => {
    handleButtonTool(3);
  };
  const onSetTimeWater = () => {
    setPlus(true);
  };
  const onEditTimeWater = (item: any, typeEdit: string) => {
    setPlus(true);
    setEdit(typeEdit);
    setIdEdit(item.id);
    setFarmUserPlotIdEdit(item.farmUserPlotId);
    setplantingProcessTypeIdEdit(item.masterPlantingProcessTypeId);
    const startTime = item.startTime
      ? moment(item.startTime, "HH:mm:ss").toDate()
      : new Date();
    const endTime = item.endTime
      ? moment(item.endTime, "HH:mm:ss").toDate()
      : new Date();

    setDate(startTime);
    setDateEnd(endTime);

    setIsMonday(item.monday || false);
    setIsTuesday(item.tuesday || false);
    setIsWednesday(item.wednesday || false);
    setIsThursday(item.thursday || false);
    setIsFriday(item.friday || false);
    setIsSaturday(item.saturday || false);
    setIsSunday(item.sunday || false);
  };
  const onCleanTimeWater = () => {
    setPlus(false);
    setIsMonday(false);
    setIsTuesday(false);
    setIsWednesday(false);
    setIsThursday(false);
    setIsFriday(false);
    setIsSaturday(false);
    setIsSunday(false);
    setEdit("");
    setDate(initialDate);
    setDateEnd(initialDate);
  };

  //เป็น Menu ย่อยใน Tap สถานะ อยู่ใน onBottonManage4
  //onBottonStutes1
  //onBottonStutes2 รอดำเนินการ
  //onBottonStutes3 เสร็จสิ้น
  const onBottonStutes1 = () => {
    handleButtonStutes(1);
  };
  const onBottonStutes2 = () => {
    handleButtonStutes(2);
  };
  const onBottonStutes3 = () => {
    handleButtonStutes(3);
  };
  const onBottonStutes4 = () => {
    handleButtonStutes(4);
  };

  //คำนวณระยะทาง
  const haversine = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) => {
    const toRad = (value: number) => (value * Math.PI) / 180;
    const R = 6371;
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) *
        Math.cos(toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance * 1000;
  };
  const calculateDistances = (polygon: any) => {
    let distances = [];
    for (let i = 0; i < polygon.length; i++) {
      const start = polygon[i];
      const end = polygon[(i + 1) % polygon.length];
      const distance = haversine(
        start.latitude,
        start.longitude,
        end.latitude,
        end.longitude
      );
      distances.push(distance.toFixed(2));
    }
    return distances;
  };
  const calculateCentroid = (polygon: Coordinate[]): Coordinate | null => {
    if (polygon.length === 0) {
      return null;
    }

    let latitudeSum = 0;
    let longitudeSum = 0;
    let validPoints = 0;

    polygon.forEach((point) => {
      if (
        point &&
        typeof point.latitude === "number" &&
        typeof point.longitude === "number"
      ) {
        latitudeSum += point.latitude;
        longitudeSum += point.longitude;
        validPoints += 1;
      }
    });

    if (validPoints === 0) {
      return null;
    }

    const centroid: Coordinate = {
      latitude: latitudeSum / validPoints,
      longitude: longitudeSum / validPoints,
    };
    return centroid;
  };

  //FlatList
  const FlasListManage = () => (
    <>
      {Array.isArray(service) && service.length > 0 ? (
        <FlatList
          data={service}
          renderItem={renderManageFram}
          style={{ flex: 1 }}
          removeClippedSubviews={true}
          maxToRenderPerBatch={9}
          windowSize={5}
          initialNumToRender={9}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => `manage-${index}`}
          showsVerticalScrollIndicator={false}
          numColumns={3}
          getItemLayout={(data, index) => ({
            length: 120, // Approximate item height
            offset: 120 * Math.floor(index / 3),
            index,
          })}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(20) }} />
          )}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListProduct = () => (
    <>
      {harvest != "" ? (
        <FlatList
          data={harvest}
          renderItem={renderProduct}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          extraData={selectedItems}
          numColumns={3}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(20) }} />
          )}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListInProgress = () => (
    <>
      {inProgress != "" ? (
        <FlatList
          data={inProgress}
          renderItem={renderInProgress}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          extraData={selectedItems}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListPending = () => (
    <>
      {waiting != "" ? (
        <FlatList
          data={waiting}
          renderItem={renderWaiting}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          extraData={selectedItems}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListSuccess = () => (
    <>
      {finish != "" ? (
        <FlatList
          data={finish}
          renderItem={renderFinish}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          extraData={selectedItems}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListInfrom = () => (
    <>
      {plotDetail.length > 0 ? (
        <FlatList
          data={plotDetail}
          renderItem={renderInFrom}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          contentContainerStyle={{ marginTop: moderateScale(20) }}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(200) }} />
          )}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListDetail = () => (
    <>
      {plotDetail != "" ? (
        <FlatList
          data={plotDetail}
          horizontal
          renderItem={renderDetail}
          removeClippedSubviews={true}
          maxToRenderPerBatch={5}
          windowSize={8}
          initialNumToRender={3}
          updateCellsBatchingPeriod={50}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={{ alignItems: "center", justifyContent: "center" }}>
          <Image
            style={{ width: 50, height: 50, alignItems: "center" }}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListWatering = () => (
    <>
      {listWatering.length > 0 ? (
        <FlatList
          data={listWatering}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          style={{ marginTop: moderateScale(5), width: "100%" }}
          renderItem={renderWatering}
          keyExtractor={(item, index) => `watering-${item.id || index}`}
          showsVerticalScrollIndicator={false}
          getItemLayout={(data, index) => ({
            length: 100, // Approximate item height
            offset: 100 * index,
            index,
          })}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(20) }} />
          )}
          refreshing={loadingDelete}
          onRefresh={() => refreshWateringData()}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListWaterStatus = () => (
    <>
      {waterListStatus.length > 0 ? (
        <FlatList
          data={waterListStatus}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          style={{ marginTop: moderateScale(5), width: "100%" }}
          renderItem={renderListStatus}
          onScroll={handleScroll}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => (
            <>
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts ? LoadingApp() : null}
              <View style={{ margin: moderateScale(120) }} />
            </>
          )}
          refreshing={loadingDelete}
          onRefresh={() => refreshWateringData()}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListWaitList = () => (
    <>
      {waitList.length > 0 ? (
        <FlatList
          data={waitList}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          renderItem={renderWaitList}
          style={{ width: "100%" }}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(20) }} />
          )}
        />
      ) : (
        <View style={ctn.ctn_OutInfom}>
          <Image
            style={img.img_outOfStock}
            source={Images.outOfStock}
            resizeMode="cover"
          />
        </View>
      )}
    </>
  );
  const FlasListAreaPlant = () => (
    <>
      {polygonsPlanting.length > 0 && coordinates.length === 0 ? (
        <FlatList
          data={polygonsPlanting}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          renderItem={renderAreaPlant}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(200) }} />
          )}
        />
      ) : polygonsPlanting.length === 0 && coordinates.length === 0 ? (
        <Text style={{ textAlign: "center", marginTop: 20 }}>
          No data available.
        </Text>
      ) : (
        <FlatList
          data={docSeedlist}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={8}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          renderItem={renderSelectPlans}
          keyExtractor={(item, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          numColumns={3}
          ListFooterComponent={() => (
            <View style={{ margin: moderateScale(300) }} />
          )}
        />
      )}
    </>
  );

  //Ui
  const mapView = () => {
    const centroid = calculateCentroid(coordinates);
    let itemPlans: any = selectedItems;
    let itemManage: any = selectedManage;
    return (
      <>
        <MapView
          style={map.map}
          ref={mapRef}
          key={forceReload}
          region={region}
          provider={PROVIDER_GOOGLE}
          mapType="satellite"
          zoomEnabled={true}
          scrollEnabled={true}
          toolbarEnabled={false}
          showsCompass={true}
          onMapReady={() => {
            setMapReady(true);
          }}
          onRegionChangeComplete={(region) => {
            setCentroid({
              latitude: region.latitude,
              longitude: region.longitude,
            });
            if (
              region.latitudeDelta < 0.0006 &&
              activeButton != 1 &&
              activeButton != 2
            ) {
              // goOperation("cctv");
            }
            if (region.latitudeDelta > 100) {
              console.log("Zoom out แล้ว", region);
            }
          }}
        >
          {polygon && polygon.length > 0 && (
            <Polygon
              coordinates={polygon}
              strokeColor={BgColor.Bg_64BAA8}
              fillColor={BgOpacity.Op_8080802}
              strokeWidth={3}
              lineCap="square"
              lineJoin="miter"
            />
          )}
          {activeButton === 2 &&
            polygon &&
            polygon.length > 0 &&
            calculateDistances(polygon)
              .slice(0, 20)
              .map((distance, idx) => {
                const start = polygon[idx];
                const end = polygon[(idx + 1) % polygon.length];
                if (!start || !end) return null;

                const midPoint = {
                  latitude: (start.latitude + end.latitude) / 2,
                  longitude: (start.longitude + end.longitude) / 2,
                };

                return (
                  <Marker key={`distance-${idx}`} coordinate={midPoint}>
                    <Text style={[fonstStyle.f8_light, txt.txt_white]}>
                      {`${distance}`}
                      {t("Meters")}
                    </Text>
                  </Marker>
                );
              })}
          {/* แปลงสร้าง */}
          {Array.isArray(polygonsPlanting) &&
            polygonsPlanting.length > 0 &&
            polygonsPlanting.map((coords: any, index: number) => (
              <Polygon
                key={coords?.id || index}
                coordinates={coords?.coordinates || []}
                strokeColor={
                  editArea === coords?.id
                    ? BgColor.Bg_E74C3C
                    : checkAreaPant === coords?.id
                    ? BgColor.Bg_3498db
                    : coords?.isCanEdit === false
                    ? BgColor.Bg_64BAA8
                    : BgColor.Bg_FF9900
                }
                fillColor={
                  editArea === coords?.id
                    ? BgOpacity.Op_E74C3C05
                    : checkAreaPant === coords?.id
                    ? BgOpacity.OP_3498db03
                    : coords?.isCanEdit === false
                    ? BgOpacity.Op_b3dbc05
                    : BgOpacity.Op_eecb2f2
                }
                strokeWidth={checkAreaPant === coords?.id ? 4 : 2}
                lineCap="square"
                lineJoin="miter"
              />
            ))}
          {polygonsPlanting.slice(0, 50).map((coords: any, index: number) => {
            if (!coords.coordinates || coords.coordinates.length === 0)
              return null;

            const centroid: any = calculateCentroid(coords.coordinates);
            if (!centroid) return null;

            return (
              <Marker
                key={`planting-${coords.id || index}`}
                pinColor="red"
                coordinate={centroid}
              >
                {coords.imageUrl != null ? (
                  <FastImage
                    style={img.img_plans}
                    source={{
                      uri: coords.imageUrl,
                      priority: FastImage.priority.normal,
                    }}
                    resizeMode={FastImage.resizeMode.contain}
                  />
                ) : (
                  <View style={img.img_plansNon}>{iconNoImg()}</View>
                )}
              </Marker>
            );
          })}
          {coordinates.length > 1 && (
            <>
              <Polygon
                coordinates={coordinates}
                fillColor={BgOpacity.Op_E74C3C03}
                strokeColor={BgColor.Bg_E74C3C}
                strokeWidth={1.5}
                lineCap="square"
                lineJoin="miter"
              />

              {calculateDistances(coordinates).map((distance, idx) => {
                const start = coordinates[idx];
                const end = coordinates[(idx + 1) % coordinates.length];
                const midPoint = {
                  latitude: (start.latitude + end.latitude) / 2,
                  longitude: (start.longitude + end.longitude) / 2,
                };

                return (
                  <>
                    {activeButton === 2 && (
                      <Marker key={idx} coordinate={midPoint}>
                        <Text style={[fonstStyle.f8_light, txt.txt_white]}>
                          {`${distance}`}
                          {t("Meters")}
                        </Text>
                      </Marker>
                    )}
                  </>
                );
              })}
              {centroid && activeButton != 2 && (
                <>
                  {docSelectPlans != "" ? (
                    <Marker coordinate={centroid} pinColor="red">
                      <View style={{ alignItems: "center" }}>
                        {imageUrl != null ? (
                          <FastImage
                            style={img.img_plans}
                            source={{ uri: imageUrl }}
                            resizeMode={FastImage.resizeMode.contain}
                            onLoadStart={() => setLoadIng(true)}
                            onLoadEnd={() => setLoadIng(false)}
                          />
                        ) : (
                          <View style={img.img_plansNon}>{iconNoImg()}</View>
                        )}
                      </View>
                    </Marker>
                  ) : (
                    <Marker coordinate={centroid}>
                      <View style={img.img_plansNon}>{iconPlusPlant()}</View>
                    </Marker>
                  )}
                </>
              )}
            </>
          )}
          {coordinates.map((coordinate: any, index: number) => (
            <>
              {activeButton === 2 && (
                <Marker
                  key={index}
                  coordinate={coordinate}
                  draggable
                  onDragStart={() => onDrag()}
                  onDragEnd={(e) => handleMarkerDragEnd(index, e)}
                />
              )}
            </>
          ))}
        </MapView>
        {activeButton == 2 && (
          <TouchableOpacity
            onPress={() => {
              setCentroid({
                latitude: region.latitude,
                longitude: region.longitude,
              });
            }}
            style={{
              position: "absolute",
              top: Platform.OS === "ios" ? height / 2 - 20 : height / 2 - -5,
              left: width / 2 - 20,
            }}
          >
            {iconLocationMarker()}
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={
            orientation === "portrait"
              ? btn.btn_costomGoback
              : btn.btn_costomGobackLan
          }
          onPress={() => goBack()}
        >
          {goBack_Bg()}
        </TouchableOpacity>
        <EditPoltname
          farmName={farmName}
          orientation={orientation}
          isEditFramName={isEditFramName}
          framPlotName={framPlotName}
          setFramPlotName={setFramPlotName}
          plotName={plotName}
          onCancleEditName={onCancleEditName}
          onOpenNewName={onOpenNewName}
          iconCancleFarmName={iconCancleFarmName}
          iconEditFarmName={iconEditFarmName}
          iconSaveFarmName={iconSaveFarmName}
          iconSavePoltName={iconSavePoltName}
          iconCanclePoltName={iconCanclePoltName}
          iconEditPoltName={iconEditPoltName}
          fonstStyle={fonstStyle}
          txt={txt}
          oth={oth}
          ctn={ctn}
          moderateScale={moderateScale}
          t={t}
          isTablet={isTablet}
          isPages={isPages}
        />
        {activeButton === 4 && (
          <Modal animationType="slide" transparent={true} visible={modalManage}>
            <View style={ctn.ctn_manageFarm}>
              <View style={{ flexDirection: "row" }}>
                {/* Munu 1 */}
                <TouchableOpacity
                  onPress={() => onBottonManage1()}
                  disabled={activeManage === 1 ? true : false}
                >
                  <LinearGradient
                    colors={activeManage === 1 ? linearSky : linearGreen}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_manageFarm}
                  >
                    <Image
                      style={img.img_ellipseManage}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />

                    <Image
                      style={img.img_manage}
                      source={Images.menu1}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f12_bold,
                        activeManage === 1
                          ? txt.txt_manage
                          : txt.txt_manageClick,
                      ]}
                    >
                      {t("water")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(2) }} />

                {/* Munu 2 */}
                <TouchableOpacity
                  onPress={() => onBottonManage2()}
                  disabled={activeManage === 2 ? true : false}
                >
                  <LinearGradient
                    colors={activeManage === 2 ? linearSky : linearGreen}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_manageFarm}
                  >
                    <Image
                      style={img.img_ellipseManage}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />

                    <Image
                      style={img.img_manage}
                      source={Images.menu2}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f12_bold,
                        activeManage === 2
                          ? txt.txt_manage
                          : txt.txt_manageClick,
                      ]}
                    >
                      {t("Manage_conversion")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(2) }} />

                {/* Munu 3 */}
                <TouchableOpacity
                  onPress={() => onBottonManage3()}
                  disabled={activeManage === 3 ? true : false}
                >
                  <LinearGradient
                    colors={activeManage === 3 ? linearSky : linearGreen}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_manageFarm}
                  >
                    <Image
                      style={img.img_ellipseManage}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Image
                      style={img.img_manage}
                      source={Images.menu3}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f12_bold,
                        activeManage === 3
                          ? txt.txt_manage
                          : txt.txt_manageClick,
                      ]}
                    >
                      {t("Harvest")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(2) }} />

                {/* Munu 4 */}
                <TouchableOpacity
                  onPress={() => onBottonManage4()}
                  disabled={activeManage === 4 ? true : false}
                >
                  <LinearGradient
                    colors={activeManage === 4 ? linearSky : linearGreen}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_manageFarm}
                  >
                    <Image
                      style={img.img_ellipseManage}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Image
                      style={img.img_manage}
                      source={Images.menu4}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f12_bold,
                        activeManage === 4
                          ? txt.txt_manage
                          : txt.txt_manageClick,
                      ]}
                    >
                      {t("status")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>

              {/* Content */}
              <ImageBackground
                imageStyle={img.img_wood}
                source={
                  activeManage === 4
                    ? Images.TransparentWooFFF
                    : Images.TransparentWoo
                }
                resizeMode="cover"
                style={[
                  ctn.ctn_manage,
                  {
                    backgroundColor:
                      activeManage === 3
                        ? BgColor.Bg_F2FFF5
                        : activeManage === 4
                        ? BgColor.Bg_316358
                        : BgColor.Bg_FFF4E3,
                  },
                ]}
              >
                {activeManage === 1 && (
                  <>
                    <View style={{ alignItems: "center" }}>
                      <View style={ctn.ctn_bottonTools}>
                        <View style={{ flexDirection: "row" }}>
                          <TouchableOpacity
                            onPress={() => onBottonTools1()}
                            disabled={activeTools === 1 ? true : false}
                            style={
                              activeTools === 1
                                ? btn.btn_Stutes1
                                : btn.btn_Stutes2
                            }
                          >
                            <Image
                              style={img.img_iconEllipse}
                              source={Images.Ellipse}
                              resizeMode="contain"
                            />
                            <Text
                              style={[
                                fonstStyle.f12_bold,
                                activeTools === 1
                                  ? txt.txt_Stutes1
                                  : txt.txt_Stutes2,
                              ]}
                            >
                              {t("Water_immediately")}
                            </Text>
                          </TouchableOpacity>
                          <View style={{ margin: moderateScale(2) }} />

                          <TouchableOpacity
                            onPress={() => onBottonTools2()}
                            disabled={activeTools === 2 ? true : false}
                            style={
                              activeTools === 2
                                ? btn.btn_Stutes1
                                : btn.btn_Stutes2
                            }
                          >
                            <Image
                              style={img.img_iconEllipse}
                              source={Images.Ellipse}
                              resizeMode="contain"
                            />
                            <Text
                              style={[
                                fonstStyle.f12_bold,
                                activeTools === 2
                                  ? txt.txt_Stutes1
                                  : txt.txt_Stutes2,
                              ]}
                            >
                              {t("Set_the_time_to_reduce_water")}
                            </Text>
                          </TouchableOpacity>
                          <View style={{ margin: moderateScale(2) }} />

                          <TouchableOpacity
                            onPress={() => onBottonTools3()}
                            disabled={activeTools === 3 ? true : false}
                            style={
                              activeTools === 3
                                ? btn.btn_Stutes1
                                : btn.btn_Stutes2
                            }
                          >
                            <Image
                              style={img.img_iconEllipse}
                              source={Images.Ellipse}
                              resizeMode="contain"
                            />
                            <Text
                              style={[
                                fonstStyle.f12_bold,
                                activeTools === 3
                                  ? txt.txt_Stutes1
                                  : txt.txt_Stutes2,
                              ]}
                            >
                              {t("status")}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>

                    {activeTools === 1 && (
                      <>
                        <View style={ctn.ctn_Tools1}>
                          <Image
                            style={img.img_tools1}
                            source={Images.menu1}
                            resizeMode="contain"
                          />
                          <Text style={[fonstStyle.f14_bold, txt.txt_Stutes2]}>
                            {stutesWater === "In Progress" ||
                            startNow === "In Progress"
                              ? t("watering_time")
                              : t(
                                  "Click_the_Confirm_button_to_start_watering_immediately"
                                )}
                          </Text>
                          <View style={{ margin: moderateScale(5) }} />

                          {/* Start > End Time */}
                          <View style={ctn.ctn_spaceCenter}>
                            {stutesWater === "In Progress" ||
                            startNow === "In Progress" ? (
                              <>
                                <Text
                                  style={[fonstStyle.f14_bold, txt.txt_Stutes2]}
                                >
                                  {moment(startTime || startNow).format(
                                    "HH:mm"
                                  )}
                                </Text>
                                <View style={{ margin: moderateScale(5) }} />
                                {iconStartEnd()}
                                <View style={{ margin: moderateScale(5) }} />
                                <Text
                                  style={[fonstStyle.f14_bold, txt.txt_Stutes2]}
                                >
                                  {moment(endTime || endNow).format("HH:mm")}
                                </Text>
                              </>
                            ) : null}
                          </View>
                          <View style={{ margin: moderateScale(5) }} />

                          {stutesWater === "In Progress" ||
                          startNow === "In Progress" ? (
                            <>
                              {remainingTime > 0 && (
                                <Text
                                  style={[fonstStyle.f14_bold, txt.txt_Stutes2]}
                                >
                                  ⏳{" "}
                                  {String(
                                    Math.floor(remainingTime / 60)
                                  ).padStart(2, "0")}
                                  :{String(remainingTime % 60).padStart(2, "0")}
                                </Text>
                              )}
                            </>
                          ) : null}
                        </View>

                        <View style={{ alignItems: "center" }}>
                          <View style={[ctn.ctn_yesNo]}>
                            <TouchableOpacity
                              onPress={() => onCloseWater()}
                              style={btn.btn_canCelYseNo}
                            >
                              <Image
                                style={img.img_iconEllipse}
                                source={Images.Ellipse}
                                resizeMode="contain"
                              />
                              <Text
                                style={[fonstStyle.f12_bold, txt.txt_white]}
                              >
                                {t("close")}
                              </Text>
                            </TouchableOpacity>
                            <View style={{ margin: moderateScale(5) }} />

                            <TouchableOpacity
                              style={[
                                btn.btn_conFirmYseNo,
                                {
                                  opacity:
                                    stutesWater === "In Progress"
                                      ? 0.5
                                      : undefined,
                                },
                              ]}
                              onPress={() => onConfirmWater()}
                              disabled={
                                countdown > 0 || stutesWater === "In Progress"
                              }
                            >
                              <Image
                                style={img.img_iconEllipse}
                                source={Images.Ellipse}
                                resizeMode="contain"
                              />
                              <Text
                                style={[fonstStyle.f12_bold, txt.txt_white]}
                              >
                                {countdown > 0 ? countdown : t("confirm")}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </>
                    )}
                    {activeTools === 2 && (
                      <>
                        {isPlus === true ? (
                          <View style={ctn.ctn_timeWater}>
                            <View style={ctn.ctn_spaceWater}>
                              {/* เวลาเริ่มต้น */}
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  style={[fonstStyle.f14_bold, txt.txt_606060]}
                                >
                                  {t("start_time")}
                                </Text>
                                <TouchableOpacity
                                  onPress={() => setOpen(true)}
                                  style={btn.btn_timeWater}
                                >
                                  <Text
                                    style={[
                                      fonstStyle.f14_light,
                                      txt.txt_center,
                                      txt.txt_606060,
                                    ]}
                                  >
                                    {date.toLocaleTimeString("th-TH", {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </Text>
                                </TouchableOpacity>
                              </View>

                              {/* เวลาสิ้นสุด */}
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  style={[fonstStyle.f14_bold, txt.txt_606060]}
                                >
                                  {t("end_time")}
                                </Text>
                                <TouchableOpacity
                                  onPress={() => setOpenEnd(true)}
                                  style={btn.btn_timeWater}
                                >
                                  <Text
                                    style={[
                                      fonstStyle.f14_light,
                                      txt.txt_center,
                                      txt.txt_606060,
                                    ]}
                                  >
                                    {dateEnd.toLocaleTimeString("th-TH", {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>

                            {/* กำหนด */}
                            <View style={{ margin: moderateScale(10) }} />
                            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                              {t("Set_date")}
                            </Text>
                            <View style={ctn.ctn_daysContainer}>
                              {days.map((day, index) => (
                                <TouchableOpacity
                                  key={index}
                                  style={[
                                    btn.btn_dayButton,
                                    day.isSelected
                                      ? btn.btn_selectedDay
                                      : btn.btn_unselectedDay,
                                  ]}
                                  onPress={day.toggle}
                                >
                                  <Text
                                    style={[
                                      fonstStyle.f12_light,
                                      txt.txt_dayText,
                                      day.isSelected
                                        ? txt.txt_white
                                        : txt.txt_606060,
                                    ]}
                                  >
                                    {day.name}
                                  </Text>
                                </TouchableOpacity>
                              ))}
                            </View>
                          </View>
                        ) : (
                          FlasListWatering()
                        )}
                        <View style={{ alignItems: "center" }}>
                          <View style={[ctn.ctn_yesNo]}>
                            <TouchableOpacity
                              onPress={() =>
                                isPlus === true
                                  ? onCleanTimeWater()
                                  : onCloseWater()
                              }
                              style={btn.btn_canCelYseNo}
                            >
                              <Image
                                style={img.img_iconEllipse}
                                source={Images.Ellipse}
                                resizeMode="contain"
                              />
                              <Text
                                style={[fonstStyle.f14_bold, txt.txt_white]}
                              >
                                {isPlus === true ? t("cancel") : t("close")}
                              </Text>
                            </TouchableOpacity>
                            <View style={{ margin: moderateScale(5) }} />

                            <TouchableOpacity
                              style={[
                                btn.btn_conFirmYseNo,
                                isPlus === true && isSaveDisabled
                                  ? btn.btn_conFirmYseNoDis
                                  : undefined,
                              ]}
                              onPress={() =>
                                isPlus === true
                                  ? isSaveDisabled
                                    ? undefined // ไม่ทำงานถ้าเงื่อนไขไม่ครบ
                                    : callSetTimeWater()
                                  : onSetTimeWater()
                              }
                            >
                              <Image
                                style={img.img_iconEllipse}
                                source={Images.Ellipse}
                                resizeMode="contain"
                              />
                              <Text
                                style={[fonstStyle.f14_bold, txt.txt_white]}
                              >
                                {isPlus === true
                                  ? t("replies_save")
                                  : t("plus")}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </>
                    )}
                    {activeTools === 3 && (
                      <>
                        {FlasListWaterStatus()}{" "}
                        <View style={{ alignItems: "center" }}>
                          <View style={[ctn.ctn_yesNo]}>
                            <TouchableOpacity
                              onPress={() =>
                                isPlus === true
                                  ? onCleanTimeWater()
                                  : onCloseWater()
                              }
                              style={btn.btn_canCelYseNo}
                            >
                              <Image
                                style={img.img_iconEllipse}
                                source={Images.Ellipse}
                                resizeMode="contain"
                              />
                              <Text
                                style={[fonstStyle.f12_bold, txt.txt_white]}
                              >
                                {t("close")}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </>
                    )}
                  </>
                )}
                {activeManage === 2 && (
                  <>
                    <Text
                      style={[
                        fonstStyle.f14_bold,
                        txt.txt_606060,
                        { padding: moderateScale(10) },
                      ]}
                    >
                      {t("select_more_item")}
                    </Text>
                    {FlasListManage()}

                    {/* ยืนยัน & ยกเลิก */}
                    <View style={ctn.ctn_continueBottomActive2}>
                      <TouchableOpacity
                        onPress={() => onCloseMange()}
                        style={btn.btn_canCelYseNo}
                      >
                        <Image
                          style={img.img_iconEllipse}
                          source={Images.Ellipse}
                          resizeMode="contain"
                        />
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("close")}
                        </Text>
                      </TouchableOpacity>
                      <View style={{ margin: moderateScale(5) }} />

                      <TouchableOpacity
                        style={
                          itemManage != ""
                            ? btn.btn_conFirmYseNo
                            : btn.btn_conFirmYseNoDis
                        }
                        onPress={() =>
                          itemManage != "" ? onSelectMange() : undefined
                        }
                      >
                        <Image
                          style={img.img_iconEllipse}
                          source={Images.Ellipse}
                          resizeMode="contain"
                        />
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("confirm")}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
                {activeManage === 3 && (
                  <>
                    <View style={[ctn.ctn_spaceBet, { alignItems: "center" }]}>
                      <Text
                        style={[
                          fonstStyle.f14_bold,
                          txt.txt_606060,
                          { padding: moderateScale(10) },
                        ]}
                      >
                        {t("select_more_item")}
                      </Text>
                      <TouchableOpacity style={oth.cardHistoryDelivery}>
                        <Image
                          style={{ width: 20, height: 24 }}
                          source={Images.historyDelivery}
                          resizeMode="cover"
                        />
                      </TouchableOpacity>
                    </View>
                    {FlasListProduct()}

                    {/* ยืนยัน & ยกเลิก */}
                    <View style={ctn.ctn_continueBottomActive3}>
                      <TouchableOpacity
                        onPress={() => onCloseWater()}
                        style={btn.btn_canCelYseNo}
                      >
                        <Image
                          style={img.img_iconEllipse}
                          source={Images.Ellipse}
                          resizeMode="contain"
                        />
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("close")}
                        </Text>
                      </TouchableOpacity>
                      <View style={{ margin: moderateScale(5) }} />

                      <TouchableOpacity
                        style={
                          itemPlans != ""
                            ? btn.btn_conFirmYseNo
                            : btn.btn_conFirmYseNoDis
                        }
                        onPress={() => (itemPlans != "" ? goDelivery() : null)}
                      >
                        <Image
                          style={img.img_iconEllipse}
                          source={Images.Ellipse}
                          resizeMode="contain"
                        />
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("confirm")}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
                {activeManage === 4 && (
                  <>
                    <View style={ctn.ctn_continueBottonStutes}>
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "center",
                        }}
                      >
                        {/* รอชำระเงิน */}
                        <TouchableOpacity
                          onPress={() => onBottonStutes3()}
                          disabled={activeStutes === 3 ? true : false}
                          style={
                            activeStutes === 3
                              ? btn.btn_boxStutes
                              : btn.btn_boxStutesNon
                          }
                        >
                          <Image
                            style={[img.img_iconEllipseStatus]}
                            source={Images.Ellipse}
                            resizeMode="contain"
                          />
                          <Text
                            style={[
                              fonstStyle.f12_bold,
                              activeStutes === 3
                                ? txt.textStutes
                                : txt.textStutesNon,
                            ]}
                          >
                            {t("payment")}
                          </Text>
                        </TouchableOpacity>
                        <View style={{ margin: moderateScale(2) }} />

                        {/* รอดำเนินการ */}
                        <TouchableOpacity
                          onPress={() => onBottonStutes2()}
                          disabled={activeStutes === 2 ? true : false}
                          style={
                            activeStutes === 2
                              ? btn.btn_boxStutes
                              : btn.btn_boxStutesNon
                          }
                        >
                          <Image
                            style={[img.img_iconEllipseStatus]}
                            source={Images.Ellipse}
                            resizeMode="contain"
                          />
                          <Text
                            style={[
                              fonstStyle.f12_bold,
                              activeStutes === 2
                                ? txt.textStutes
                                : txt.textStutesNon,
                            ]}
                          >
                            {t("pending")}
                          </Text>
                        </TouchableOpacity>
                        <View style={{ margin: moderateScale(2) }} />

                        {/* ปัญหา */}
                        <TouchableOpacity
                          onPress={() => onBottonStutes1()}
                          disabled={activeStutes === 1 ? true : false}
                          style={
                            activeStutes === 1
                              ? btn.btn_boxStutes
                              : btn.btn_boxStutesNon
                          }
                        >
                          <Image
                            style={[img.img_iconEllipseStatus]}
                            source={Images.Ellipse}
                            resizeMode="contain"
                          />
                          <Text
                            style={[
                              fonstStyle.f12_bold,
                              activeStutes === 1
                                ? txt.textStutes
                                : txt.textStutesNon,
                            ]}
                          >
                            {t("issue")}
                          </Text>
                        </TouchableOpacity>
                        <View style={{ margin: moderateScale(2) }} />

                        {/* เสร็จสิ้น */}
                        <TouchableOpacity
                          onPress={() => onBottonStutes4()}
                          disabled={activeStutes === 4 ? true : false}
                          style={
                            activeStutes === 4
                              ? btn.btn_boxStutes
                              : btn.btn_boxStutesNon
                          }
                        >
                          <Image
                            style={[img.img_iconEllipseStatus]}
                            source={Images.Ellipse}
                            resizeMode="contain"
                          />
                          <Text
                            style={[
                              fonstStyle.f12_bold,
                              activeStutes === 4
                                ? txt.textStutes
                                : txt.textStutesNon,
                            ]}
                          >
                            {t("finish")}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                    <View style={{ margin: moderateScale(5) }} />

                    {activeStutes === 3 && <>{FlasListWaitList()}</>}
                    {activeStutes === 1 && <>{FlasListInProgress()}</>}
                    {activeStutes === 2 && <>{FlasListPending()}</>}
                    {activeStutes === 4 && <>{FlasListSuccess()}</>}

                    {/* ยืนยัน & ยกเลิก */}
                    <View style={ctn.continueBottomActive4}>
                      <TouchableOpacity
                        onPress={() => onCloseWater()}
                        style={btn.btn_canCelYseNo}
                      >
                        <Image
                          style={img.img_iconEllipse}
                          source={Images.Ellipse}
                          resizeMode="contain"
                        />
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("close")}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                )}
              </ImageBackground>
            </View>
          </Modal>
        )}
        {polygonMode && activeButton === 2 && (
          <View style={ctn.ctn_bottomEditArea}>
            {orientation === "portrait" ? (
              <TouchableOpacity
                style={{ marginBottom: 10 }}
                onPress={() => {
                  handleMapPress({
                    nativeEvent: { coordinate: centroidCen },
                  });
                }}
              >
                <LinearGradient
                  colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_cleanButton}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconButtomMarker()}
                </LinearGradient>
              </TouchableOpacity>
            ) : null}
            <View style={{ flexDirection: "row" }}>
              {/* Share on landscape */}
              {orientation === "landscape" ? (
                <>
                  <TouchableOpacity
                    onPress={() => {
                      handleMapPress({
                        nativeEvent: { coordinate: centroidCen },
                      });
                    }}
                  >
                    <LinearGradient
                      colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
                      start={{ x: 0.0, y: 0.05 }}
                      end={{ x: 0.5, y: 1.0 }}
                      style={btn.btn_cleanButton}
                    >
                      <Image
                        style={img.img_iconEllipse}
                        source={Images.Ellipse}
                        resizeMode="contain"
                      />
                      {iconButtomMarker()}
                    </LinearGradient>
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(5) }} />
                </>
              ) : null}

              {/* Draw */}
              <TouchableOpacity onPress={goDrawmap}>
                <LinearGradient
                  colors={linearPink}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_cleanButton}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />

                  <Image
                    style={{
                      width: moderateScale(35),
                      height: moderateScale(35),
                    }}
                    source={Images.Drawmap}
                    resizeMode="cover"
                  />
                </LinearGradient>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              {/* Clean */}
              <TouchableOpacity onPress={handleCleanPolygon}>
                <LinearGradient
                  colors={lineOrange}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={
                    coordinates != ""
                      ? btn.btn_cleanButton
                      : btn.btn_cleanButtonNon
                  }
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />

                  <Image
                    style={{
                      width: moderateScale(20),
                      height: moderateScale(20),
                    }}
                    source={Images.ControlClean}
                    resizeMode="cover"
                  />
                </LinearGradient>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              {/* Delete Edit */}
              <TouchableOpacity onPress={handleCleanArea}>
                <LinearGradient
                  colors={linearDanger}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={
                    coordinates != ""
                      ? btn.btn_cleanButton
                      : btn.btn_cleanButtonNon
                  }
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />

                  {iconDeleteFarm()}
                </LinearGradient>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              {/* Select Area */}
              <TouchableOpacity
                onPress={() =>
                  coordinates != "" && coordinates.length > 2
                    ? onBottonType1()
                    : null
                }
                disabled={activeButton === 1 ? true : false}
              >
                <LinearGradient
                  colors={linearGreen}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={
                    coordinates != "" && coordinates.length > 2
                      ? btn.btn_cleanButton
                      : btn.btn_cleanButtonNon
                  }
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />

                  {iconCheckArea()}
                </LinearGradient>
              </TouchableOpacity>
            </View>
            <View style={{ margin: moderateScale(5) }} />
            <View style={ctn.ctn_areaMinmax}>
              <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                {t("already_created")} {totalPlanting || "0"} {t("remaining")}{" "}
                {maxPlanting || "0"}
              </Text>
            </View>
          </View>
        )}

        <Modal animationType="slide" transparent={true} visible={modalDetail}>
          <View style={mod.mod_end}>
            <LinearGradient
              colors={[LgColor.Lg_C0D576, LgColor.Lg_A1BD57, LgColor.Lg_78994A]}
              start={{ x: 0.0, y: 0.05 }}
              end={{ x: 0.5, y: 5.0 }}
              style={ctn.ctn_titleDetail}
            >
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="contain"
              />
              <View
                style={{
                  alignItems: "center",
                  marginTop: moderateScale(10),
                }}
              >
                <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                  {t("Area_information")}
                </Text>
              </View>
            </LinearGradient>

            <ImageBackground
              source={Images.TransparentWoo}
              resizeMode="cover"
              style={ctn.ctn_farmDetail}
            >
              <TouchableOpacity onPress={() => setModalDetail(false)}>
                <LinearGradient
                  colors={lineOrange}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_closeMod}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />

                  <View style={img.img_screenClose}>{iconScreenClose()}</View>
                </LinearGradient>
              </TouchableOpacity>

              <View style={oth.card_farmDetail}>
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {t("Plants_in_the_farm")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                {FlasListDetail()}
                <View style={{ margin: moderateScale(5) }} />

                <View style={oth.card_PH}>
                  <View style={ctn.ctn_spaceBet}>
                    <View style={oth.cardPhNumber}>
                      <Image
                        style={{ width: 40, height: 40 }}
                        source={Images.phCheck}
                        resizeMode="cover"
                      />
                      <View style={{ margin: moderateScale(5) }} />
                      <View style={{ marginTop: moderateScale(5) }}>
                        <Text style={[fonstStyle.f14_bold, txt.txt_greenPH]}>
                          {ph || "0"}
                        </Text>
                      </View>
                    </View>

                    <View style={{ margin: moderateScale(5) }} />

                    <View style={ctn.ctn_Slider}>
                      <LinearGradient
                        colors={colorsPH}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={ctn.ctn_bgSlider}
                      />
                      <View
                        style={[
                          oth.phStarp,
                          {
                            left: `${(value / 14) * 100}%`,
                          },
                        ]}
                      >
                        <Image
                          style={{ width: 20, height: 20 }}
                          source={Images.pktop}
                          resizeMode="cover"
                        />
                      </View>

                      <View
                        style={[
                          oth.phStarpDown,
                          {
                            left: `${(value / 14) * 100}%`,
                          },
                        ]}
                      >
                        <Image
                          style={{ width: 20, height: 20 }}
                          source={Images.pkdown}
                          resizeMode="cover"
                        />
                      </View>
                    </View>
                  </View>
                </View>
              </View>
              <View style={{ margin: moderateScale(5) }} />

              <ScrollView>
                <View style={ctn.ctn_spaceCenter}>
                  <View style={[btn.btn_boxDetilIot, oth.bgIotA5FAFF]}>
                    <View style={oth.progressmoisrure}>
                      <View
                        style={[
                          oth.progressM,
                          {
                            height: `${(humidity / 100) * 100}%`,
                          },
                        ]}
                      />
                    </View>
                    <Image
                      style={{ width: 35, height: 35 }}
                      source={Images.waterCheck}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(2) }} />
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {humidity || "0"} %
                    </Text>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("moisture")}
                    </Text>
                  </View>
                  <View style={{ margin: moderateScale(5) }} />

                  <View style={[btn.btn_boxDetilIot, oth.bgIotFFC581]}>
                    <View
                      style={{
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Svg width={size} height={size}>
                        <Circle
                          cx={size / 2}
                          cy={size / 2}
                          r={radius}
                          stroke="#E0E0E0"
                          strokeWidth={strokeWidth}
                          fill="none"
                        />

                        <Circle
                          cx={size / 2}
                          cy={size / 2}
                          r={radius}
                          stroke="#515151"
                          strokeWidth={strokeWidth}
                          fill="none"
                          strokeDasharray={circumference}
                          strokeDashoffset={circumference - progress}
                          strokeLinecap="round"
                          rotation="-90"
                          origin={`${size / 2},${size / 2}`}
                        />
                      </Svg>
                      <View
                        style={{
                          position: "absolute",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                          {temperature || "0"} ํC
                        </Text>
                      </View>
                    </View>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("temperature")}
                    </Text>
                  </View>
                  <View style={{ margin: moderateScale(5) }} />
                  {/* {แสงแดด} */}
                  <View style={[btn.btn_boxDetilIot, oth.bgIotFDFFB4]}>
                    <Image
                      style={{ width: 35, height: 35 }}
                      source={Images.theSun}
                      resizeMode="cover"
                    />
                    <View style={{ margin: moderateScale(2) }} />
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {sunlight} %
                    </Text>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("sunlight")}
                    </Text>
                  </View>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                <View style={ctn.ctn_spaceCenter}>
                  <View style={[btn.btn_boxDetilIot, oth.bgIotAECAFF]}>
                    <Text style={[fonstStyle.f24_medium]}>N</Text>
                    <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                      {n || "0"}
                    </Text>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("nitrogen")}
                    </Text>
                  </View>
                  <View style={{ margin: moderateScale(5) }} />
                  <View style={[btn.btn_boxDetilIot, oth.bgIotFEB7B1]}>
                    <Text style={[fonstStyle.f24_medium]}>P</Text>
                    <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                      {p || "0"}
                    </Text>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("phosphorus")}
                    </Text>
                  </View>
                  <View style={{ margin: moderateScale(5) }} />
                  <View style={[btn.btn_boxDetilIot, oth.bgIotFFDAC0]}>
                    <Text style={[fonstStyle.f24_medium]}>K</Text>
                    <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                      {k || "0"}
                    </Text>
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      {t("potassium")}
                    </Text>
                  </View>
                </View>
                <View style={{ margin: moderateScale(5) }} />
              </ScrollView>
            </ImageBackground>
          </View>
        </Modal>
        {/* {แจ้งเตือน บันทึกพื้นที่} */}
        <Modal animationType="fade" transparent={true} visible={modalSaveArea}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_Success}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_C0D576,
                    LgColor.Lg_A1BD57,
                    LgColor.Lg_78994A,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                  {t("record_area")}
                </Text>
              </View>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity onPress={() => setModalSaveArea(false)}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_FFE2BF,
                      LgColor.Lg_FFB155,
                      LgColor.Lg_FF8A00,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonCancle}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("cancel")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity onPress={() => handleSaveData()}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_C0D576,
                      LgColor.Lg_A1BD57,
                      LgColor.Lg_78994A,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonAgree}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("confirm")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน ชื่อใหม่} */}
        <Modal animationType="fade" transparent={true} visible={modalSaveName}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_Success}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_C0D576,
                    LgColor.Lg_A1BD57,
                    LgColor.Lg_78994A,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                  {t("confirm_name")}
                </Text>
              </View>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity onPress={() => setModalSaveName(false)}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_FFE2BF,
                      LgColor.Lg_FFB155,
                      LgColor.Lg_FF8A00,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonCancle}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("cancel")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity onPress={() => callUpdatePlotName()}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_C0D576,
                      LgColor.Lg_A1BD57,
                      LgColor.Lg_78994A,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonAgree}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("confirm")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน ลบ} */}
        <Modal animationType="fade" transparent={true} visible={modalDelete}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("delete_area")}
                </Text>
              </View>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity onPress={() => setModalDelete(false)}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_FFE2BF,
                      LgColor.Lg_FFB155,
                      LgColor.Lg_FF8A00,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonCancle}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("cancel")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity onPress={() => callDeleteArea()}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_C0D576,
                      LgColor.Lg_A1BD57,
                      LgColor.Lg_78994A,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonAgree}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("confirm")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน ไม่สามารถแบ่งแปลงได้} */}
        <Modal animationType="fade" transparent={true} visible={modalBuildArea}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("unable_to_divide")}
                </Text>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("maximum_limit")}
                </Text>
              </View>
              <TouchableOpacity onPress={() => setModalBuildArea(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("agree")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน สร้างแปลงนอกขอบเขต} */}
        <Modal animationType="fade" transparent={true} visible={modalOutArea}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("out_of_area")}
                </Text>
              </View>
              {/* <TouchableOpacity
                  style={[btn.btn_FlaseCancle]}
                  onPress={() => setOutArea(false)}
                >
                  <Text style={[txt.txt_center, fonstStyle.f12_light]}>
                    {t("agree")}
                  </Text>
                </TouchableOpacity> */}
              <TouchableOpacity onPress={() => setOutArea(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("agree")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน สร้างแปลงเกินจำนวน} */}
        <Modal animationType="fade" transparent={true} visible={modalMaxPlans}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("unable_area")}
                </Text>
              </View>
              <TouchableOpacity onPress={() => setModalMaxPlans(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("agree")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน เกิดข้อผิดพลาด} */}
        <Modal animationType="fade" transparent={true} visible={modalError}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("something")}
                </Text>
              </View>
              <TouchableOpacity onPress={() => setModalError(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("agree")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* {แจ้งเตือน แปลงซ้อน} */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalOverPolygon}
        >
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_FlaseCancle}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                  {t("unable_create_overlapping")}
                </Text>
              </View>
              <TouchableOpacity onPress={() => setModalOverPolygon(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("agree")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* Intro */}
        <Modal animationType="slide" transparent={true} visible={Intro}>
          <AppIntroSlider
            renderItem={_renderItem}
            data={slides}
            onDone={onDoneIntro}
            showSkipButton
            renderSkipButton={() => (
              <Text style={[fonstStyle.f16_bold, txt.txt_white]}>
                {t("skip")}
              </Text>
            )}
            renderNextButton={() => (
              <Text style={[fonstStyle.f16_bold, txt.txt_white]}>
                {t("next")}
              </Text>
            )}
            renderDoneButton={() => (
              <Text style={[fonstStyle.f16_bold, txt.txt_white]}>
                {t("confirm")}
              </Text>
            )}
            // onSkip={_onDone}
          />
        </Modal>
        {isPages === "MaketFram" ? null : controlBotton()}
      </>
    );
  };
  const timeStart = () => (
    <DatePicker
      modal
      mode="time"
      open={open}
      date={date}
      title={null}
      confirmText={t("confirm")}
      cancelText={t("cancel")}
      onConfirm={(selectedDate: Date) => {
        setOpen(false);
        setDate(selectedDate);
      }}
      onCancel={() => {
        setOpen(false);
      }}
    />
  );
  const timeEnd = () => (
    <DatePicker
      modal
      mode="time"
      open={openEnd}
      date={dateEnd}
      title={null}
      confirmText={t("confirm")}
      cancelText={t("cancel")}
      onConfirm={(selectedDateEnd: Date) => {
        setOpenEnd(false);
        setDateEnd(selectedDateEnd);
      }}
      onCancel={() => {
        setOpenEnd(false);
      }}
    />
  );
  const controlHeader = () => {
    return (
      <>
        <View
          style={
            orientation === "landscape"
              ? ctn.ctn_headerControlRowLan
              : ctn.ctn_headerControl
          }
        >
          {isMenuHeader && (
            <>
              {isPages != "MaketFram" && (
                <>
                  <TouchableOpacity onPress={() => goListManageFarm()}>
                    {selectedManage.length > 0 && (
                      <Badge
                        value={selectedManage.length}
                        status="error"
                        badgeStyle={
                          orientation === "landscape"
                            ? oth.bageNotiLan
                            : oth.bageNoti
                        }
                      />
                    )}
                    <LinearGradient
                      colors={linearGreen}
                      start={{ x: 0.0, y: 0.05 }}
                      end={{ x: 0.5, y: 1.0 }}
                      style={ctn.ctn_boxBgHeaer}
                    />
                    <Image
                      style={img.img_imgHeaer}
                      source={Images.vegetables}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(5) }} />
                </>
              )}
              <TouchableOpacity onPress={() => onModalDetail()}>
                <LinearGradient
                  colors={linearGreen}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={ctn.ctn_boxBgHeaer}
                />
                <Image
                  style={img.img_imgHeaer}
                  source={Images.SmartFarm}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
              {isPages != "MaketFram" && (
                <>
                  <TouchableOpacity onPress={() => goOperation("gallery")}>
                    <LinearGradient
                      colors={linearGreen}
                      start={{ x: 0.0, y: 0.05 }}
                      end={{ x: 0.5, y: 1.0 }}
                      style={[ctn.ctn_boxBgHeaer]}
                    />
                    <Image
                      style={[img.img_imgHeaer]}
                      source={Images.picture}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(5) }} />
                </>
              )}
              <TouchableOpacity onPress={() => goOperation("cctv")}>
                <LinearGradient
                  colors={linearGreen}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={[ctn.ctn_boxBgHeaer]}
                />
                <Image
                  style={[img.img_imgHeaer]}
                  source={Images.cctv}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
              {isPages != "MaketFram" && (
                <>
                  <TouchableOpacity onPress={() => goOperation("noti")}>
                    {notificationCountFram != 0 && (
                      <Badge
                        value={notificationCountFram}
                        status="error"
                        badgeStyle={
                          orientation === "landscape"
                            ? oth.bageNotiLan
                            : oth.bageNoti
                        }
                      />
                    )}
                    <LinearGradient
                      colors={linearGreen}
                      start={{ x: 0.0, y: 0.05 }}
                      end={{ x: 0.5, y: 1.0 }}
                      style={[ctn.ctn_boxBgHeaer]}
                    />
                    <Image
                      style={[img.img_imgHeaer]}
                      source={Images.bell}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(5) }} />
                </>
              )}
              {isPages != "MaketFram" && (
                <>
                  <TouchableOpacity onPress={() => onChantFarm()}>
                    <LinearGradient
                      colors={linearGreen}
                      start={{ x: 0.0, y: 0.05 }}
                      end={{ x: 0.5, y: 1.0 }}
                      style={[ctn.ctn_boxBgHeaer]}
                    />
                    <Image
                      style={[img.img_imgHeaer]}
                      source={Images.chat}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(5) }} />
                </>
              )}
              <TouchableOpacity onPress={() => handleReturnToRegion()}>
                <LinearGradient
                  colors={linearSky}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={ctn.ctn_boxBgHeaer}
                />
                <Image
                  style={img.img_Heaer}
                  source={Images.gps}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
              <TouchableOpacity onPress={() => setIntro(true)}>
                <LinearGradient
                  colors={linearSky}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={ctn.ctn_boxBgHeaer}
                />
                <Image
                  style={img.img_Info}
                  source={Images.info}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
            </>
          )}
          <TouchableOpacity onPress={() => onMenuHerder()}>
            {notificationCountFram != 0 && !isMenuHeader && (
              <Badge
                value={notificationCountFram}
                status="error"
                badgeStyle={
                  orientation === "landscape" ? oth.bageNotiLan : oth.bageNoti
                }
              />
            )}
            <LinearGradient
              colors={isMenuHeader ? lineOrange : linearSky}
              start={{ x: 0.0, y: 0.05 }}
              end={{ x: 0.5, y: 1.0 }}
              style={ctn.ctn_boxBgHeaer}
            >
              {!isMenuHeader ? (
                <Image
                  style={img.img_controlBottom}
                  source={Images.list}
                  resizeMode="cover"
                />
              ) : (
                iconScreenClose()
              )}
            </LinearGradient>
          </TouchableOpacity>
          <View style={{ margin: moderateScale(5) }} />
        </View>
      </>
    );
  };
  const controlBotton = () => {
    const hasFalse = confirmList.some((value) => value === false);
    return (
      <View
        style={[
          isModalMenu
            ? [
                ctn.ctn_control,
                {
                  flexDirection: orientation === "portrait" ? "column" : "row",
                },
              ]
            : ctn.ctn_controlPlus,
        ]}
      >
        {isModalMenu ? (
          <>
            <TouchableOpacity
              onPress={() => onBottonType2()}
              disabled={activeButton === 2 ? true : false}
            >
              <LinearGradient
                colors={[LgColor.Lg_B8EFB2, LgColor.Lg_B8EFB2]}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={btn.btn_bottomMenu}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={img.img_controlBottom}
                  source={Images.Control2}
                  resizeMode="cover"
                />
              </LinearGradient>
              <Text style={[fonstStyle.f10_bold, txt.txt_bottomControl]}>
                {t("share_plot")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity
              onPress={() =>
                polygonsPlanting != "" ? onBottonType1() : undefined
              }
              disabled={activeButton === 1 ? true : false}
            >
              <LinearGradient
                colors={linearPurple}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={
                  polygonsPlanting != ""
                    ? btn.btn_bottomMenu
                    : btn.btn_bottomMenuNon
                }
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={img.img_controlBottom}
                  source={Images.Control1}
                  resizeMode="cover"
                />
              </LinearGradient>
              <Text style={[fonstStyle.f10_bold, txt.txt_bottomControl]}>
                {t("farm")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity
              onPress={() =>
                polygonsPlanting != "" && hasFalse ? onBottonType3() : undefined
              }
              disabled={activeButton === 3 ? true : false}
            >
              <LinearGradient
                colors={[LgColor.Lg_FFDAE1, LgColor.Lg_FFDAE1]}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={
                  polygonsPlanting != "" && hasFalse
                    ? btn.btn_bottomMenu
                    : btn.btn_bottomMenuNon
                }
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={img.img_controlBottom}
                  source={Images.Control3}
                  resizeMode="cover"
                />
              </LinearGradient>
              <Text style={[fonstStyle.f10_bold, txt.txt_bottomControl]}>
                {t("Planting_Notification")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity
              onPress={() =>
                polygonsPlanting != "" && isManagement === true
                  ? onBottonType4()
                  : undefined
              }
              disabled={activeButton === 4 ? true : false}
            >
              <LinearGradient
                colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={
                  polygonsPlanting != "" && isManagement === true
                    ? btn.btn_bottomMenu
                    : btn.btn_bottomMenuNon
                }
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={img.img_controlBottom}
                  source={Images.Control4}
                  resizeMode="cover"
                />
              </LinearGradient>
              <Text style={[fonstStyle.f10_bold, txt.txt_bottomControl]}>
                {t("Manage_conversion")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />
          </>
        ) : null}

        <TouchableOpacity
          onPress={() =>
            isModalMenu || polygonMode == true ? onMenuClose() : onMenu()
          }
        >
          <LinearGradient
            colors={
              isModalMenu || polygonMode == true ? lineOrange : linearGreen
            }
            start={{ x: 0.0, y: 0.05 }}
            end={{ x: 0.5, y: 1.0 }}
            style={btn.btn_bottomMenu}
          >
            <Image
              style={img.img_iconEllipse}
              source={Images.Ellipse}
              resizeMode="contain"
            />

            {isModalMenu || polygonMode == true
              ? iconScreenClose()
              : iconScreenPlus()}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };
  const _renderItem = ({ item }: any) => (
    <View
      style={[
        {
          flex: 1,
          padding: 20,
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: item.backgroundColor,
        },
      ]}
    >
      <Text style={[fonstStyle.f16_bold, txt.txt_white]}>{item.title}</Text>
      {/* <Image source={item.image} style={styles.image} /> */}
      <Text style={[fonstStyle.f16_medium, txt.txt_white]}>{item.text}</Text>
    </View>
  );
  const renderManageFram = ({ item }: any) => {
    const isSelected = selectedManage.some(
      (selectedManage) => selectedManage.id === item.id
    );
    return (
      <View style={{ padding: moderateScale(5) }}>
        <LinearGradient
          colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
          start={{ x: 0.0, y: 0.05 }}
          end={{ x: 0.5, y: 1.0 }}
          style={ctn.ctn_mange}
        >
          <Image
            style={img.img_iconEllipse}
            source={Images.Ellipse}
            resizeMode="contain"
          />
          {item.imageUrl === null ? (
            <View style={img.img_noImage}>{iconNoImg()}</View>
          ) : (
            <Image
              style={img.img_Manage}
              source={{ uri: item.imageUrl }}
              resizeMode="cover"
            />
          )}
          <Text
            numberOfLines={1}
            style={[
              fonstStyle.f12_bold,
              txt.txt_606060,
              { padding: moderateScale(5) },
            ]}
          >
            {item.serviceName}
          </Text>
          <View style={{ margin: moderateScale(5) }} />

          <TouchableOpacity onPress={() => toggleManage(item)}>
            <LinearGradient
              colors={!isSelected ? linearDis : linearGreen}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 0.0, y: 1.0 }}
              style={ctn.ctn_boxSelect}
            >
              <View
                style={{ alignItems: "center", marginTop: moderateScale(2) }}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  ฿ {item.price === 0 ? t("free") : item.price}
                </Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    );
  };
  const renderProduct = ({ item }: any) => {
    const isSelected = selectedItems.some(
      (selectedItems) => selectedItems.id === item.id
    );
    return (
      <View style={{ padding: moderateScale(5) }}>
        <LinearGradient
          colors={[LgColor.Lg_FFF1D8, LgColor.Lg_fff1d8]}
          start={{ x: 0.0, y: 0.05 }}
          end={{ x: 0.5, y: 1.0 }}
          style={ctn.ctn_contentProduct}
        >
          <Image
            style={img.img_iconEllipse}
            source={Images.Ellipse}
            resizeMode="contain"
          />
          {item.imageUrl === null ? (
            <View style={img.img_noImage}>{iconNoImg()}</View>
          ) : (
            <Image
              style={img.img_Product}
              source={{ uri: item.imageUrl }}
              resizeMode="cover"
            />
          )}
          {/* {Name Price} */}
          <Text
            numberOfLines={1}
            style={[
              fonstStyle.f12_bold,
              txt.txt_606060,
              { padding: moderateScale(5) },
            ]}
          >
            {item.plantName}
          </Text>

          <Text
            numberOfLines={1}
            style={[
              fonstStyle.f12_medium,
              txt.txt_606060,
              // { padding: moderateScale(5) },
            ]}
          >
            {item.harvestCountdown || "-"} {t("day")}
          </Text>
          {/* <Text>{item.isCanHarvest}</Text> */}
          <View style={{ margin: moderateScale(5) }} />

          {/* {Botton Price} */}
          <TouchableOpacity
            onPress={() => toggleProduct(item)}
            disabled={item.isCanHarvest === false}
          >
            <LinearGradient
              colors={!isSelected ? linearDis : linearGreen}
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 0.0, y: 1.0 }}
              style={ctn.ctn_boxSelect}
            >
              <View
                style={{ alignItems: "center", marginTop: moderateScale(2) }}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {item.isCanHarvest === false ? "รอเก็บเกี่ยว" : t("select")}
                </Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    );
  };
  const renderInFrom = ({ item }: any) => {
    return (
      <>
        {item.isConfirm === false ? (
          <View style={{ alignItems: "center", padding: moderateScale(5) }}>
            <LinearGradient
              colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
              start={{ x: 0.0, y: 0.05 }}
              end={{ x: 0.5, y: 5.0 }}
              style={ctn.ctn_InFrom}
            >
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="contain"
              />
              <View style={ctn.ctn_flexInFrom}>
                <View style={ctn.ctn_bgPlansInFrom}>
                  {item.imageUrl === null ? (
                    <View style={img.img_PalnsInFrom}>{iconNoImg()}</View>
                  ) : (
                    <Image
                      style={img.img_PalnsInFrom}
                      source={{ uri: item.imageUrl }}
                      resizeMode="contain"
                    />
                  )}
                </View>
                <View style={{ margin: moderateScale(8) }} />
                <View style={{ width: "50%", justifyContent: "center" }}>
                  <Text
                    style={[fonstStyle.f14_bold, txt.txt_606060]}
                    numberOfLines={5}
                  >
                    {item.plantName}
                  </Text>
                  <View style={{ margin: moderateScale(2) }} />
                  <Text
                    style={[fonstStyle.f12_medium, txt.txt_606060]}
                    numberOfLines={5}
                  >
                    {item.plantType}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />
              </View>
            </LinearGradient>
          </View>
        ) : null}
      </>
    );
  };
  const renderWatering = ({ item, index }: any) => {
    return (
      <View style={ctn.ctn_Watering}>
        <View style={ctn.ctn_flexInFrom}>
          <View style={{ flexDirection: "row" }}>
            <View style={{ flexDirection: "column" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {item.startTime} - {item.endTime}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <View style={{ flexDirection: "row" }}>
                {/* {Monday} */}
                <View
                  style={item.monday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.monday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Mon")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Tuesday} */}
                <View
                  style={item.tuesday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.tuesday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Tue")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Wednesday} */}
                <View
                  style={
                    item.wednesday === true ? oth.boxSelectDay : oth.boxDay
                  }
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.wednesday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Wed")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Thursday} */}
                <View
                  style={item.thursday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.thursday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Thu")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Friday} */}
                <View
                  style={item.friday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.friday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Fri")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Saturday} */}
                <View
                  style={item.saturday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.saturday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Sat")}
                  </Text>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Sunday} */}
                <View
                  style={item.sunday === true ? oth.boxSelectDay : oth.boxDay}
                >
                  <Text
                    style={[
                      fonstStyle.f10_light,
                      item.sunday === true ? txt.txt_white : txt.txt_606060,
                    ]}
                  >
                    {t("Sun")}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={{ margin: moderateScale(5) }} />
          <View style={oth.lineInFrom} />
          <View style={{ margin: moderateScale(5) }} />
          {loadingDelete ? (
            <ActivityIndicator size="small" color={BgColor.Bg_84B8A2} />
          ) : (
            <>
              <TouchableOpacity
                style={{ justifyContent: "center" }}
                onPress={() => onEditTimeWater(item, "typeEdit")}
              >
                {iconEdit()}
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={{ justifyContent: "center" }}
                // onPress={() => onDeleteConpfig(item)}
                onPress={() => toggleDelete(index)}
              >
                {iconDeleteOrange()}
              </TouchableOpacity>
            </>
          )}
        </View>

        {/* ลบรายการ */}
        {openIndex === index && (
          <>
            <View style={{ margin: moderateScale(5) }} />
            <View style={oth.line_profile} />
            <View style={ctn.ctn_deletListWater}>
              <TouchableOpacity
                style={btn.btn_cancelWater}
                onPress={() => toggleDelete(index)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(2) }} />

              <TouchableOpacity
                style={btn.btn_congirmDeleteWater}
                onPress={() => callDeleteConfig(item)}
              >
                {loadingDelete ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("confirm")}
                  </Text>
                )}
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
            </View>
            <View style={{ margin: moderateScale(5) }} />
          </>
        )}
      </View>
    );
  };
  const renderListStatus = ({ item }: any) => {
    return (
      <View style={ctn.ctn_WateringList}>
        <View style={[ctn.ctn_flexInFrom]}>
          <View style={ctn.ctn_iconWaterList}>
            <FastImage
              style={img.img_finish}
              source={Images.wateringList}
              resizeMode={FastImage.resizeMode.cover}
            />
          </View>
          <View style={{ margin: moderateScale(5) }} />
          <View style={{ flexDirection: "column" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {moment(item.requestedTime).format("DD/MM/YYYY")}
            </Text>
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {moment(item.startTime).format("HH:mm")} -{" "}
              {moment(item.endTime).format("HH:mm")}
            </Text>
            <View style={{ flexDirection: "row" }}>
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {t("status")}:{" "}
              </Text>
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {item.state}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderDetail = ({ item }: any) => {
    return (
      <>
        <View
          style={{ paddingHorizontal: moderateScale(10), alignItems: "center" }}
        >
          {item.imageUrl != null ? (
            <Image
              style={img.img_PalnsInFrom}
              source={{ uri: item.imageUrl }}
              resizeMode="contain"
            />
          ) : (
            <View style={img.img_RenderPlans}>{iconNoImg()}</View>
          )}
          <View style={{ margin: moderateScale(2) }} />
          <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
            {item.plantName}
          </Text>
        </View>
      </>
    );
  };
  const renderSelectPlans = ({ item }: any) => {
    return (
      <View style={[ctn.ctn_clickPlans]}>
        <LinearGradient
          colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
          start={{ x: 0.0, y: 0.05 }}
          end={{ x: 0.5, y: 1.0 }}
          style={oth.clickPlans}
        >
          <Image
            style={img.img_iconEllipse}
            source={Images.Ellipse}
            resizeMode="contain"
          />

          <View
            style={{ alignItems: "center", flex: 1, justifyContent: "center" }}
          >
            <Text
              numberOfLines={1}
              style={[fonstStyle.f12_bold, txt.txt_606060]}
            >
              {item.plantName}
            </Text>
            {item.imageUrl != null ? (
              <Image
                style={img.img_Manage}
                source={{ uri: item.imageUrl }}
                resizeMode="contain"
              />
            ) : (
              <View style={img.img_Manage}>{iconNoImg()}</View>
            )}
            <Text
              style={[fonstStyle.f12_bold, txt.txt_606060, txt.text_Harvest]}
            >
              {item.harvestPeriod} {t("day")}
            </Text>
            <TouchableOpacity
              onPress={() =>
                idSelectPlans === item.id ? cleanDocSelect() : selectPlans(item)
              }
              style={{ marginTop: moderateScale(24) }}
            >
              <LinearGradient
                colors={idSelectPlans === item.id ? lineOrange : linearGreen}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 0.0, y: 1.0 }}
                style={[oth.boxSelect]}
              >
                <View style={{ alignItems: "center" }}>
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {idSelectPlans === item.id ? t("cancel") : t("select")}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };
  const renderWaitList = ({ item, index }: any) => {
    return (
      <View style={ctn.ctn_WaitList}>
        <View style={ctn.ctn_spaceBet}>
          <View style={{ flexDirection: "row" }}>
            <View style={oth.paymentIcon}>
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>$</Text>
            </View>
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.paidStatus}
            </Text>
          </View>
          <Text style={[fonstStyle.f14_bold, txt.txt_green]}>
            ฿ {item.invoiceAmount}
          </Text>
        </View>
        <View style={{ margin: moderateScale(5) }} />
        <View style={oth.line_profile} />

        {openIndexProduct != index && (
          <>
            <View style={{ margin: moderateScale(5) }} />
            <TouchableOpacity
              style={ctn.ctn_moreProduct}
              onPress={() => toggleOpenProduct(index)}
            >
              <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                {t("more")}
              </Text>
              <View style={{ margin: moderateScale(2) }} />
              {iconDownProduct()}
            </TouchableOpacity>
          </>
        )}

        {openIndexProduct === index && (
          <>
            <View style={{ margin: moderateScale(5) }}>
              {/* แสดง serviceName จาก details */}
              {item.details.map((detail: any) => (
                <View style={ctn.ctn_spaceWait}>
                  <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                    {detail.serviceName}
                  </Text>

                  <View style={{ flexDirection: "row" }}>
                    {detail.price === 0 ? (
                      <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                        ฿ {t("free")}
                      </Text>
                    ) : (
                      <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                        ฿ {detail.price}
                      </Text>
                    )}
                    <View style={{ margin: moderateScale(5) }} />
                    <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                      x {detail.quantity}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
            <View style={{ margin: moderateScale(5) }} />
          </>
        )}

        {loadingDelete ? (
          <ActivityIndicator size="small" color={BgColor.Bg_84B8A2} />
        ) : (
          <>
            {openIndexProduct === index && (
              <View style={ctn.ctn_spaceEnd}>
                <TouchableOpacity
                  // onPress={() => goReturnPay("typePayAfter", item)}
                  onPress={() => togglePayment(index)}
                  style={
                    openIndexPayMent === index
                      ? btn.btn_makePaymentNon
                      : btn.btn_makePayment
                  }
                  disabled={openIndexPayMent === index}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                    {t("Make_payment")}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{ padding: moderateScale(10) }}
                  onPress={() => toggleDeletePayment(index)}
                >
                  {iconDeletePayment()}
                </TouchableOpacity>
              </View>
            )}

            {openIndexProduct === index && (
              <>
                <View style={{ margin: moderateScale(5) }} />
                <TouchableOpacity
                  style={{ alignItems: "center" }}
                  onPress={() => toggleOpenProduct(index)}
                >
                  <View style={oth.cardIconUpProduct}>{iconUpProduct()}</View>
                </TouchableOpacity>
              </>
            )}
          </>
        )}

        {openIndexPayMent === index && openIndexProduct === index && (
          <>
            <View style={{ margin: moderateScale(5) }} />
            <View style={oth.line_profile} />
            <PayMentManage
              paymentMethods={paymentMethods}
              selectedIndex={selectedIndex}
              selectPayMent={selectPayMent}
              btn={btn}
              iconBanking={iconBanking}
              iconBankingChang={iconBankingChang}
              moderateScale={moderateScale}
              BgColor={BgColor}
            />
            <View style={ctn.ctn_deletListWater}>
              <TouchableOpacity
                style={btn.btn_cancelWater}
                onPress={() => togglePayment(index)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(2) }} />

              <TouchableOpacity
                style={
                  selectedIndex === null
                    ? btn.btn_congirmDeleteWaterNon
                    : btn.btn_congirmDeleteWater
                }
                disabled={selectedIndex === null}
                onPress={() => goPayMent(item, "typePayAfter")}
              >
                {loadingDelete ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("confirm")}
                  </Text>
                )}
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
            </View>
          </>
        )}

        {openIndexPay === index && openIndexProduct === index && (
          <>
            <View style={{ margin: moderateScale(5) }} />
            <View style={oth.line_profile} />
            <View style={ctn.ctn_deletListWater}>
              <TouchableOpacity
                style={btn.btn_cancelWater}
                onPress={() => toggleDeletePayment(index)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(2) }} />

              <TouchableOpacity
                style={btn.btn_congirmDeleteWater}
                onPress={() => callDeleteInvoice(item)}
              >
                {loadingDelete ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("confirm")}
                  </Text>
                )}
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
            </View>
            <View style={{ margin: moderateScale(2) }} />
          </>
        )}
      </View>
    );
  };
  const renderPlansNoti = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalPlantingNoti}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_Success}>
              <LinearGradient
                colors={[
                  LgColor.Lg_C0D576,
                  LgColor.Lg_A1BD57,
                  LgColor.Lg_78994A,
                ]}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={oth.bg_Success}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />
                {iconPlant()}
              </LinearGradient>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_planting")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity onPress={() => setModalPlantingNoti(false)}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_FFE2BF,
                    LgColor.Lg_FFB155,
                    LgColor.Lg_FF8A00,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonCancle}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("cancel")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity onPress={() => callConfirmPlanting()}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_C0D576,
                    LgColor.Lg_A1BD57,
                    LgColor.Lg_78994A,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={btn.btn_bottonAgree}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {t("confirm")}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const renderAreaPlant = ({ item }: any) => {
    return (
      <View
        style={{
          marginTop: moderateScale(10),
          paddingHorizontal: moderateScale(10),
        }}
      >
        <LinearGradient
          colors={item.isCanEdit === true ? lineOrange : linearGreen}
          start={{ x: 0.0, y: 0.05 }}
          end={{ x: 0.5, y: 5.0 }}
          style={ctn.ctn_DeleteArea}
        >
          <Image
            style={img.img_iconEllipse}
            source={Images.Ellipse}
            resizeMode="contain"
          />
          <View style={ctn.ctn_detailDelete}>
            <View style={{ flexDirection: "row" }}>
              {item.imageUrl === null ? (
                <View style={img.img_PalnsInFrom}>{iconNoImg()}</View>
              ) : (
                <Image
                  style={img.img_PalnsInFrom}
                  source={{ uri: item.imageUrl }}
                  resizeMode="contain"
                />
              )}
              <View style={{ margin: moderateScale(5) }} />
              <View style={{ justifyContent: "center" }}>
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {item.plantName}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                <Text style={[fonstStyle.f12_medium, txt.txt_606060]}>
                  {item.plantType}
                </Text>
              </View>
            </View>

            <TouchableOpacity
              onPress={() =>
                checkAreaPant === item.id ? cleanArea() : selectArea(item)
              }
              style={ctn.ctn_iconMapArea}
            >
              <FastImage
                style={img.img_mapSelect}
                source={
                  checkAreaPant === item.id ? Images.Mapselect : Images.Mapnon
                }
                resizeMode={FastImage.resizeMode.cover}
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => onConfirmDelete(item)}
              style={
                item.isCanEdit === true
                  ? ctn.ctn_iconDeleteArea
                  : ctn.ctn_iconDeleteAreafalse
              }
              disabled={item.isCanEdit === false}
            >
              {/* Removerubbish */}
              {item.isCanEdit === true ? (
                <FastImage
                  style={img.img_removeArea}
                  source={Images.Removerubbish}
                  resizeMode={FastImage.resizeMode.cover}
                />
              ) : (
                <View style={ctn.ctn_checkAreaFalse}>
                  <FastImage
                    style={img.img_chckeAreaFalse}
                    source={Images.finishStatus}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                </View>
              )}
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };
  const renderWaiting = ({ item }: any) => {
    return (
      <View style={ctn.ctn_Finish}>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <View style={ctn.ctn_iconfinish}>
            {item.imageUrl === null ? (
              <FastImage
                style={img.img_finish}
                source={Images.menu1}
                resizeMode={FastImage.resizeMode.cover}
              />
            ) : (
              <FastImage
                style={img.img_finish}
                source={{ uri: item.imageUrl }}
                resizeMode={FastImage.resizeMode.cover}
              />
            )}
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={{ flexDirection: "column", width: "80%" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.serviceName}
            </Text>
            <Text style={[fonstStyle.f12_medium, txt.txt_606060]}>
              {item.plantName}
            </Text>
            {item.remarks && item.remarks.length > 0 && (
              <>
                <View style={{ margin: moderateScale(2) }} />
                <View style={oth.line_profile} />
                <View style={{ margin: moderateScale(2) }} />
              </>
            )}
            {item.remarks && item.remarks.length > 0 && (
              <>
                {item.remarks.map((remark: any, index: number) => (
                  <Text
                    style={[fonstStyle.f12_medium, txt.txt_606060]}
                    key={index}
                  >
                    {remark}
                  </Text>
                ))}
              </>
            )}
          </View>
        </View>
      </View>
    );
  };
  const renderInProgress = ({ item }: any) => {
    return (
      <View style={ctn.ctn_Finish}>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <View style={ctn.ctn_iconfinish}>
            {item.imageUrl === null ? (
              <FastImage
                style={img.img_finish}
                source={Images.menu1}
                resizeMode={FastImage.resizeMode.cover}
              />
            ) : (
              <FastImage
                style={img.img_finish}
                source={{ uri: item.imageUrl }}
                resizeMode={FastImage.resizeMode.cover}
              />
            )}
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={{ flexDirection: "column", width: "80%" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.serviceName}
            </Text>
            <Text style={[fonstStyle.f12_medium, txt.txt_606060]}>
              {item.plantName}
            </Text>
            {item.remarks && item.remarks.length > 0 && (
              <>
                <View style={{ margin: moderateScale(2) }} />
                <View style={oth.line_profile} />
                <View style={{ margin: moderateScale(2) }} />
              </>
            )}
            {item.remarks && item.remarks.length > 0 && (
              <>
                {item.remarks.map((remark: any, index: number) => (
                  <Text
                    style={[fonstStyle.f12_medium, txt.txt_606060]}
                    key={index}
                  >
                    {remark}
                  </Text>
                ))}
              </>
            )}
          </View>
        </View>
      </View>
    );
  };
  const renderFinish = ({ item }: any) => {
    return (
      <View style={ctn.ctn_Finish}>
        <View style={[ctn.ctn_spaceBet, { alignItems: "center" }]}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View style={ctn.ctn_iconfinish}>
              {item.imageUrl === null ? (
                <FastImage
                  style={img.img_finish}
                  source={Images.menu1}
                  resizeMode={FastImage.resizeMode.cover}
                />
              ) : (
                <FastImage
                  style={img.img_finish}
                  source={{ uri: item.imageUrl }}
                  resizeMode={FastImage.resizeMode.cover}
                />
              )}
            </View>
            <View style={{ margin: moderateScale(10) }} />
            <View style={{ flexDirection: "column", width: "65%" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {item.serviceName}
              </Text>
              <Text style={[fonstStyle.f12_medium, txt.txt_606060]}>
                {item.plantName || "-"}
              </Text>
              {item.remarks && item.remarks.length > 0 && (
                <>
                  <View style={{ margin: moderateScale(2) }} />
                  <View style={oth.line_profile} />
                  <View style={{ margin: moderateScale(2) }} />
                </>
              )}
              {item.remarks && item.remarks.length > 0 && (
                <>
                  {item.remarks.map((remark: any, index: number) => (
                    <Text
                      style={[fonstStyle.f12_medium, txt.txt_606060]}
                      key={index}
                    >
                      {remark}
                    </Text>
                  ))}
                </>
              )}
            </View>
          </View>
          <FastImage
            style={img.img_Checkfinish}
            source={Images.finishStatus}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>
      </View>
    );
  };
  const buttomContal = () => {
    return (
      <View style={ctn.ctn_bottomSavePlant}>
        <View
          style={{
            flexDirection: "row",
            backgroundColor: BgOpacity.Op_ffffff5,
            padding: 10,
            borderRadius: 26,
          }}
        >
          {/* {Save} */}
          <View style={{ flexDirection: "column" }}>
            <TouchableOpacity
              onPress={() =>
                docSelectPlans != "" ? handleSavePolygon() : null
              }
            >
              <LinearGradient
                colors={linearGreen}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={
                  docSelectPlans != ""
                    ? btn.btn_saveButton
                    : btn.btn_saveButtonNon
                }
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={{
                    width: moderateScale(18),
                    height: moderateScale(18),
                  }}
                  source={Images.save}
                  resizeMode="cover"
                />
              </LinearGradient>
            </TouchableOpacity>
            <Text
              style={[
                fonstStyle.f10_bold,
                txt.txt_606060,
                { textAlign: "center" },
              ]}
            >
              {t("save")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(5) }} />

          <View style={{ flexDirection: "column" }}>
            <TouchableOpacity
              onPress={() => onBottonType2()}
              disabled={activeButton === 2 ? true : false}
            >
              <LinearGradient
                colors={[LgColor.Lg_B8EFB2, LgColor.Lg_B8EFB2]}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={btn.btn_boxBottonMenu}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />

                <Image
                  style={{
                    width: moderateScale(25),
                    height: moderateScale(25),
                  }}
                  source={Images.Control2}
                  resizeMode="cover"
                />
              </LinearGradient>
            </TouchableOpacity>
            <Text
              style={[
                fonstStyle.f10_bold,
                txt.txt_606060,
                { textAlign: "center" },
              ]}
            >
              {t("share_plot")}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  const buttomClose = () => {
    return (
      <View style={[ctn.ctn_controlPlus]}>
        <TouchableOpacity onPress={() => onTypeclose()}>
          <LinearGradient
            colors={[LgColor.Lg_FFE2BF, LgColor.Lg_FFB155, LgColor.Lg_FF8A00]}
            start={{ x: 0.0, y: 0.05 }}
            end={{ x: 0.5, y: 1.0 }}
            style={btn.btn_bottomMenu}
          >
            <Image
              style={img.img_iconEllipse}
              source={Images.Ellipse}
              resizeMode="contain"
            />
            {iconScreenClose()}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };
  const buttomComfirmPlans = () => {
    return (
      <>
        {plotDetail != "" ? (
          <View style={ctn.ctn_BottomInfrom}>
            <TouchableOpacity
              onPress={() => onCloseWater()}
              style={btn.btn_canCelYseNo}
            >
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="contain"
              />
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                {t("cancel")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity
              style={btn.btn_conFirmYseNo}
              onPress={() => onModalPlantingNoti()}
            >
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="contain"
              />
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                {t("Planting_Notification")}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={{ paddingHorizontal: moderateScale(8) }}>
            <TouchableOpacity
              onPress={() => onCloseWater()}
              style={oth.canCelClose}
            >
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="contain"
              />
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                {t("close")}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };

  if (!region) {
    return <LoadingFarm />;
  }

  return (
    <>
      <StatusBar
        barStyle={Platform.OS === "ios" ? "light-content" : "dark-content"}
        hidden={false}
      />
      {mapReady === false ? <LoadingFarm /> : null}
      <View style={ctn.continue}>
        {mapView()}
        {controlHeader()}
        {renderPlansNoti()}
        {timeStart()}
        {timeEnd()}
        {activeButton === 1 ? (
          <BottomSheet
            index={0}
            ref={bottomSheetRef}
            snapPoints={snapPoints}
            // enablePanDownToClose={true}
            backgroundStyle={{
              backgroundColor: BgOpacity.Op_C5BBDE,
            }}
          >
            <BottomSheetView>{FlasListAreaPlant()}</BottomSheetView>
          </BottomSheet>
        ) : null}
        {activeButton === 3 ? (
          <BottomSheet
            index={0}
            ref={bottomSheetRef}
            snapPoints={snapPointsComfirm}
            // enablePanDownToClose={true}
            backgroundStyle={{
              backgroundColor: BgOpacity.Op_FFDAE1,
            }}
          >
            <BottomSheetView>{FlasListInfrom()}</BottomSheetView>
          </BottomSheet>
        ) : null}
        {isChatFarm === true ? (
          <SelectChat
            onCloseChat={() => setChatFarm(false)}
            navigation={navigation}
          />
        ) : null}
        {activeButton === 1 ? buttomContal() : null}
        {activeButton === 1 ? buttomClose() : null}
        {activeButton === 3 ? buttomComfirmPlans() : null}
      </View>
    </>
  );
}
