import {
  Text,
  View,
  Modal,
  Image,
  Alert,
  FlatList,
  Platform,
  StatusBar,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import moment from "moment";
import RNFS from "react-native-fs";
import { Badge } from "@rneui/themed";
import Video from "react-native-video";
import FastImage from "react-native-fast-image";
import { Header as HeaderRNE } from "@rneui/themed";
import ImageViewing from "react-native-image-viewing";
import { VolumeX, Volume2 } from "lucide-react-native";
import React, { useState, useEffect, useRef } from "react";
import { useOrientation } from "../../../../hooks/useOrientation";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { verticalScale, moderateScale } from "react-native-size-matters";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import {
  Swipeable,
  GestureHandlerRootView,
} from "react-native-gesture-handler";
//Style
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import img from "../../../../styleSheet/img";
import txt from "../../../../styleSheet/txt";
import oth from "../../../../styleSheet/oth";
import mod from "../../../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Svg
import { goBack_gay } from "../../../../assets/svg/svg_naviagte";
import {
  iconError,
  iconFullscreen,
  iconCameraEdit,
  iconDeleteNoti,
  iconUploadMedia,
  iconGalleryEdit,
  iconDeleteGallery,
} from "../../../../assets/svg/svg_other";
//Components
import Loading from "../../../../components/loading/loading";
import LoadingApp from "../../../../components/loading/loadingApp";
//Api
import {
  pusCount,
  deleteNoti,
  unReadNoti,
  pushNotiList,
  pushReadFramAll,
} from "../../../../action/Mefarm_Realtime_API";
import {
  getImageFram,
  postUploadImages,
  deleteFarmImgVideo,
} from "../../../../action/Mefarm_Farm_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setImagesShow,
  setNotificationCount,
  setNotificationCountFram,
} from "../../../../Redux_Store/action";
//Translation
import { useTranslation } from "../../../i18n";
import CCTVPlayer from "../../../../components/cctv/CCTVPlayer";
import Images from "../../../../utils/imageManager";

export default function Operation({ navigation, route }: any) {
  const orientation = useOrientation();

  const params = route.params || "";
  const docList = params.docList || "";
  const plotName = docList.plotName || "";
  const isPages = params.isPages || "";
  const farmUserPlotId = params.farmUserPlotId || "";
  const [type, setType] = useState(params.type || "");
  // console.log(JSON.stringify(docList, null, 2));

  const dispatch = useDispatch();
  const notificationCount = useSelector(
    (state: any) => state.notificationCount
  );

  const notificationCountFram = useSelector(
    (state: any) => state.notificationCountFram
  );
  const imagesShow = useSelector((state: any) => state.imagesShow);
  //State
  const { t } = useTranslation();
  const actionSheetRef = useRef<ActionSheetRef>(null);
  //String
  const [idDelete, setIdDelete] = useState<string>("");
  const [deleteId, setDeleteId] = useState<string>("");
  const [cameraUrl, setCameraUrl] = useState<string>("");
  //Array
  const [docToday, setDocToday] = useState<any>([]);
  const [imageFull, setImageFull] = useState<any>([]);
  const [docDefore, setDocDefore] = useState<any>([]);
  const [imageUris, setImageUris] = useState<any>([]);
  const [fileNames, setFileNames] = useState<any>([]);
  const [typeNames, setTypeNames] = useState<any>([]);
  // const [imagesShow, setImagesShow] = useState<any>([]);
  //True & False
  const [refresh, setRefresh] = useState<boolean>(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isModalDelete, setModalDelete] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  const [isModalDeleteImg, setModalDeleteImg] = useState<boolean>(false);
  //Number
  const [countAll, setCountAll] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const { width } = Dimensions.get("window");
  // เล่ม state
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [isVideoLoading, setIsVideoLoading] = useState<boolean>(true);

  const handleScroll = (event: any) => {
    // Destructure nativeEvent immediately to avoid synthetic event pooling issues
    const nativeEvent = event.nativeEvent;
    const scrollPosition = nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / width);
    setActiveIndex(index);
  };
  //PERMISSIONS
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        // Alert.alert("❌ Camera permission denied");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  useEffect(() => {
    requestCameraPermission();
  }, []);
  useEffect(() => {
    callPushNotiList();
    callGallery();
    pushNotiCount();
    callCCTV();
  }, [refresh]);
  const callCCTV = () => {
    const plotDetail = docList.plotDetail || {};
    // console.log(plotDetail.cameraUrl);
    setCameraUrl(plotDetail.cameraUrl);
  };
  const callGallery = async () => {
    try {
      setLoadPosts(true);
      const minDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      const pageSizes = pageSize;
      const res = await getImageFram(farmUserPlotId, minDate, pageSizes);
      const dataImags = res.model || "";

      // const imageUrls = dataImags.map((item: any) => item.thumbnailUrl);
      const imageUrls = dataImags.map((item: any) => ({
        id: item.id,
        thumbnailUrl: item.thumbnailUrl,
      }));
      dispatch(setImagesShow(imageUrls));
      // console.log(JSON.stringify(imageUrls, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadPosts(false);
    }
  };
  const callPushNotiList = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        farmUserPlotId: farmUserPlotId,
        lastCreatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        pageSize: pageSize,
      };
      const res = await pushNotiList(req);
      // console.log(JSON.stringify(res, null, 2));
      const dataToday = res.model.today || [];
      const dataDefore = res.model.before || [];
      setDocToday(dataToday);
      setDocDefore(dataDefore);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const pushNotiReadAll = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const res = await pushReadFramAll(userIdLogin, farmUserPlotId);
      dispatch(setNotificationCount(0));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callReadNoti = async (item: any, index: number) => {
    try {
      setLoadIng(true);
      const notiId = item.id || "";
      const isRead = true || "";
      if (item.isRead === true) {
        undefined;
      } else {
        const res = await unReadNoti(notiId, isRead);
        callPushNotiList();
        dispatch(setNotificationCount(notificationCount - 1));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const pushNotiCount = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const res = await pusCount(userIdLogin, farmUserPlotId);
      const totalNotifications = res.model || "";
      setCountAll(totalNotifications);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callDeleteNoto = async () => {
    try {
      setLoadIng(true);
      const res = await deleteNoti(deleteId);
      callPushNotiList();
      setModalDelete(false);
      pushNotiCount();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callDeleteImages = async (imageId: string) => {
    try {
      setLoadIng(true);
      const req = {
        farmUserPlotId: farmUserPlotId, // ใส่ค่า farmUserPlotId เหมาะสม
        objectIds: [imageId], // ส่ง ID ของต้องการลบ
      };
      const res = await deleteFarmImgVideo(req);
      // console.log("ลบรูปสำเร็จ:", res);
      callGallery();
    } catch (error) {
      console.error("ข้อพลาดในการลบรูป:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const openDelete = (item: any, index: number) => {
    setModalDelete(true);
    setDeleteId(item.id);
  };
  const loadMoreData = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await callGallery();
    setLoadPosts(false);
  };
  const openFullImag = (item: any, id: any) => {
    setImageFull(item);
    setIdDelete(id);
    setModalFullImg(true);
  };
  const chunkArray = (array: any[], size: number) => {
    return array.reduce((acc, _, i) => {
      if (i % size === 0) acc.push(array.slice(i, i + size));
      return acc;
    }, [] as any[][]);
  };
  const handleDeleteFile = (imageId: string) => {
    if (!imageId) return;

    Alert.alert(
      "ลบรูปภาพ",
      "แน่ใจว่าต้องการลบรูปภาพ ไม่?",
      [
        { text: "ยกเลิก", style: "cancel" },
        {
          text: "ลบ",
          onPress: async () => {
            await callDeleteImages(imageId); // เรียก API ลบจากเซิร์ฟเวอร์

            setImagesShow((prevImages: any) => {
              if (!Array.isArray(prevImages)) {
                // console.error("imagesShow ไม่ใช่อาร์เรย์");
                return prevImages;
              }

              // ลบรูป ที่ id ตรงกับ imageId
              const updatedImages = prevImages.filter(
                (img) => img.id !== imageId
              );

              // ถ้าไม่มีภาพเหลือ ให้แจ้ง
              if (updatedImages.length === 0) {
                console.log("ไม่มีภาพอยู่");
              }

              return updatedImages;
            });

            console.log(`ลบรูป ID: ${imageId}`);
            setModalFullImg(false); // ปิด Modal ลบรูป
          },
        },
      ],
      { cancelable: true }
    );
  };
  const openCamera = () => {
    const options: any = {
      mediaType: "photo",
      saveToPhotos: true, // ให้เก็บลงคลัง
      quality: 1, // ภาพ
    };

    launchCamera(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris(newImageUris);
        setFileNames(newFileNames);
        setTypeNames(newTypeNames);

        // 📌 สำเร็จ
        await onUploadImages(newImageUris, newFileNames, newTypeNames);
        closeActionSheet();
      }
    });
  };
  const openGallery = () => {
    const options: any = {
      mediaType: "photo",
      quality: 1,
      selectionLimit: 10,
    };

    launchImageLibrary(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris(newImageUris);
        setFileNames(newFileNames);
        setTypeNames(newTypeNames);

        // 📌 สำเร็จ
        await onUploadImages(newImageUris, newFileNames, newTypeNames);
        closeActionSheet();
      }
    });
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const showPickerOptions = () => {
    Alert.alert(t("select"), t("select_import"), [
      { text: t("cancel"), style: "cancel" },
      { text: t("open_camera"), onPress: openCamera },
      { text: t("photo_gallery"), onPress: openGallery },
    ]);
  };
  const onUploadImages = async (
    imageUris: string[],
    fileNames: string[],
    typeNames: string[]
  ) => {
    try {
      setLoadIng(true);
      const imageContent = await Promise.all(
        imageUris.map(async (uri, index) => {
          const base64Data = await convertImageToBase64(uri);
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );

      const req = {
        farmUserPlotId: farmUserPlotId,
        files: imageContent,
      };

      const res = await postUploadImages(req);
      // console.log("Upload Response:", res);
      // setRefresh((prev) => !prev);
      callGallery();
      setTimeout(() => setRefresh((prev) => !prev), 500);
    } catch (error) {
      console.log("Upload Error:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const openActionSheet = () => {
    actionSheetRef.current?.show();
  };
  const closeActionSheet = () => {
    actionSheetRef.current?.hide();
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      placement="left"
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      rightComponent={rightHeader()}
      centerComponent={centerComment()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => navigation.goBack()}>
      {goBack_gay()}
    </TouchableOpacity>
  );
  const rightHeader = () => {
    return (
      <>
        {isPages != "MaketFram" && (
          <View style={{ flexDirection: "row", marginTop: moderateScale(10) }}>
            {type === "gallery" ? (
              <TouchableOpacity
                onPress={() => openActionSheet()}
                disabled={type === ""}
              >
                <Image
                  style={img.img_barOperation}
                  source={Images.aperture}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ) : null}
            <View style={{ margin: moderateScale(10) }} />

            <TouchableOpacity
              onPress={() => setType("gallery")}
              disabled={type === "gallery"}
            >
              <Image
                style={
                  type === "gallery"
                    ? img.img_barOperation
                    : img.img_barOperationNon
                }
                source={Images.picture}
                resizeMode="cover"
              />
            </TouchableOpacity>
            <View style={{ margin: moderateScale(10) }} />

            <TouchableOpacity
              onPress={() => setType("cctv")}
              disabled={type === "cctv"}
            >
              <Image
                style={
                  type === "cctv"
                    ? img.img_barOperation
                    : img.img_barOperationNon
                }
                source={Images.cctv}
                resizeMode="cover"
              />
            </TouchableOpacity>
            <View style={{ margin: moderateScale(10) }} />

            <TouchableOpacity
              onPress={() => setType("noti")}
              disabled={type === "noti"}
            >
              <Image
                style={
                  type === "noti"
                    ? img.img_barOperation
                    : img.img_barOperationNon
                }
                source={Images.bell}
                resizeMode="cover"
              />
              {notificationCountFram > 0 && (
                <Badge
                  value={notificationCountFram}
                  status="error"
                  containerStyle={{ position: "absolute", top: -5, right: -5 }}
                />
              )}
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {plotName}
    </Text>
  );
  const readAll = () => {
    return (
      <>
        <View
          style={{
            padding: 20,
            alignItems: "flex-end",
            backgroundColor: BgColor.Bg_FFFFFF,
          }}
        >
          <TouchableOpacity onPress={() => pushNotiReadAll()}>
            <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
              {t("readall")}
            </Text>
          </TouchableOpacity>
        </View>
        <View style={oth.line_notiNon} />
      </>
    );
  };
  const renderHeaderToday = () => {
    return (
      <>
        <View style={ctn.ctn_txtheader}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("today")}
            </Text>
            <View style={{ margin: moderateScale(5) }} />
          </View>
          {/* <TouchableOpacity onPress={() => setOpenToday(!isOpenToday)}>
            {isOpenToday === true ? iconUpNoti() : iconDownNoti()}
          </TouchableOpacity> */}
        </View>
        {/* {isOpenToday === false ? <View style={oth.line_notiNon} /> : null} */}
      </>
    );
  };
  const renderHeaderDefore = () => {
    return (
      <>
        <View style={ctn.ctn_txtheader}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("before")}
            </Text>
            <View style={{ margin: moderateScale(5) }} />
          </View>
          {/* <TouchableOpacity onPress={() => setOpenBefore(!isOpenBefore)}>
            {isOpenBefore === true ? iconUpNoti() : iconDownNoti()}
          </TouchableOpacity> */}
        </View>
        {/* {isOpenBefore === false ? <View style={oth.line_notiNon} /> : null} */}
      </>
    );
  };
  const renderRightActions = (item: any, index: number) => (
    <TouchableOpacity
      onPress={() => openDelete(item, index)}
      style={ctn.ctn_rightAction}
    >
      {iconDeleteNoti()}
    </TouchableOpacity>
  );
  const renderToday = ({ item, index }: any) => {
    return (
      <Swipeable renderRightActions={() => renderRightActions(item, index)}>
        <TouchableOpacity
          style={[ctn.ctn_contentNoti]}
          onPress={() => callReadNoti(item, index)}
        >
          <View style={ctn.ctn_imgNoti}>
            <FastImage
              style={img.img_imgNoti}
              source={Images.LogoMeFarmHug}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
          </View>
          <View style={{ margin: moderateScale(8) }} />
          <View style={ctn.ctn_txtContent}>
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationTitle}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationBody}
            </Text>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };
  const renderDefore = ({ item, index }: any) => {
    return (
      <Swipeable renderRightActions={() => renderRightActions(item, index)}>
        <TouchableOpacity
          style={[ctn.ctn_contentNoti]}
          onPress={() => callReadNoti(item, index)}
        >
          <View style={ctn.ctn_imgNoti}>
            <FastImage
              style={img.img_imgNoti}
              source={Images.LogoMeFarmHug}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
          </View>
          <View style={{ margin: moderateScale(8) }} />
          <View style={ctn.ctn_txtContent}>
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationTitle}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationBody}
            </Text>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };
  const renderGallery = () => {
    const displayedImages = imagesShow.slice(0, 5); // เฉพาะ 5 แรก

    return (
      <View>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {displayedImages.map((image: any, index: number) => {
            if (!image || !image.thumbnailUrl) {
              console.warn("Missing thumbnailUrl for image:", image);
              return null;
            }

            return (
              <Image
                key={image.id || index} // ใช้ id ถ้า
                source={{ uri: image.thumbnailUrl }}
                style={{
                  width,
                  height:
                    orientation === "portrait"
                      ? moderateScale(350)
                      : moderateScale(150),
                }}
                resizeMode="cover"
                onError={(e) =>
                  console.error("Image Load Error:", e.nativeEvent.error)
                }
              />
            );
          })}
        </ScrollView>

        {/* Indicator */}
        <View
          style={{
            flexDirection: "row",
            justifyContent: "center",
            bottom: 20,
          }}
        >
          {displayedImages.map((image: any, index: number) => (
            <View
              key={image.id || index}
              style={{
                width: 10,
                height: 10,
                borderRadius: 5,
                marginHorizontal: 5,
                backgroundColor: activeIndex === index ? "#007AFF" : "#fff",
              }}
            />
          ))}
        </View>
      </View>
    );
  };
  const renderCollnum = ({ item }: any) => {
    return (
      <TouchableOpacity
        onPress={() => openFullImag(item.thumbnailUrl, item.id)}
        style={{ width: "100%", aspectRatio: 1 }} // ให้ container เป็นสี่เหลี่ยมจัตุรัส
      >
        <FastImage
          style={{
            width: "100%",
            height: "100%",
            aspectRatio: 1, // ให้ภาพเป็นสี่เหลี่ยมจัตุรัสเต็มพื้นที่
            borderRadius: 8,
          }}
          source={{ uri: item.thumbnailUrl }}
          resizeMode={FastImage.resizeMode.cover}
          onLoadStart={() => setLoadIng(true)}
          onLoadEnd={() => setLoadIng(false)}
        />
      </TouchableOpacity>
    );
  };
  const renderCCTV = () => {
    return (
      <View style={{ flex: 1 }}>
        <CCTVPlayer
          // streamUrl="https://mefarm-media.canadev.net/live/375445d2-4368-408e-b839-e61c12a90c1d/hls.m3u8"
          streamUrl={cameraUrl}
          onError={(error) => {
            setVideoError(t("unable_to_play"));
          }}
          onLoaded={() => {
            setIsVideoLoading(false);
            setVideoError(null);
          }}
          paused={false}
          type={"gallery"}
          isPages={isPages}
          onChangeType={(newType) => setType(newType)}
          onCaptureSuccess={(timestamp) => {
            callGallery();
            setRefresh((prev) => !prev);
          }}
        />
      </View>
    );
  };
  const modalDelete = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalDelete}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconDeleteNoti()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("delete_noti")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => setModalDelete(false)}
              >
                <Text style={[fonstStyle.f12_light, txt.txt_orange]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonDelete}
                onPress={() => callDeleteNoto()}
              >
                <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                  {t("agree")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const modalFullImg = () => {
    return (
      <View style={{ flex: 1 }}>
        <ImageViewing
          images={[{ uri: imageFull }]}
          imageIndex={0}
          visible={isModalFullImg}
          onRequestClose={() => setModalFullImg(!isModalFullImg)}
          FooterComponent={({ imageIndex }) => (
            <TouchableOpacity
              style={{
                position: "absolute",
                bottom: 50,
                right: 20,
                backgroundColor: "rgba(0,0,0,0.5)",
                padding: 10,
                borderRadius: 5,
              }}
              onPress={() => handleDeleteFile(idDelete)}
              // onPress={() => openDeleteImg(idDelete)}
            >
              {iconDeleteGallery()}
            </TouchableOpacity>
          )}
        />
      </View>
    );
  };
  const alertReject = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDeleteImg}
        style={{ zIndex: 9999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("Confirm_delete_photo")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalDeleteImg(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                // onPress={() => handleDeleteFile()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const renderActionSheet = () => {
    return (
      <ActionSheet
        ref={actionSheetRef}
        gestureEnabled
        containerStyle={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      >
        <TouchableOpacity style={{ padding: 20 }} onPress={openCamera}>
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconCameraEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("open_camera")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity style={{ padding: 20 }} onPress={openGallery}>
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconGalleryEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("photo_gallery")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity style={{ padding: 20 }} onPress={closeActionSheet}>
          <Text
            style={[fonstStyle.f14_light, txt.txt_red, { textAlign: "center" }]}
          >
            {t("cancel")}
          </Text>
        </TouchableOpacity>
      </ActionSheet>
    );
  };
  const contentGallery = () => (
    <View style={ctn.continueMain}>
      {renderGallery()}
      <ScrollView
        nestedScrollEnabled={true}
        style={[ctn.continueMain, { marginTop: -10 }]}
        showsVerticalScrollIndicator={false}
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isScrolledToEnd =
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - 5;

          if (isScrolledToEnd && !isLoadPosts) {
            loadMoreData();
          }
        }}
        scrollEventThrottle={16}
      >
        {imagesShow.length === 0 ? (
          <View style={ctn.ctn_uploadGallery}>
            <TouchableOpacity
              onPress={() => showPickerOptions()}
              style={btn.btn_uploadImgBank}
            >
              {iconUploadMedia()}
              <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                {t("upload_gellary")}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={imagesShow}
            removeClippedSubviews={false}
            keyExtractor={(item, index) =>
              item.id?.toString() || index.toString()
            }
            numColumns={3}
            renderItem={({ item }) => (
              <View style={{ width: "33.3%", aspectRatio: 1, padding: 2 }}>
                {renderCollnum({ item })}
              </View>
            )}
            ListEmptyComponent={
              <View style={ctn.ctn_uploadGallery}>
                <TouchableOpacity
                  onPress={() => showPickerOptions()}
                  style={btn.btn_uploadImgBank}
                >
                  {iconUploadMedia()}
                  <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                    {t("upload_gellary")}
                  </Text>
                </TouchableOpacity>
              </View>
            }
            contentContainerStyle={{ paddingBottom: moderateScale(20) }}
          />
        )}

        <View style={{ margin: moderateScale(20) }} />
        {isLoadPosts ? LoadingApp() : null}
        <View style={{ margin: moderateScale(80) }} />

        {modalFullImg()}
        {alertReject()}
      </ScrollView>
    </View>
  );
  const contenGalleryLan = () => (
    <SafeAreaView style={ctn.continueMain}>
      {renderGallery()}
      <ScrollView
        nestedScrollEnabled={true}
        style={[ctn.continueMain, { marginTop: -10 }]}
        showsVerticalScrollIndicator={false}
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isScrolledToEnd =
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - 5;

          if (isScrolledToEnd && !isLoadPosts) {
            loadMoreData();
          }
        }}
        scrollEventThrottle={16}
      >
        {imagesShow.length === 0 ? (
          <View style={ctn.ctn_uploadGallery}>
            <TouchableOpacity
              onPress={() => showPickerOptions()}
              style={btn.btn_uploadImgBank}
            >
              {iconUploadMedia()}
              <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                {t("upload_gellary")}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={imagesShow}
            removeClippedSubviews={false}
            keyExtractor={(item, index) =>
              item.id?.toString() || index.toString()
            }
            numColumns={3}
            renderItem={({ item }) => (
              <View style={{ width: "33.3%", aspectRatio: 1, padding: 2 }}>
                {renderCollnum({ item })}
              </View>
            )}
            ListEmptyComponent={
              <View style={ctn.ctn_uploadGallery}>
                <TouchableOpacity
                  onPress={() => showPickerOptions()}
                  style={btn.btn_uploadImgBank}
                >
                  {iconUploadMedia()}
                  <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                    {t("upload_gellary")}
                  </Text>
                </TouchableOpacity>
              </View>
            }
            contentContainerStyle={{ paddingBottom: moderateScale(20) }}
          />
        )}

        <View style={{ margin: moderateScale(20) }} />
        {isLoadPosts ? LoadingApp() : null}
        <View style={{ margin: moderateScale(80) }} />

        {modalFullImg()}
        {alertReject()}
      </ScrollView>
    </SafeAreaView>
  );
  const contentNoti = () => (
    <ScrollView
      nestedScrollEnabled={true}
      style={[ctn.continueMain]}
      showsVerticalScrollIndicator={false}
      onScroll={(event) => {
        // Destructure nativeEvent immediately to avoid synthetic event pooling issues
        const nativeEvent = event.nativeEvent;
        const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
        const isScrolledToEnd =
          layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

        if (isScrolledToEnd && !isLoadPosts) {
          loadMoreData();
        }
      }}
    >
      <GestureHandlerRootView>
        {readAll()}
        {renderHeaderToday()}
        {docToday?.length > 0 ? (
          docToday.map((item: any, index: number) =>
            renderToday({ item, index })
          )
        ) : (
          <View style={{ alignItems: "center", padding: 10 }}>
            <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
              {t("no_notiList")}
            </Text>
          </View>
        )}
        {isLoadPosts ? (
          <>
            {LoadingApp()} <View style={{ margin: moderateScale(20) }} />
          </>
        ) : null}

        {renderHeaderDefore()}

        {docDefore?.length > 0 ? (
          docDefore.map((item: any, index: number) =>
            renderDefore({ item, index })
          )
        ) : (
          <View style={{ alignItems: "center", padding: 10 }}>
            <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
              {t("no_notiList")}
            </Text>
          </View>
        )}
        {isLoadPosts ? LoadingApp() : null}
        {modalDelete()}
      </GestureHandlerRootView>
      <View style={{ margin: moderateScale(120) }} />
    </ScrollView>
  );
  const contentNotiLan = () => (
    <ScrollView
      nestedScrollEnabled={true}
      style={[ctn.continueMain]}
      showsVerticalScrollIndicator={false}
      onScroll={(event) => {
        // Destructure nativeEvent immediately to avoid synthetic event pooling issues
        const nativeEvent = event.nativeEvent;
        const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
        const isScrolledToEnd =
          layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

        if (isScrolledToEnd && !isLoadPosts) {
          loadMoreData();
        }
      }}
    >
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <GestureHandlerRootView>
          {readAll()}
          {renderHeaderToday()}
          {docToday?.length > 0 ? (
            docToday.map((item: any, index: number) =>
              renderToday({ item, index })
            )
          ) : (
            <View style={{ alignItems: "center", padding: 10 }}>
              <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                {t("no_notiList")}
              </Text>
            </View>
          )}
          {isLoadPosts ? (
            <>
              {LoadingApp()} <View style={{ margin: moderateScale(20) }} />
            </>
          ) : null}

          {renderHeaderDefore()}

          {docDefore?.length > 0 ? (
            docDefore.map((item: any, index: number) =>
              renderDefore({ item, index })
            )
          ) : (
            <View style={{ alignItems: "center", padding: 10 }}>
              <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                {t("no_notiList")}
              </Text>
            </View>
          )}
          {isLoadPosts ? LoadingApp() : null}
          {modalDelete()}
        </GestureHandlerRootView>
      </SafeAreaView>
      <View style={{ margin: moderateScale(120) }} />
    </ScrollView>
  );

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {isLoadIng ? <Loading /> : null}
      {headerBar()}

      {type === "gallery"
        ? orientation === "portrait"
          ? contentGallery()
          : contenGalleryLan()
        : null}

      {type === "cctv" ? (
        <View style={ctn.continue}>{renderCCTV()}</View>
      ) : null}
      {type === "noti"
        ? orientation === "portrait"
          ? contentNoti()
          : contentNotiLan()
        : null}
      {renderActionSheet()}
    </>
  );
}
