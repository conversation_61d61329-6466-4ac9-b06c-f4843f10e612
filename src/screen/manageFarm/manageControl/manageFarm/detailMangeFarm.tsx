import {
  Text,
  View,
  Image,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import React, { useState, useEffect } from "react";
import { moderateScale } from "react-native-size-matters";
//StyleSheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import img from "../../../../styleSheet/img";
import txt from "../../../../styleSheet/txt";
//Svg
import {
  iconQrPay,
  iconNoImg,
  iconBanking,
  iconQrPayChang,
  iconBankingChang,
} from "../../../../assets/svg/svg_other";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import ManageBar from "../../../../components/appBar/manage_Bar";
import PayMent from "../../../../components/payMent/payMent";
//Translation
import { useTranslation } from "../../../i18n";
//Api
import { getPayMentApi } from "../../../../action/Mefarm_Farm_API";

export default function DetailMangeFarm({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  //Number
  const [activeButton, setActiveButton] = useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  //OBJ
  const [paymentData, setPaymentData] = useState<any>({});
  //Array
  const [paymentMethods, setPaymentMethods] = useState<any>([]);

  //Params
  const isPage = route.name || "";
  const params = route.params || "";
  const items = params.items || "";
  const sumPrice = params.sumPrice || "";
  const farmUserPlotId = params.farmUserPlotId || "";
  const docDetailMyfarm = params.docDetailMyfarm || "";
  // console.log(params);

  //Function
  useEffect(() => {
    const callPayMent = async () => {
      try {
        setLoadIng(true);
        const response = await getPayMentApi();
        const payMentData = response.model || "";
        // console.log(JSON.stringify(payMentData, null, 2));
        setPaymentMethods(payMentData);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    };
    callPayMent();
  }, []);
  const selectPayMent = (item: any, index: number) => {
    // console.log(index);
    setSelectedIndex(index);
    setActiveButton(index);
    setPaymentData(item);
  };

  //Go to
  const goNext = () => {
    navigation.navigate("QrCodepay", {
      pageDetailMangeFarm: isPage,
      activeButton: activeButton,
      items: items,
      sumPrice: sumPrice,
      farmUserPlotId: farmUserPlotId,
      docDetailMyfarm: docDetailMyfarm,
      paymentData: paymentData,
    });
  };

  //UI
  const titlePay = () => (
    <View
      style={{
        marginTop: moderateScale(10),
        paddingHorizontal: moderateScale(20),
      }}
    >
      <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
        {t("seed_list")}
      </Text>
    </View>
  );
  const renderContent = ({ item }: any) => {
    return (
      <View style={ctn.ctn_listManageFarm}>
        <View style={{ flexDirection: "row" }}>
          <View style={ctn.ctn_imgManageFarm}>
            {item.imageUrl === null ? (
              iconNoImg()
            ) : (
              <Image
                style={img.img_listManageFram}
                source={{ uri: item.imageUrl }}
                resizeMode="cover"
              />
            )}
          </View>
          <View style={{ margin: moderateScale(5) }} />

          <View style={{ flexDirection: "column", flex: 1 }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.serviceName}
            </Text>
            <View style={{ margin: moderateScale(5) }} />

            {item.price === 0 ? (
              <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                {t("free")}
              </Text>
            ) : (
              <Text style={[fonstStyle.f12_bold, txt.txt_gray]}>
                ฿ {item.lastPrice}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };
  const selectPay = () => {
    return (
      // <View
      //   style={{
      //     paddingHorizontal: moderateScale(20),
      //     paddingVertical: moderateScale(5),
      //   }}
      // >
      //   <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
      //     {t("Payment_channels")}
      //   </Text>
      //   <View style={{ margin: moderateScale(5) }} />
      //   <View style={{ flexDirection: "row" }}>
      //     {paymentMethods.map((item: any, index: number) => (
      //       <>
      //         <View>
      //           <TouchableOpacity
      //             disabled={selectedIndex === index}
      //             onPress={() => selectPayMent(item, index)}
      //             style={[
      //               selectedIndex === index
      //                 ? btn.btn_payMentActive
      //                 : btn.btn_payMentNon,
      //             ]}
      //           >
      //             {item.iconImageUrl === null ? (
      //               <>
      //                 {selectedIndex === index
      //                   ? iconBankingChang()
      //                   : iconBanking()}
      //               </>
      //             ) : (
      //               <Image
      //                 source={{ uri: item.iconImageUrl }}
      //                 style={{ width: 40, height: 40, borderRadius: 5 }}
      //               />
      //             )}
      //           </TouchableOpacity>
      //         </View>
      //         <View style={{ margin: moderateScale(10) }} />
      //       </>
      //     ))}
      //   </View>
      //   <View style={{ margin: moderateScale(5) }} />
      // </View>
      <PayMent
        paymentMethods={paymentMethods}
        selectedIndex={selectedIndex}
        selectPayMent={selectPayMent}
        t={t}
        btn={btn}
        fonstStyle={fonstStyle}
        txt={txt}
        moderateScale={moderateScale}
        iconBanking={iconBanking}
        iconBankingChang={iconBankingChang}
      />
    );
  };
  const bottomPay = () => {
    return (
      <View style={ctn.ctn_detailMangeFarm}>
        <View style={{ padding: moderateScale(20), flex: 1 }}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("payment_information")}
          </Text>
          <View style={{ margin: moderateScale(5) }} />

          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_light, txt.txt_gray]}>
              {t("total_orders")}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {sumPrice == 0 ? t("free") : sumPrice}
            </Text>
          </View>
          <View style={{ margin: moderateScale(5) }} />

          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("total_payment")}
            </Text>
            <Text style={[fonstStyle.f14_medium, txt.txt_green]}>
              {sumPrice == 0 ? t("free") : sumPrice}
            </Text>
          </View>
        </View>

        {/* {Bottom Pay} */}
        <View style={{ alignItems: "center", bottom: moderateScale(20) }}>
          <TouchableOpacity
            disabled={selectedIndex === null}
            style={
              selectedIndex != null ? btn.btn_detailPay : btn.btn_detailPayNon
            }
            onPress={() => (selectedIndex != null ? goNext() : undefined)}
          >
            <View style={ctn.ctn_btnDetailPay}>
              <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                {t("confirm_payment")}
              </Text>

              <Text style={[fonstStyle.f14_medium, txt.txt_white]}>
                {sumPrice == 0 ? t("free") : sumPrice}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <View style={ctn.continueMain}>
        <ScrollView
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
        >
          {titlePay()}
          {items.map((item: any, index: number) => (
            <View style={{ paddingHorizontal: moderateScale(10) }}>
              {renderContent({ item, index })}
            </View>
          ))}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
        {selectPay()}
        <View style={{ margin: moderateScale(5) }} />
        {bottomPay()}
      </View>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
          >
            {titlePay()}
            {items.map((item: any, index: number) => (
              <View style={{ paddingHorizontal: moderateScale(10) }}>
                {renderContent({ item, index })}
              </View>
            ))}
            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          {selectPay()}
          {bottomPay()}
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <ManageBar
        onBack={() => navigation.goBack()}
        pageName={t("payment_details")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
