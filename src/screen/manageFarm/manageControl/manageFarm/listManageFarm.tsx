import {
  Text,
  View,
  Image,
  TextInput,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import React, { useState } from "react";
import { moderateScale } from "react-native-size-matters";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GestureHandlerRootView } from "react-native-gesture-handler";
//Style Sheet
import btn from "../../../../styleSheet/btn";
import ctn from "../../../../styleSheet/ctn";
import img from "../../../../styleSheet/img";
import txt from "../../../../styleSheet/txt";
import oth from "../../../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../../../styleSheet/style_Custom";
//Svg
import {
  iconNoImg,
  iconPlusNumber,
  iconReductNumber,
} from "../../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../../hooks/useOrientation";
import ManageBar from "../../../../components/appBar/manage_Bar";
//Translation
import { useTranslation } from "../../../i18n";

export default function ListManageFarm({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  //String
  const [serviceName, setServiceName] = useState<string>("");
  const [comentManage, setComentManage] = useState<string[]>([]);
  //True & False
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  //Params
  const params = route.params || "";
  const selectedManage = params.selectedManage || "";
  const farmUserPlotId = params.farmUserPlotId || "";
  const docDetailMyfarm = params.docDetailMyfarm || "";

  const [dataChange, setDataChange] = useState(selectedManage);
  const [quantities, setQuantities] = useState<number[]>(() =>
    dataChange.map(() => 1)
  );
  const [totalPrices, setTotalPrices] = useState<number[]>(() =>
    dataChange.map((item: any) => item.price)
  );

  //Function
  const handleIncrement = (index: number) => {
    const newQuantities = [...quantities];
    const newTotalPrices = [...totalPrices];

    newQuantities[index] += 1;
    newTotalPrices[index] += dataChange[index].price;

    setQuantities(newQuantities);
    setTotalPrices(newTotalPrices);
    setServiceName(dataChange[index].serviceName);
  };
  const handleDecrement = (index: number) => {
    if (quantities[index] > 1) {
      const newQuantities = [...quantities];
      const newTotalPrices = [...totalPrices];

      newQuantities[index] -= 1;
      newTotalPrices[index] -= dataChange[index].price;

      setQuantities(newQuantities);
      setTotalPrices(newTotalPrices);
      setServiceName(dataChange[index].serviceName);
    }
  };

  //Go to
  const goNext = async () => {
    const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
    const sumPrice = totalPrices.reduce((sum, price) => sum + price, 0);
    // สร้าง array ประกอบไปด้วยข้อมูลต้องการส่ง
    const updatedData = dataChange.map((item: any, index: number) => ({
      id: userIdLogin,
      serviceId: item.id,
      serviceName: item.serviceName,
      quantity: quantities[index],
      unitQuantity: "",
      isFree: item.isFree,
      imageUrl: item.imageUrl,
      price: item.price,
      lastPrice: item.isFree ? 0 : totalPrices[index],
      unitPrice: "",
      comment: comentManage[index] 
    }));
    // ส่งข้อมูลหน้า DetailMangeFarm
    // console.log(updatedData);
    
    navigation.navigate("DetailMangeFarm", {
      items: updatedData,
      sumPrice: sumPrice,
      farmUserPlotId: farmUserPlotId,
      docDetailMyfarm: docDetailMyfarm,
    });
  };

  //UI
  const renderContent = ({ item, index }: any) => {
    return (
      <View style={ctn.ctn_listManageFarm}>
        <View style={{ flexDirection: "row" }}>
          <View style={ctn.ctn_imgManageFarm}>
            {item.imageUrl === null ? (
              iconNoImg()
            ) : (
              <Image
                style={img.img_listManageFram}
                source={{ uri: item.imageUrl }}
                resizeMode="cover"
              />
            )}
          </View>
          <View style={{ margin: moderateScale(5) }} />

          <View style={{ flexDirection: "column", flex: 1 }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.serviceName}
            </Text>
            <View style={{ margin: moderateScale(5) }} />

            <View style={ctn.ctn_spaceBet}>
              <View style={oth.plusNumber}>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <TouchableOpacity onPress={() => handleDecrement(index)}>
                    {iconReductNumber()}
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(10) }} />

                  <Text style={[fonstStyle.f14_light, txt.txt_green]}>
                    {quantities[index]}
                  </Text>
                  <View style={{ margin: moderateScale(10) }} />
                  <TouchableOpacity onPress={() => handleIncrement(index)}>
                    {iconPlusNumber()}
                  </TouchableOpacity>
                </View>
              </View>

              {item.price === 0 ? (
                <Text style={[fonstStyle.f14_bold, txt.txt_orange]}>
                  {t("free")}
                </Text>
              ) : (
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  ฿ {totalPrices[index]}
                </Text>
              )}
            </View>
            <View style={{ margin: moderateScale(5) }} />
            <View
              style={{
                backgroundColor: BgColor.Bg_F4F4F4,
                padding: 10,
                borderRadius: 10,
              }}
            >
              <TextInput
                style={[fonstStyle.f12_light, txt.txt_606060]}
                placeholder={t("comment_post")}
                onChangeText={(text) => {
                  const newComments = [...comentManage];
                  newComments[index] = text;
                  setComentManage(newComments);
                }}
                value={comentManage[index] || ""}
                editable
                multiline
              />
            </View>
          </View>
        </View>
      </View>
    );
  };
  const botton = () => {
    return (
      <>
        {!isBottomSheetOpen && selectedManage != "" && (
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              style={btn.btn_bottonListManageFarm}
              onPress={() => goNext()}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                {t("check")}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <View style={ctn.continueMain}>
        <GestureHandlerRootView>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
          >
            {dataChange.length > 0 ? (
              dataChange.map((item: any, index: number) => (
                <View
                  key={`item-${item.id || index}`}
                  style={{ paddingHorizontal: moderateScale(10) }}
                >
                  {renderContent({ item, index })}
                </View>
              ))
            ) : (
              <View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {t("no_management")}
                </Text>
              </View>
            )}
            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          {botton()}
        </GestureHandlerRootView>
      </View>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          <GestureHandlerRootView>
            <ScrollView
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ flexGrow: 1 }}
            >
              {dataChange.length > 0 ? (
                dataChange.map((item: any, index: number) => (
                  <View
                    key={`item-${item.id || index}`}
                    style={{ paddingHorizontal: moderateScale(10) }}
                  >
                    {renderContent({ item, index })}
                  </View>
                ))
              ) : (
                <View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                    {t("no_management")}
                  </Text>
                </View>
              )}
              <View style={{ margin: moderateScale(120) }} />
            </ScrollView>
            {botton()}
          </GestureHandlerRootView>
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      <ManageBar
        onBack={() => navigation.goBack()}
        pageName={t("list_managefram")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
