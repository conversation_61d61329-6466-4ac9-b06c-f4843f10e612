import {
  moderateScale,
  moderateVerticalScale,
} from "react-native-size-matters";
import { useFocusEffect } from "@react-navigation/native";
import React, { useEffect, useState, useCallback } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  Text,
  View,
  Switch,
  TextInput,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
//Style
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import fonstStyle, {
  BgColor,
  FonstColor,
} from "../../../styleSheet/style_Custom";
//Svg
import { iconRight } from "../../../assets/svg/svg_other";
//Components
import Loading from "../../../components/loading/loading";
import ManageBar from "../../../components/appBar/manage_Bar";
import { useOrientation } from "../../../hooks/useOrientation";
//Api
import {
  putAddressBook,
  postAddressBook,
} from "../../../action/Mefarm_Identity_API";
//Translation
import { useTranslation } from "../../i18n";

export default function PlusAddress({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const [inputName, setInputName] = useState<string>("");
  const [inputPhone, setInputPhone] = useState<string>("");
  const [inputAddress, setInputAddress] = useState<string>("");
  //True & false
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  //ข้อมูล editAddress จาก AddressList.tsx
  const params = route.params || "";
  const isPage = params.isPage || "";
  const typeEdit = params.typeEdit || "";
  const editAddress = params.editAddress || "";

  const [editPhone, setEditPhone] = useState<string>(editAddress.mobileNo);
  const [editName, setEditName] = useState<string>(editAddress.recipientName);
  const [editAddressNum, setEditAddressNum] = useState<string>(
    editAddress.addressLine1
  );
  const [address, setAddress] = useState<any>({
    postalCode: "",
    returnName: "",
    districtName: "",
    provinceName: "",
    subDistrictName: "",
    masterDistrictId: "",
    masterProvinceId: "",
    masterSubDistrictId: "",
  });
  const isAddressValid = () => {
    return Object.values(address).some((value) => value !== "");
  };

  const isButtonEnabled =
    inputName.trim() !== "" &&
    inputPhone.trim() !== "" &&
    inputAddress.trim() !== "";
  // &&
  // address.trim !== "";

  //Function
  useEffect(() => {
    const loadSwitchState = async () => {
      try {
        // ดึงค่า isEnabled และ editAddress ที่เก็บไว้ใน AsyncStorage
        const savedState = await AsyncStorage.getItem("isEnabled");
        const storedAddress = await AsyncStorage.getItem("editAddress");

        if (storedAddress) {
          const parsedAddress = JSON.parse(storedAddress);

          // ตรวจสอบว่า id ตรงกันหรือไม่
          if (editAddress.id === parsedAddress.id) {
            // ถ้า id ตรงกัน ตั้งค่า isEnabled ตาม savedState
            if (savedState !== null) {
              setIsEnabled(JSON.parse(savedState));
            }
          } else {
            // ถ้า id ไม่ตรงกัน ตั้งค่า isEnabled เป็น false
            setIsEnabled(false);
          }
        } else {
          // ถ้าไม่มีข้อมูลเก็บไว้ใน AsyncStorage ให้ตั้งเป็น false
          setIsEnabled(false);
        }
      } catch (error) {
        console.error("Error loading switch state:", error);
      }
    };

    loadSwitchState();
  }, [editAddress.id]);
  useFocusEffect(
    useCallback(() => {
      fetchAddressData();
    }, [])
  );
  const fetchAddressData = async () => {
    try {
      const storedData = await AsyncStorage.getItem("addressData");
      // console.log(storedData);

      if (storedData) {
        setAddress(JSON.parse(storedData)); // อัปเดต state ด้วยข้อมูลที่ดึงมา
      } else {
        setAddress({
          postalCode: "",
          returnName: "",
          districtName: "",
          provinceName: "",
          subDistrictName: "",
          masterDistrictId: "",
          masterProvinceId: "",
          masterSubDistrictId: "",
        });
      }
    } catch (error) {
      console.error("Error reading data from AsyncStorage:", error);
    }
  };

  const callPostAddressBook = async () => {
    if (isPage === "AddressList") {
      try {
        setLoadIng(true);
        const req = {
          id: editAddress.id || "",
          recipientName: editName || "",
          addressLine1: editAddressNum || "",
          addressLine2: "",
          masterProvinceId:
            address.masterProvinceId || editAddress.masterProvinceId || "",
          provinceName: address.provinceName || editAddress.provinceName || "",
          masterDistrictId:
            address.masterDistrictId || editAddress.masterDistrictId || "",
          districtName: address.districtName || editAddress.districtName || "",
          masterSubDistrictId:
            address.masterSubDistrictId ||
            editAddress.masterSubDistrictId ||
            "",
          subDistrictName:
            address.subDistrictName || editAddress.subDistrictName || "",
          postalCode: address.postalCode || editAddress.postalCode || "",
          isDefault: true,
          isActive: true,
          latitude: 0,
          longitude: 0,
          mobileNo: editPhone || "",
        };
        const response = await putAddressBook(req);
        // await AsyncStorage.setItem("editAddress", JSON.stringify(req));
        // AsyncStorage.removeItem("addressData");
        navigation.goBack();
        // console.log(JSON.stringify(req, null, 2));
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    } else {
      try {
        setLoadIng(true);
        const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
        const req = {
          id: userIdLogin || "",
          recipientName: inputName || address.returnName || "",
          addressLine1: inputAddress || "",
          addressLine2: "",
          masterProvinceId: address.masterProvinceId || "",
          provinceName: address.provinceName || "",
          masterDistrictId: address.masterDistrictId || "",
          districtName: address.districtName || "",
          masterSubDistrictId: address.masterSubDistrictId || "",
          subDistrictName: address.subDistrictName || "",
          postalCode: address.postalCode || "",
          isDefault: true,
          isActive: true,
          latitude: 0,
          longitude: 0,
          mobileNo: inputPhone || "",
        };
        const response = await postAddressBook(req);
        const data = response.model || "";
        // console.log(JSON.stringify(req, null, 2));
        navigation.goBack();
        // AsyncStorage.removeItem("addressData");
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    }
  };
  const toggleSwitch = async () => {
    const newValue = !isEnabled;
    setIsEnabled(newValue);

    try {
      await AsyncStorage.setItem("isEnabled", JSON.stringify(newValue));

      if (newValue) {
        // เมื่อเปิด Switch ให้บันทึก editAddress ลงใน AsyncStorage
        await AsyncStorage.setItem("editAddress", JSON.stringify(editAddress));
        // console.log("Address saved:", editAddress);
      } else {
        // เมื่อปิด Switch ให้ลบ editAddress จาก AsyncStorage
        await AsyncStorage.removeItem("editAddress");
        // console.log("Address removed from AsyncStorage");
      }
    } catch (error) {
      console.error("Error updating AsyncStorage:", error);
    }
  };

  //Go to
  const goBuildAddress = () => {
    navigation.navigate("BuildAddress", {
      isPage: isPage,
      editAddress: editAddress,
      inputName: inputName,
      inputPhone: inputPhone,
      inputAddress: inputAddress,
    });
  };
  const goBack = async () => {
    navigation.goBack();
    AsyncStorage.removeItem("addressData");
  };

  //Ui
  const titleHeader = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          marginTop: moderateScale(20),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("contact_channels")}
        </Text>
        <View style={{ margin: moderateVerticalScale(10) }} />
      </View>
    );
  };
  const inputContent = () => {
    return (
      <View style={{ paddingHorizontal: moderateScale(20) }}>
        <View style={oth.shadowBox}>
          <TextInput
            style={[oth.input_address, fonstStyle.f12_light, txt.txt_606060]}
            placeholder={t("name_surname")}
            onChangeText={isPage === "AddressList" ? setEditName : setInputName}
            value={isPage === "AddressList" ? editName : inputName}
            keyboardType="default"
          />
        </View>
        <View style={{ margin: moderateVerticalScale(5) }} />

        <View style={oth.shadowBox}>
          <TextInput
            style={[oth.input_address, fonstStyle.f12_light, txt.txt_606060]}
            placeholder={t("phone_nember")}
            onChangeText={
              isPage === "AddressList" ? setEditPhone : setInputPhone
            }
            value={isPage === "AddressList" ? editPhone : inputPhone}
            keyboardType="numeric"
          />
        </View>
      </View>
    );
  };
  const titleBottom = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          marginTop: moderateScale(20),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("address")}
        </Text>
        <View style={{ margin: moderateVerticalScale(5) }} />
      </View>
    );
  };
  const addressUser = () => {
    return (
      <View style={{ paddingHorizontal: moderateScale(20) }}>
        {address.masterProvinceId === "" ? (
          <TouchableOpacity
            style={[oth.addressUser]}
            onPress={() => goBuildAddress()}
          >
            {isPage === "AddressList" ? (
              <TouchableOpacity onPress={() => goBuildAddress()}>
                <View style={oth.provinceData}>
                  <View style={{ flexDirection: "column" }}>
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {editAddress.provinceName}
                    </Text>
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {editAddress.districtName}
                    </Text>
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {editAddress.subDistrictName}
                    </Text>
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {editAddress.postalCode}
                    </Text>
                  </View>
                  {iconRight()}
                </View>
              </TouchableOpacity>
            ) : (
              <View style={[oth.provinceall]}>
                <Text
                  style={[
                    fonstStyle.f12_light,
                    { color: FonstColor.Tc_A5A5A5 },
                  ]}
                >
                  {t("province_all")}
                </Text>
                {iconRight()}
              </View>
            )}
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[oth.addressUser]}
            onPress={() => goBuildAddress()}
          >
            <View style={oth.provinceData}>
              <View style={{ flexDirection: "column" }}>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {address.provinceName}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {address.districtName}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {address.subDistrictName}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {address.postalCode}
                </Text>
              </View>
              {iconRight()}
            </View>
          </TouchableOpacity>
        )}
        <View style={{ margin: moderateVerticalScale(5) }} />

        <View style={oth.shadowBox}>
          <TextInput
            style={[oth.input_address, fonstStyle.f12_light, txt.txt_606060]}
            placeholder={t("home_address")}
            onChangeText={
              isPage === "AddressList" ? setEditAddressNum : setInputAddress
            }
            value={isPage === "AddressList" ? editAddressNum : inputAddress}
            keyboardType="default"
          />
        </View>
        <View style={{ margin: moderateVerticalScale(5) }} />
      </View>
    );
  };
  const titleSetAddress = () => {
    return (
      <>
        {isPage === "AddressList" ? (
          <View
            style={{
              paddingHorizontal: moderateScale(20),
              paddingVertical: moderateScale(10),
            }}
          >
            <View
              style={{ flexDirection: "row", justifyContent: "space-between" }}
            >
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {t("set_address")}
              </Text>

              <Switch
                trackColor={{
                  false: BgColor.Bg_D6D6D6,
                  true: BgColor.Bg_84B8A2,
                }}
                thumbColor={isEnabled ? BgColor.Bg_FFFFFF : BgColor.Bg_FFFFFF}
                onValueChange={toggleSwitch}
                value={isEnabled}
              />
            </View>
          </View>
        ) : null}
      </>
    );
  };
  const botton = () => {
    return (
      <View style={{ alignItems: "center" }}>
        {(typeEdit || isAddressValid()) && (isButtonEnabled || typeEdit) ? (
          <TouchableOpacity
            style={btn.btn_detailPay}
            onPress={() => callPostAddressBook()}
            disabled={typeEdit ? isButtonEnabled : !isButtonEnabled}
          >
            <Text
              style={[
                fonstStyle.f14_bold,
                txt.txt_white,
                { textAlign: "center" },
              ]}
            >
              {t("confirm")}
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <ScrollView style={ctn.continueMain}>
          {titleHeader()}
          {inputContent()}
          {titleBottom()}
          {addressUser()}
          {/* {titleSetAddress()} */}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>

        {botton()}
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <ScrollView style={ctn.continueMain}>
          {titleHeader()}
          {inputContent()}
          {titleBottom()}
          {addressUser()}
          {/* {titleSetAddress()} */}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>

        {botton()}
      </SafeAreaView>
    );
  };

  return (
    <>
      <ManageBar onBack={goBack} pageName={t("add_address")} />
      {isLoadIng ? <Loading /> : null}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
