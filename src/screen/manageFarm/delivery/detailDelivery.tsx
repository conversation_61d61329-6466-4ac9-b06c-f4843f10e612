import {
  Text,
  View,
  Image,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import React, { useState, useEffect } from "react";
import { moderateScale } from "react-native-size-matters";
//Style
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import img from "../../../styleSheet/img";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import fonstStyle, {
  BgColor,
  FonstColor,
} from "../../../styleSheet/style_Custom";
//Svg
import {
  iconQrPay,
  iconBanking,
  iconQrPayChang,
  iconBankingChang,
} from "../../../assets/svg/svg_other";
//Components
import Loading from "../../../components/loading/loading";
import ManageBar from "../../../components/appBar/manage_Bar";
import { useOrientation } from "../../../hooks/useOrientation";
//Api
import { postHarvest, getPayMentApi } from "../../../action/Mefarm_Farm_API";
//Translation
import { useTranslation } from "../../i18n";

export default function DetailDelivery({ navigation, route }: any) {
  const orientation = useOrientation();

  const pageDetailDelivery = route.name || "";
  const { t } = useTranslation();

  //DocData
  const params = route.params || "";
  const harvestData = params.harvestData || "";
  const Docdetail = harvestData.addressBookDetail || "";
  const addressBook = Docdetail.fullAddress || "";
  const deliveryCost = harvestData.deliveryCost || "";
  const serviceFee = harvestData.serviceFee || "";
  const invoiceAmount = harvestData.invoiceAmount || "";
  const docDetailMyfarm = params.docDetailMyfarm || "";
  //Array
  const [paymentMethods, setPaymentMethods] = useState<any>([]);
  //OBJ
  const [paymentData, setPaymentData] = useState<any>({});
  //Number
  // const [activeButton, setActiveButton] = useState<any>(1);
  const [activeButton, setActiveButton] = useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  //True & false
  const [isLoadIng, setLoadIng] = useState<boolean>(false);

  //Function
  useEffect(() => {
    const callPayMent = async () => {
      try {
        setLoadIng(true);
        const response = await getPayMentApi();
        const payMentData = response.model || "";
        // console.log(JSON.stringify(payMentData, null, 2));
        setPaymentMethods(payMentData);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    };
    callPayMent();
  }, []);
  const selectPayMent = (item: any, index: number) => {
    // console.log(index);
    setSelectedIndex(index);
    setActiveButton(index);
    setPaymentData(item);
  };

  //Go to
  const goNext = async () => {
    navigation.navigate("QrCodepay", {
      invoiceAmount: invoiceAmount,
      activeButton: activeButton,
      pageDetailDelivery: pageDetailDelivery,
      docDetailMyfarm: docDetailMyfarm,
      paymentData: paymentData,
    });
  };

  //Ui
  const detail = () => {
    return (
      <View style={ctn.ctn_detailDeliveryTitle}>
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("payment_information")}
        </Text>
        <View style={{ margin: moderateScale(10) }} />
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
            {t("service_fee")}
          </Text>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            ฿ {deliveryCost}
          </Text>
        </View>
        <View style={{ margin: moderateScale(5) }} />
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
            {t("shipping_costs")}
          </Text>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            ฿ {serviceFee}
          </Text>
        </View>
        <View style={{ margin: moderateScale(10) }} />
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("total_payment")}
          </Text>
          <Text
            style={[
              [fonstStyle.f14_bold, txt.txt_606060],
              { color: FonstColor.Tc_84B8A2 },
            ]}
          >
            ฿ {invoiceAmount}
          </Text>
        </View>
        <View style={{ margin: moderateScale(5) }} />
        <View style={oth.line_profile} />
        <View style={{ margin: moderateScale(10) }} />

        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("shipping_address")}
        </Text>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
          {addressBook}
        </Text>
      </View>
    );
  };
  const payMent = () => {
    return (
      <View style={{ padding: moderateScale(20) }}>
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("Payment_channels")}
        </Text>
        <View style={{ margin: moderateScale(10) }} />
        <View style={{ flexDirection: "row" }}>
          {paymentMethods.map((item: any, index: number) => (
            <>
              <View>
                <TouchableOpacity
                  disabled={selectedIndex === index}
                  onPress={() => selectPayMent(item, index)}
                  style={[
                    selectedIndex === index
                      ? btn.btn_payMentActive
                      : btn.btn_payMentNon,
                  ]}
                >
                  {item.iconImageUrl === null ? (
                    <>
                      {selectedIndex === index
                        ? iconBankingChang()
                        : iconBanking()}
                    </>
                  ) : (
                    <Image
                      source={{ uri: item.iconImageUrl }}
                      style={{ width: 40, height: 40, borderRadius: 5 }}
                    />
                  )}
                </TouchableOpacity>
              </View>
              <View style={{ margin: moderateScale(10) }} />
            </>
          ))}
        </View>
      </View>
    );
  };
  const botton = () => {
    return (
      <View style={{ alignItems: "center" }}>
        <TouchableOpacity
          disabled={selectedIndex === null}
          style={
            selectedIndex != null
              ? oth.cardBottonDelivery
              : oth.cardBottonDeliveryNon
          }
          onPress={() => (selectedIndex != null ? goNext() : undefined)}
        >
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
              {t("confirm_payment")}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
              ฿ {invoiceAmount}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <ScrollView style={ctn.continue}>
          {detail()}
          {payMent()}
          <View style={{margin: moderateScale(120)}}/>
        </ScrollView>
        {botton()}
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <ScrollView style={ctn.continue}>
          {detail()}
          {payMent()}
          <View style={{margin: moderateScale(120)}}/>
        </ScrollView>
        {botton()}
      </SafeAreaView>
    );
  };

  return (
    <>
      <ManageBar
        onBack={() => navigation.goBack()}
        pageName={t("shipping_details")}
      />
      {isLoadIng ? <Loading /> : null}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
