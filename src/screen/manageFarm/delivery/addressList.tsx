import {
  Text,
  View,
  Modal,
  Switch,
  <PERSON>rollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import React, { useState, useCallback, useEffect } from "react";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale, verticalScale } from "react-native-size-matters";
import AsyncStorage from "@react-native-async-storage/async-storage";
//Style
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import mod from "../../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../../styleSheet/style_Custom";
//Svg
import {
  iconPlant,
  iconSaveEdit,
  iconEditAddress,
  iconPlusAddress,
  iconDeleteAddress,
} from "../../../assets/svg/svg_other";
//Components
import Loading from "../../../components/loading/loading";
import { useOrientation } from "../../../hooks/useOrientation";
import ManageBar from "../../../components/appBar/manage_Bar";
//Api
import {
  deleteAddress,
  getAddressList,
} from "../../../action/Mefarm_Identity_API";
//Translation
import { useTranslation } from "../../i18n";

export default function AddressList({ navigation, route }: any) {
  const orientation = useOrientation();

  const { t } = useTranslation();
  const isPage = route.name || "";
  const params = route.params || "";
  //String
  const [addressId, setAddressId] = useState<string>("");
  //Array
  const [addressList, setAddressList] = useState<any>([]);
  //True & false
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  //Null
  const [savedAddress, setSavedAddress] = useState<string | null>(null);
  const [fullAddress, setFullAddress] = useState<string>("");
  const [addressCheckId, setAddressCheckId] = useState<string>("");
  // เusaha state ที่เก็บ id ของ address ที่ถูก switch
  const [enabledAddressId, setEnabledAddressId] = useState<string>("");

  //Function
  useFocusEffect(
    useCallback(() => {
      callAddressList();
      fetchAddress();
    }, [])
  );
  const fetchAddress = async () => {
    try {
      const addressData = await AsyncStorage.getItem("editAddress");

      if (addressData) {
        const parsedAddress = JSON.parse(addressData);
        setSavedAddress(parsedAddress);
        setAddressCheckId(parsedAddress.id);

        // สร้าง fullAddress จากข้อมูลที่มา
        const combinedAddress = `${parsedAddress.addressLine1}, ${parsedAddress.subDistrictName}, ${parsedAddress.districtName}, ${parsedAddress.provinceName}, ${parsedAddress.postalCode}`;
        setFullAddress(combinedAddress);
      } else {
        // ถ้า editAddress ถูกลบ ต้องเซ็ตค่าทั้งหมด
        setSavedAddress(null);
        setAddressCheckId("");
      }
    } catch (error) {
      console.error("Error fetching address from AsyncStorage:", error);
    }
  };
  const callAddressList = async () => {
    try {
      setLoadIng(true);
      const response = await getAddressList();
      const data = response.models || "";
      // console.log(JSON.stringify(data, null, 2));
      setAddressList(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callDeleteAress = async () => {
    try {
      setLoadIng(true);
      const response = await deleteAddress(addressId);
      const data = response.models || "";
      // console.log(JSON.stringify(data, null, 2));
      await AsyncStorage.removeItem("editAddress");
      setModalDelete(false);
      navigation.goBack();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const onDelete = (item: any) => {
    setModalDelete(true);
    setAddressId(item.id);
  };

  //Go to
  const goAddress = () => {
    navigation.navigate("PlusAddress");
  };
  const goEdit = (item: any, typeEdit: string) => {
    navigation.navigate("PlusAddress", {
      editAddress: item,
      isPage: isPage,
      typeEdit: typeEdit,
    });
  };
  const goBack = () => {
    navigation.goBack();
  };

  //Ui
  const titleHeader = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          marginTop: moderateScale(20),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("shipping_address")}
        </Text>
        <View style={{ margin: moderateScale(10) }} />
      </View>
    );
  };
  const renderAddress = ({ item, index }: any) => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          marginTop: moderateScale(10),
        }}
      >
        <View style={ctn.ctn_contentAddress}>
          <View style={oth.contentAddress}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <View style={{ flexDirection: "column", flex: 1 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {item.fullAddress}
                </Text>
              </View>
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <View style={{ margin: moderateScale(10) }} />
                <TouchableOpacity onPress={() => goEdit(item, "typeEdit")}>
                  {iconEditAddress()}
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />
                <TouchableOpacity onPress={() => onDelete(item)}>
                  {iconDeleteAddress()}
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={oth.line_profile} />
          <View style={{ margin: moderateScale(5) }} />
          <View
            style={{
              paddingHorizontal: 10,
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
              {t("set_address")}
            </Text>
            <Switch
              trackColor={{
                false: BgColor.Bg_D6D6D6,
                true: BgColor.Bg_84B8A2,
              }}
              thumbColor={
                enabledAddressId === item.id
                  ? BgColor.Bg_FFFFFF
                  : BgColor.Bg_FFFFFF
              }
              onValueChange={() => toggleSwitch(item)}
              value={enabledAddressId === item.id}
            />
          </View>
        </View>
      </View>
    );
  };
  const aleraDelete = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={modalDelete}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconPlant()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                {t("delete_area")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => setModalDelete(false)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonDelete}
                onPress={() => callDeleteAress()}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const footerBottom = () => {
    return (
      <TouchableOpacity
        style={ctn.ctn_footerAddress}
        onPress={() => goAddress()}
      >
        {iconPlusAddress()}
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
          {t("add_address")}
        </Text>
      </TouchableOpacity>
    );
  };

  // เusaha useEffect เusahaโหลดค่า switch state เมื่อหน้าถูกโหลด
  useEffect(() => {
    const loadSwitchState = async () => {
      try {
        const storedAddress = await AsyncStorage.getItem("editAddress");
        if (storedAddress) {
          const parsedAddress = JSON.parse(storedAddress);
          setEnabledAddressId(parsedAddress.id);
        }
      } catch (error) {
        console.error("Error loading switch state:", error);
      }
    };

    loadSwitchState();
  }, []);

  // เusaha toggleSwitch
  const toggleSwitch = async (addressItem: any) => {
    try {
      if (enabledAddressId === addressItem.id) {
        // ถ้ากด switch ที่ถูกเลือกอยู่ -> ยกเลิก
        setEnabledAddressId("");
        await AsyncStorage.removeItem("isEnabled");
        await AsyncStorage.removeItem("editAddress");
      } else {
        // ถ้ากด switch ใหม่
        setEnabledAddressId(addressItem.id);
        await AsyncStorage.setItem("isEnabled", JSON.stringify(true));
        await AsyncStorage.setItem("editAddress", JSON.stringify(addressItem));
      }
    } catch (error) {
      console.error("Error updating switch state:", error);
    }
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continueMain}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
          >
            {titleHeader()}
            {aleraDelete()}
            {addressList.map((item: any, index: number) =>
              renderAddress({ item, index })
            )}
            {footerBottom()}
          </ScrollView>
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
          >
            {titleHeader()}
            {aleraDelete()}
            {addressList.map((item: any, index: number) =>
              renderAddress({ item, index })
            )}
            {footerBottom()}
          </ScrollView>
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <ManageBar onBack={goBack} pageName={t("select_address")} />
      {isLoadIng ? <Loading /> : null}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
