import {
  Text,
  View,
  Modal,
  FlatList,
  TextInput,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import { createFilter } from "react-native-search-filter";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { moderateScale, verticalScale } from "react-native-size-matters";
//Style
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import mod from "../../../styleSheet/mod";
import fonstStyle, { BgColor, FonstColor } from "../../../styleSheet/style_Custom";
//Svg
import { iconCheck } from "../../../assets/svg/svg_other";
//Api
import {
  getProvince,
  postProvince,
  postDistrict,
  postSubDistrict,
} from "../../../action/Mefarm_Identity_API";
//Components
import Loading from "../../../components/loading/loading";
import ManageBar from "../../../components/appBar/manage_Bar";
import { useOrientation } from "../../../hooks/useOrientation";
//Translation
import { useTranslation } from "../../i18n";

export default function BuildAddress({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const params = route.params || "";
  const isPage = params.isPage || "";
  const editAddress = params.editAddress || "";
  const inputName = params.inputName || "";
  const inputPhone = params.inputPhone || "";
  const inputAddress = params.inputAddress || "";
  //State
  const [filter, setfilter] = useState<any>([]);
  const [tambon, setTambon] = useState<any>([]);
  const [amphure, setAmphure] = useState<any>([]);
  const [provinces, setProvinces] = useState<any>([]);
  const [filterTambon, setFilterTambon] = useState<any>([]);
  const [filterAmphure, setFilterAmphure] = useState<any>([]);
  //String
  const [name_th, setName_th] = useState<any>("");
  const [zip_code, setZip_code] = useState<any>("");
  const [showTombon, setShowTombon] = useState<any>("");
  const [typeTombon, setTypeTambon] = useState<any>("");
  const [typeAmphure, setTypeAmphure] = useState<any>("");
  const [tambon_name, setTambon_name] = useState<any>("");
  const [showAmphure, setShowAmphure] = useState<any>("");
  const [amphure_name, setAmphure_name] = useState<any>("");
  const [typeProvince, setTypeProvince] = useState<any>("");
  //Id ส่งกลับ plusAddress.tsx
  const [tombonId, setTombonId] = useState<any>("");
  const [AmphureId, setAmphureId] = useState<any>("");
  const [provinceId, setProvinceId] = useState<any>("");
  //True & false
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [modalSaveArea, setModalSaveArea] = useState<boolean>(false);

  //Function
  useEffect(() => {
    callPostProvince();
  }, []);

  //Call
  const callPostProvince = async () => {
    try {
      setLoadIng(true);
      let typeProvince = "Province";
      setTypeProvince(typeProvince);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        provinceName: "",
      };
      const response = await postProvince(req);
      const data = response.model || "";
      setProvinces(data);
      // console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callPostDistrict = async (item: any) => {
    try {
      setLoadIng(true);
      setTypeProvince("");
      let typeAmphure = "Amphure";
      let showAmphure = "SelectAmphur";
      setTypeAmphure(typeAmphure);
      setShowAmphure(showAmphure);
      setProvinceId(item.provinceId);
      setName_th(item.provinceName);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        provinceId: item.provinceId,
        districtName: "",
      };
      const response = await postDistrict(req);
      const data = response.model || "";
      setAmphure(data);
      // console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callPostSubDistrict = async (item: any) => {
    try {
      setLoadIng(true);
      setTypeAmphure("");
      let typeTombon = "Tombon";
      let showTombon = "SelectTombon";
      setTypeTambon(typeTombon);
      setShowTombon(showTombon);
      setAmphureId(item.districtId);
      setAmphure_name(item.districtName);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        districtId: item.districtId,
        districtName: "",
      };
      const response = await postSubDistrict(req);
      const data = response.model || "";
      setTambon(data);
      console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };

  //On
  const onEditProvinec = () => {
    let typeProvince = "Province";
    setTypeProvince(typeProvince);
    setTypeAmphure("");
    setTypeTambon("");
    setShowAmphure("");
    setShowTombon("");
    setAmphure_name("");
    setTambon_name("");
  };
  const onEditAmphure = () => {
    let typeAmphure = "Amphure";
    setTypeAmphure(typeAmphure);
    setTypeTambon("");
    setShowTombon("");
    setTambon_name("");
  };
  const onCleanTombon = () => {
    setTambon_name("");
    setZip_code("");
    setModalSaveArea(false);
  };
  const onCleanData = () => {
    // setDataProvinces([]);
    setName_th("");
    setAmphure_name("");
    setTambon_name("");
    setTypeTambon("");
    setTypeAmphure("");
    setShowAmphure("");
    setShowTombon("");
    setfilter([]);
    setFilterAmphure([]);
    setFilterTambon([]);
    callPostProvince();
  };

  //Go to
  const goPlusAddress = async (item: any) => {
    try {
      setZip_code(item.postalCode);
      setTombonId(item.subDistrictId);
      setTambon_name(item.subDistrictName);

      // เตรียมข้อมูลที่จะเก็บใน AsyncStorage
      const addressData = {
        masterProvinceId: provinceId || "",
        provinceName: name_th || "",
        masterDistrictId: AmphureId || "",
        districtName: amphure_name || "",
        masterSubDistrictId: item.subDistrictId || "",
        subDistrictName: item.subDistrictName || "",
        postalCode: item.postalCode || "",
        isPage: isPage || "",
        editAddress: editAddress || "",
        inputName: inputName || "",
        inputPhone: inputPhone || "",
        inputAddress: inputAddress || "",
      };

      // บันทึกข้อมูลลง AsyncStorage
      await AsyncStorage.setItem("addressData", JSON.stringify(addressData));
      setModalSaveArea(true);
    } catch (error) {
      console.error("Error saving data to AsyncStorage:", error);
    }
  };
  const searchUpdated = (text: string) => {
    const filter = provinces.filter(createFilter(text, ["provinceName"]));
    setfilter(filter);
  };
  const searchAmphureUpdated = (text: string) => {
    const filter = amphure.filter(createFilter(text, ["districtName"]));
    setFilterAmphure(filter);
  };
  const searchTombonUpdated = (text: string) => {
    const filter = tambon.filter(createFilter(text, ["subDistrictName"]));
    setFilterTambon(filter);
  };

  //FlatList
  const FlasList = () => (
    <FlatList
      data={filter == "" ? provinces : filter}
      renderItem={renderProvinces}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={title1}
      ListFooterComponent={listFooter}
    />
  );
  const FlasListAmphure = () => (
    <FlatList
      data={filterAmphure == "" ? amphure : filterAmphure}
      renderItem={renderAmphure}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={title2}
      ListFooterComponent={listFooterAmphure}
    />
  );
  const FlasListTombon = () => (
    <FlatList
      data={filterTambon == "" ? tambon : filterTambon}
      renderItem={renderTombon}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={title3}
      ListFooterComponent={listFooterTombon}
    />
  );
  const listFooter = () => {
    return <View style={{ marginBottom: moderateScale(500) }} />;
  };
  const listFooterAmphure = () => {
    return <View style={{ marginBottom: moderateScale(500) }} />;
  };
  const listFooterTombon = () => {
    return <View style={{ marginBottom: moderateScale(500) }} />;
  };

  //Ui
  const search = () => {
    return (
      <>
        {typeProvince === "Province" ? (
          <View style={oth.continueSearch}>
            <View style={oth.contentSearch}>
              <TextInput
                style={[fonstStyle.f12_light, txt.txt_606060]}
                placeholder={t("search_province")}
                onChangeText={(text) => searchUpdated(text)}
              />
            </View>
          </View>
        ) : null}

        {typeAmphure === "Amphure" ? (
          <View style={oth.continueSearch}>
            <View style={oth.contentSearch}>
              <TextInput
                style={[fonstStyle.f12_light, txt.txt_606060]}
                placeholder={t("search_bistrict")}
                onChangeText={(text) => searchAmphureUpdated(text)}
              />
            </View>
          </View>
        ) : null}

        {typeTombon === "Tombon" ? (
          <View style={oth.continueSearch}>
            <View style={oth.contentSearch}>
              <TextInput
                style={[fonstStyle.f12_light, txt.txt_606060]}
                placeholder={t("search_subdis")}
                onChangeText={(text) => searchTombonUpdated(text)}
              />
            </View>
          </View>
        ) : null}
      </>
    );
  };
  const titleProvinces = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          paddingVertical: moderateScale(20),
        }}
      >
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("select_address")}
          </Text>

          <TouchableOpacity onPress={() => onCleanData()}>
            <Text
              style={[
                fonstStyle.f14_bold,
                txt.txt_606060,
                { color: FonstColor.Tc_E74C3C },
              ]}
            >
              {t("clean")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const title1 = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          paddingVertical: moderateScale(10),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("province")}
        </Text>
      </View>
    );
  };
  const title2 = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          paddingVertical: moderateScale(10),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("district")}
        </Text>
      </View>
    );
  };
  const title3 = () => {
    return (
      <View
        style={{
          paddingHorizontal: moderateScale(20),
          paddingVertical: moderateScale(10),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("subdistrict_district")}
        </Text>
      </View>
    );
  };
  const contentSelect = () => {
    return (
      <View style={{ paddingHorizontal: moderateScale(20) }}>
        <View style={oth.continueData}>
          <View style={{ padding: moderateScale(20) }}>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <View style={name_th === "" ? oth.dotMapSelect : oth.dotMap} />
              <View style={{ margin: moderateScale(5) }} />
              <TouchableOpacity onPress={() => onEditProvinec()}>
                <Text
                  style={[
                    fonstStyle.f12_light,
                    {
                      color:
                        name_th === ""
                          ? FonstColor.Tc_84B8A2
                          : FonstColor.Tc_606060,
                    },
                  ]}
                >
                  {name_th || t("province")}
                </Text>
              </TouchableOpacity>
            </View>

            {showAmphure === "SelectAmphur" ? (
              <>
                <View style={oth.lineVitical} />
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={amphure_name === "" ? oth.dotMapSelect : oth.dotMap}
                  />
                  <View style={{ margin: moderateScale(5) }} />
                  <TouchableOpacity onPress={() => onEditAmphure()}>
                    <Text
                      style={[
                        fonstStyle.f12_light,
                        {
                          color:
                            amphure_name === ""
                              ? FonstColor.Tc_84B8A2
                              : FonstColor.Tc_606060,
                        },
                      ]}
                    >
                      {amphure_name || t("district")}
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : null}

            {showTombon === "SelectTombon" ? (
              <>
                <View style={oth.lineVitical} />
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={tambon_name === "" ? oth.dotMapSelect : oth.dotMap}
                  />
                  <View style={{ margin: moderateScale(5) }} />
                  <Text
                    style={[
                      fonstStyle.f12_light,
                      {
                        color:
                          tambon_name === ""
                            ? FonstColor.Tc_84B8A2
                            : FonstColor.Tc_606060,
                      },
                    ]}
                  >
                    {tambon_name || t("subdistrict_district")} {zip_code || ""}
                  </Text>
                </View>
              </>
            ) : null}
          </View>
        </View>
      </View>
    );
  };
  const contentProvinces = () => {
    return (
      <View style={{ paddingVertical: moderateScale(10) }}>
        <View style={oth.contentProvinces}>
          {typeProvince === "Province" ? (
            <ScrollView
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={false}
            >
              {title1()}
              {filter == "" ? (
                <>
                  {provinces.map((item: any, index: number) =>
                    renderProvinces({ item, index })
                  )}
                </>
              ) : (
                <>
                  {filter.map((item: any, index: number) =>
                    renderProvinces({ item, index })
                  )}
                </>
              )}
              {listFooter()}
            </ScrollView>
          ) : null}
          {typeAmphure === "Amphure" ? (
            <ScrollView
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={false}
            >
              {title2()}
              {filterAmphure == "" ? (
                <>
                  {amphure.map((item: any, index: number) =>
                    renderAmphure({ item, index })
                  )}
                </>
              ) : (
                <>
                  {filterAmphure.map((item: any, index: number) =>
                    renderAmphure({ item, index })
                  )}
                </>
              )}
              {listFooterAmphure()}
            </ScrollView>
          ) : null}
          {typeTombon === "Tombon" ? (
            <ScrollView
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={false}
            >
              {title3()}
              {filterTambon == "" ? (
                <>
                  {tambon.map((item: any, index: number) =>
                    renderTombon({ item, index })
                  )}
                </>
              ) : (
                <>
                  {filterTambon.map((item: any, index: number) =>
                    renderTombon({ item, index })
                  )}
                </>
              )}
              {listFooterTombon()}
            </ScrollView>
          ) : null}
        </View>
      </View>
    );
  };

  //Render
  const renderProvinces = ({ item }: any) => {
    return (
      <View style={{ padding: moderateScale(10) }}>
        <TouchableOpacity
          style={{ paddingHorizontal: moderateScale(20) }}
          onPress={() => callPostDistrict(item)}
        >
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            {item.provinceName}
          </Text>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
        <View style={oth.line_profile} />
      </View>
    );
  };
  const renderAmphure = ({ item }: any) => {
    return (
      <View style={{ padding: moderateScale(10) }}>
        <TouchableOpacity
          style={{ paddingHorizontal: moderateScale(20) }}
          onPress={() => callPostSubDistrict(item)}
        >
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            {item.districtName}
          </Text>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
        <View style={oth.line_profile} />
      </View>
    );
  };
  const renderTombon = ({ item }: any) => {
    return (
      <View style={{ padding: moderateScale(10) }}>
        <TouchableOpacity
          style={{ paddingHorizontal: moderateScale(20) }}
          onPress={() => goPlusAddress(item)}
        >
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            {item.subDistrictName}
          </Text>
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            {item.postalCode}
          </Text>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
        <View style={oth.line_profile} />
      </View>
    );
  };
  const modalAddress = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={modalSaveArea}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_Success}>
              <View style={oth.bg_Success}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_address")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => onCleanTombon()}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonAgree}
                onPress={() => navigation.goBack()}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

   //Main
   const mainPortrait = () => {
    return (
      <>
       <View style={ctn.continueMain}>
        {search()}
        {titleProvinces()}
        {contentSelect()}
        {contentProvinces()}
        {modalAddress()}
      </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
        {search()}
        {titleProvinces()}
        {contentSelect()}
        {contentProvinces()}
        {modalAddress()}
      </View>
      </SafeAreaView>
    );
  };
  return (
    <>
      <ManageBar
        onBack={() => navigation.goBack()}
        pageName={t("add_address")}
      />
      {isLoadIng ? <Loading /> : null}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
