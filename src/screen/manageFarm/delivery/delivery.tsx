import {
  Text,
  View,
  Image,
  Modal,
  TextInput,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import {
  moderateScale,
  verticalScale,
  moderateVerticalScale,
} from "react-native-size-matters";
import React, { useState, useCallback } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
//Style
import btn from "../../../styleSheet/btn";
import ctn from "../../../styleSheet/ctn";
import img from "../../../styleSheet/img";
import txt from "../../../styleSheet/txt";
import oth from "../../../styleSheet/oth";
import mod from "../../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../../styleSheet/style_Custom";
import { useFocusEffect } from "@react-navigation/native";
//Svg
import {
  iconPlant,
  iconNoImg,
  iconRight,
  iconNoImgProduct,
  iconPlusAddress,
} from "../../../assets/svg/svg_other";
//Components
import { useOrientation } from "../../../hooks/useOrientation";
import Loading from "../../../components/loading/loading";
import ManageBar from "../../../components/appBar/manage_Bar";
//Api
import { getAddressList } from "../../../action/Mefarm_Identity_API";
import { postHarvest } from "../../../action/Mefarm_Farm_API";
//Translation
import { useTranslation } from "../../i18n";

export default function Delivery({ navigation, route }: any) {
  const orientation = useOrientation();

  const params = route.params || "";
  const selectProduct = params.selectedItems || "";
  const farmUserPlotId = params.farmUserPlotId || "";
  const counth = selectProduct.length || "error";
  const docDetailMyfarm = params.docDetailMyfarm || "";
  //String
  const { t } = useTranslation();
  const [idAddress, setIdAddress] = useState<any>("");
  const [commentShip, setCommentShip] = useState<string>("");
  const [fullAddress, setFullAddress] = useState<string>("");
  //Array
  const [addressList, setAddressList] = useState<any>([]);
  //True & false
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [modalError, setModalError] = useState<boolean>(false);
  //Null
  const [savedAddress, setSavedAddress] = useState<any>(null);

  //Function
  useFocusEffect(
    useCallback(() => {
      callAddressList();
      fetchAddress();
      // console.log("Screen reset");
    }, [])
  );
  const fetchAddress = async () => {
    try {
      setLoadIng(true);
      const addressData = await AsyncStorage.getItem("editAddress");
      if (addressData !== null) {
        const parsedAddress = JSON.parse(addressData);
        setSavedAddress(parsedAddress);
        setIdAddress(parsedAddress.id);

        // สร้าง fullAddress จากข้อมูล
        const combinedAddress = `${parsedAddress.addressLine1}, ${parsedAddress.subDistrictName}, ${parsedAddress.districtName}, ${parsedAddress.provinceName}, ${parsedAddress.postalCode}`;
        setFullAddress(combinedAddress);
      } else {
        // ล้างค่าใน state หาก addressData เป็น null
        setSavedAddress(null);
        setFullAddress("");
        setIdAddress(null);
      }
    } catch (error) {
      console.error("Error fetching address from AsyncStorage:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const callAddressList = async () => {
    try {
      setLoadIng(true);
      const response = await getAddressList();
      const data = response.models || "";
      // console.log(JSON.stringify(data, null, 2));
      setAddressList(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callHarvest = async () => {
    try {
      setLoadIng(true);
      const req = {
        farmUserPlotId: farmUserPlotId,
        addressBookId: idAddress,
        requestComment: commentShip,
        details: selectProduct,
      };
      const res = await postHarvest(req);
      const data = res.model || "";
      // console.log("????", res);
      if (res?.success) {
        navigation.navigate("DetailDelivery", {
          harvestData: data,
          docDetailMyfarm: docDetailMyfarm,
        });
      } else {
        setModalError(true);
      }
      // console.log(JSON.stringify(data, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };

  //Go to
  const goAddress = () => {
    if (addressList == "") {
      navigation.navigate("PlusAddress");
    } else {
      navigation.navigate("AddressList");
    }
  };
  const goDetail = () => {
    callHarvest();
  };

  //Ui
  const titleHeader = () => {
    return (
      <View
        style={{
          marginTop: moderateScale(20),
          paddingHorizontal: moderateScale(20),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("shipping_address")}
        </Text>
      </View>
    );
  };
  const address = () => {
    return (
      <TouchableOpacity
        style={{
          marginTop: moderateScale(20),
          paddingHorizontal: moderateScale(20),
        }}
        onPress={() => goAddress()}
      >
        {savedAddress === null ? (
          <View style={ctn.ctn_PlusAddress}>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              {iconPlusAddress()}
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                {t("add_address")}
              </Text>
            </View>
          </View>
        ) : (
          <View style={ctn.ctn_PlusAddress}>
            <View style={[ctn.ctn_spaceBet, { alignItems: "center" }]}>
              <View style={{ flexDirection: "column" }}>
                <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                  {savedAddress.recipientName}, {savedAddress.mobileNo}
                </Text>

                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {fullAddress}
                </Text>
              </View>
              {iconRight()}
            </View>
          </View>
        )}
        <View style={{ margin: moderateVerticalScale(10) }} />
      </TouchableOpacity>
    );
  };
  const line = () => {
    return <View style={oth.lineDelivery} />;
  };
  const titleBottom = () => {
    return (
      <View
        style={{
          marginTop: moderateScale(20),
          paddingHorizontal: moderateScale(20),
          marginBottom: moderateScale(10),
        }}
      >
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("product_list")}
        </Text>
      </View>
    );
  };
  const renderProduct = ({ item }: any) => {
    return (
      <View style={oth.checkProduct}>
        <View style={{ alignItems: "center" }}>
          {item.imageUrl === null ? (
            <>
              <View style={oth.cardProduct}>
                <View style={ctn.ctn_noImgProduct}>{iconNoImgProduct()}</View>
              </View>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                {item.plantName}
              </Text>
            </>
          ) : (
            <>
              <View style={oth.cardProduct}>
                <Image
                  style={img.img_imgProduct}
                  source={{ uri: item.imageUrl }}
                  resizeMode="cover"
                />
              </View>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                {item.plantName}
              </Text>
            </>
          )}
        </View>
      </View>
    );
  };
  const botton = () => {
    return (
      <View style={{ alignItems: "center" }}>
        <TouchableOpacity
          style={
            savedAddress === null
              ? btn.btn_bottonListProductNon
              : btn.btn_bottonListManageFarm
          }
          onPress={() => (savedAddress === null ? null : goDetail())}
          // onPress={() => navigation.navigate("DetailDelivery")}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_white]}>{t("check")}</Text>
        </TouchableOpacity>
      </View>
    );
  };
  const modalFalse = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={modalError}>
        <View style={mod.mod_center}>
          <View style={[mod.mod_View]}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconPlant()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                {t("no_delivery")}
              </Text>
            </View>
            <TouchableOpacity
              style={[btn.btn_FlaseCancle]}
              onPress={() => setModalError(false)}
            >
              <Text
                style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
              >
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continueMain}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
          >
            {titleHeader()}
            {address()}
            {line()}

            <View
              style={{ marginTop: moderateScale(20), paddingHorizontal: 20 }}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("comment_post")}
              </Text>

              <View style={{ margin: moderateScale(5) }} />
              <View
                style={{
                  padding: 10,
                  borderRadius: 10,
                  height: 100,
                  backgroundColor: BgColor.Bg_F4F4F4,
                }}
              >
                <TextInput
                  style={[fonstStyle.f12_light, txt.txt_606060]}
                  placeholder={t("comment_post")}
                  onChangeText={setCommentShip}
                  value={commentShip}
                  editable
                  multiline
                />
              </View>
            </View>

            {titleBottom()}

            <View style={[ctn.ctn_listAllImg]}>
              {selectProduct.map((item: any, index: number) => (
                <View key={index} style={[oth.listAllImg]}>
                  {renderProduct({ item, index })}
                </View>
              ))}
              <View style={{ margin: moderateScale(120) }} />
            </View>
          </ScrollView>
        </View>
        {botton()}
        {modalFalse()}
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
          >
            {titleHeader()}
            {address()}
            {line()}
            {titleBottom()}

            <View style={[ctn.ctn_listAllImg]}>
              {selectProduct.map((item: any, index: number) => (
                <View key={index} style={[oth.listAllImg]}>
                  {renderProduct({ item, index })}
                </View>
              ))}
              <View style={{ margin: moderateScale(120) }} />
            </View>
          </ScrollView>
        </View>
        {botton()}
        {modalFalse()}
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {isLoadIng ? <Loading /> : null}
      <ManageBar
        onBack={() => navigation.goBack()}
        pageName={t("manage_productivity")}
      />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
