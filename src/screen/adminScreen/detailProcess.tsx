import {
  Text,
  View,
  Image,
  Modal,
  Alert,
  Platform,
  TextInput,
  ScrollView,
  Dimensions,
  FlatList,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import RNFS from "react-native-fs";
import FastImage from "react-native-fast-image";
import { Header as HeaderRNE } from "@rneui/themed";
import ImageViewing from "react-native-image-viewing";
import { Dropdown } from "react-native-element-dropdown";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import React, { useState, useEffect, useRef } from "react";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
//Style
import ctn from "../../styleSheet/ctn";
import btn from "../../styleSheet/btn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconPlus,
  iconCheck,
  iconError,
  iconEditImg,
  iconDelete,
  iconCameraEdit,
  iconUploadMedia,
  iconSaveProcess,
  iconGalleryEdit,
  iconDeleteProcess,
} from "../../assets/svg/svg_other";
import { goBack_gay, goBack_x } from "../../assets/svg/svg_naviagte";
//commponents
import Loading from "../../components/loading/loading";
//Translation
import { useTranslation } from "../i18n";
//Api
import {
  deleteProcess,
  postProcessAdd,
  putProcessUpdate,
  getProcessDetail,
  putProcessEditlUpdate,
} from "../../action/Mefarm_Admin_API";
import { getMyFarmDetail } from "../../action/Mefarm_Farm_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { setDocProcessDetail } from "../../Redux_Store/action";
import Images from "../../utils/imageManager";

export default function DetailProcess({ navigation, route }: any) {
  const inputRef: any = useRef(null);
  const { t } = useTranslation();
  //Dimensions
  const { width: screenWidth } = Dimensions.get("window");
  const { height: screenHeight } = Dimensions.get("window");
  //Redux
  const dispatch = useDispatch();
  const docLanguageKey = useSelector((state: any) => state.docLanguageKey);
  const docProcessDetail = useSelector((state: any) => state.docProcessDetail);
  //String
  const [commentId, setCommentId] = useState<any>("");
  const [docStatus, setDocStatus] = useState<any>("");
  const [textComment, setTextComment] = useState<any>("");
  const [inputMessage, setInputmessage] = useState<string>("");
  //Array
  const [imageFull, setImageFull] = useState<any>([]);
  const [imageUris, setImageUris] = useState<any>([]);
  const [fileNames, setFileNames] = useState<any>([]);
  const [typeNames, setTypeNames] = useState<any>([]);
  const [attachments, setAttachments] = useState<any>([]);
  const [deleteFileIds, setDeleteFileIds] = useState<string[]>([]);
  //Obj
  const [image, setImage] = useState<any>({});
  //True & Fales
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isModalSave, setModalSave] = useState<boolean>(false);
  const [isEditStutes, setEditStutes] = useState<boolean>(false);
  const [isModalDetail, setModalDetail] = useState<boolean>(false);
  const [isModalDelete, setModalDelete] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  const [isModalProcess, setModalProcess] = useState<boolean>(false);
  const [isModalEditUpdate, setModalEditUpdate] = useState<boolean>(false);
  const [isModalSaveStutes, setModalSaveStutes] = useState<boolean>(false);
  const [isModalDeleteProcess, setModalDeleteProcess] =
    useState<boolean>(false);
  //Null
  const [value, setValue] = useState(null);
  const actionSheetRef = useRef<ActionSheetRef>(null);

  const params = route.params || "";
  const itemManage = params.itemManage || "";
  const processId = itemManage.processId || "";
  const currentState = itemManage.currentState || "";
  const farmUserPlotId = itemManage.farmUserPlotId || "";
  // console.log(">>>>", farmUserPlotId);

  const data = [
    { label: t("in_progress"), value: "in progress" },
    { label: t("completed"), value: "finish" },
  ];
  //DisBotton
  const isButtonDisabled = !inputMessage.trim();
  const isBottonStutes = !value;
  //PERMISSIONS
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        // Alert.alert("❌ Camera permission denied");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  useEffect(() => {
    requestCameraPermission();
  }, []);
  useFocusEffect(
    React.useCallback(() => {
      callProcessDetail();
      // callFarmDetail();
    }, [dispatch])
  );
  const callFarmDetail = async () => {
    try {
      setLoadIng(true);
      // const res = await getMyFarmDetail(farmUserPlotId);
      // console.log("....", res);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callProcessDetail = async () => {
    try {
      setLoadIng(true);
      const req = await getProcessDetail(processId);
      const data = req.model.comments || "";
      const statusDetail = req.model.currentState;
      // console.log("Process Detail:", JSON.stringify(req.model, null, 2));

      if (data) {
        dispatch(setDocProcessDetail(data));
        setDocStatus(statusDetail);
      } else {
        dispatch(setDocProcessDetail(null));
      }
      setModalProcess(false);
      setModalDetail(false);
    } catch (error) {
      console.error("Error fetching process detail:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const callUpdateStatus = async () => {
    setModalSaveStutes(false);
    try {
      setLoadIng(true);

      const res = {
        processId: processId,
        newState: value,
      };
      const req = await putProcessUpdate(res);
      navigation.goBack();
      callProcessDetail();
      // if (value === "finish") {
      //   navigation.goBack();
      // } else {
      //   callProcessDetail();
      // }
      // console.log(JSON.stringify(req, null, 2));
      setValue(null);
    } catch (error) {
    } finally {
      setLoadIng(false);
    }
  };
  const callProcessAdd = async () => {
    setModalSave(false);
    setModalProcess(true);
    try {
      setLoadIng(true);
      const imageContent = await Promise.all(
        imageUris.map(async (uri: any, index: any) => {
          const base64Data = await convertImageToBase64(uri);
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );

      const res = {
        processId: processId,
        commentId: "",
        textComment: inputMessage,
        deleteFileIds: ["string"],
        uploadedImages: imageContent || "",
      };
      const req = await postProcessAdd(res);
      callProcessDetail();
      setInputmessage("");
      setImageUris([]);
    } catch (error) {
    } finally {
      setLoadIng(false);
    }
  };
  const callProcessEditUpdate = async () => {
    setModalEditUpdate(false);
    setModalProcess(true);
    try {
      setLoadIng(true);
      const imageContent = await Promise.all(
        imageUris.map(async (uri: any, index: any) => {
          const base64Data = await convertImageToBase64(uri);
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );
      const res = {
        processId: processId,
        commentId: commentId,
        textComment: inputMessage ? inputMessage : textComment,
        deleteFileIds: deleteFileIds || "",
        uploadedImages: imageContent || "",
      };
      const req = await putProcessEditlUpdate(res);
      callProcessDetail();
      setInputmessage("");
      setImageUris([]);
    } catch (error) {
    } finally {
      setLoadIng(false);
    }
  };
  const callProcessDelest = async () => {
    setModalDeleteProcess(false);
    try {
      setLoadIng(true);
      const req = await deleteProcess(commentId);
      // console.log(JSON.stringify(req, null, 2));
      callProcessDetail();
    } catch (error) {
    } finally {
      setLoadIng(false);
    }
  };
  const openActionSheet = () => {
    actionSheetRef.current?.show();
  };
  const closeActionSheet = () => {
    actionSheetRef.current?.hide();
  };

  //Camera & base 64
  const openCamera = () => {
    const options: any = {
      mediaType: "photo",
      saveToPhotos: true,
      quality: 1,
    };

    launchCamera(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);
        closeActionSheet();
      }
    });
  };
  const openGallery = () => {
    const options: any = {
      mediaType: "photo",
      quality: 1,
      selectionLimit: 10,
    };

    launchImageLibrary(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);
        closeActionSheet();
      }
    });
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const showPickerOptions = () => {
    Alert.alert(t("select"), t("select_import"), [
      { text: t("cancel"), style: "cancel" },
      { text: t("open_camera"), onPress: openCamera },
      { text: t("photo_gallery"), onPress: openGallery },
    ]);
  };
  const deleteImage = (index: number) => {
    const updatedImageUris = [...imageUris];
    updatedImageUris.splice(index, 1);
    setImageUris(updatedImageUris);
  };
  const onOpenDelete = (image: any) => {
    // console.log(image);
    setModalDelete(true);
    setImage(image);
  };
  const handleDeleteFile = () => {
    if (!image) return; // ตรวจสอบว่า image ไม่เป็น null หรือ undefined

    if (Array.isArray(attachments)) {
      // ลบรูปภาพออกจาก state
      const updatedImages = attachments.filter(
        (item: any) => item.id !== image.id
      );

      // อัปเดต state ของรูปภาพที่เหลือ
      setAttachments(updatedImages);

      // ถ้า `deletedFileIds` คือ state แยกเก็บไฟล์ที่ถูกลบ
      setDeleteFileIds((prev: string[]) => [...prev, image.id]);

      setModalDelete(false);

      if (updatedImages.length === 0) {
        console.log("ไม่มีรูปภาพที่เหลืออยู่");
      }
    } else {
      console.error("attachments ไม่ใช่อาร์เรย์");
    }
  };

  //Goto
  const goBack = () => {
    navigation.goBack();
  };
  const onOpenModal = () => {
    setModalProcess(true);
  };
  const onOpenModalDetail = (item: any) => {
    // console.log(item);

    setModalDetail(true);
    setCommentId(item.commentId);
    setTextComment(item.textComment);
    setAttachments(item.attachments);
  };
  const onClose = () => {
    setModalProcess(false);
    setInputmessage("");
    setValue(null);
    setImageUris([]);
  };
  const openFullImag = (item: any) => {
    setImageFull(item.thumbnailLargeUri);
    setModalFullImg(true);
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      rightComponent={rightHeader()}
      centerComponent={centerComment()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>{goBack_gay()}</TouchableOpacity>
  );
  const rightHeader = () => (
    <TouchableOpacity>
      <FastImage
        style={img.img_markgroup}
        source={Images.Maskgroup}
        resizeMode={FastImage.resizeMode.cover}
      />
    </TouchableOpacity>
  );
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("Todo_list")}
    </Text>
  );
  const content = () => {
    return (
      <>
        <View
          style={{
            marginTop: moderateScale(10),
            paddingHorizontal: moderateScale(20),
          }}
        >
          <View style={{ flexDirection: "row" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {itemManage.farmUserPlotNameTh}
            </Text>
            <View style={{ margin: moderateScale(5) }} />
            <View style={ctn.ctn_bgFarmNameuser}>
              <Text style={[fonstStyle.f12_light, txt.txt_green]}>
                {itemManage.farmNameTh}
              </Text>
            </View>
          </View>
        </View>

        <View style={ctn.ctn_bgListNote}>
          {itemManage.processTypeNameTh != "" && (
            <Text style={[fonstStyle.f14_bold, txt.txt_346359]}>
              {itemManage.processTypeNameTh || ""}
            </Text>
          )}
          <View style={{ margin: moderateScale(5) }} />
          {itemManage.processTypeNameEn != "" && (
            <Text style={[fonstStyle.f14_bold, txt.txt_346359]}>
              ({itemManage.processTypeNameEn || ""})
            </Text>
          )}
        </View>

        <View
          style={{
            marginTop: moderateScale(10),
            paddingHorizontal: moderateScale(20),
          }}
        >
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
              {t("status")}
            </Text>

            {docStatus != "Waiting" ? (
              <TouchableOpacity
                onPress={() => setEditStutes(!isEditStutes)}
                style={{
                  backgroundColor: BgColor.Bg_FFEEEE,
                  paddingHorizontal: 10,
                  borderRadius: 5,
                }}
              >
                <Text style={[fonstStyle.f14_medium, txt.txt_red]}>
                  {isEditStutes === true ? t("cancel") : t("edit_comment")}
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
          <View style={{ margin: moderateScale(10) }} />

          {isEditStutes === false ? (
            <>
              {docStatus != "Waiting" ? (
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={[
                      oth.bgStatusProcess,
                      {
                        backgroundColor:
                          docStatus === "In Progress"
                            ? BgColor.Bg_7FBDF6
                            : BgColor.Bg_84B8A2,
                      },
                    ]}
                  />

                  <Text style={[fonstStyle.f14_medium, txt.txt_606060]}>
                    {docStatus === "In Progress"
                      ? t("in_progress")
                      : t("completed")}
                  </Text>
                </View>
              ) : null}
            </>
          ) : null}

          {/* Stutes Dropdown */}
          {isEditStutes === true || docStatus === "Waiting" ? (
            <Dropdown
              style={oth.cardDrowdown}
              placeholderStyle={[fonstStyle.f14_medium, txt.txt_606060]}
              selectedTextStyle={[fonstStyle.f14_medium, txt.txt_606060]}
              data={data}
              maxHeight={300}
              labelField="label"
              valueField="value"
              placeholder={t("tap_status_update")}
              value={value}
              onChange={(item) => {
                setValue(item.value);
              }}
              renderItem={renderItem}
              renderLeftIcon={() => (
                <View
                  style={[
                    oth.bgStatusProcess,
                    {
                      backgroundColor: !value
                        ? BgColor.Bg_D6D6D6
                        : value === "finish"
                        ? BgColor.Bg_84B8A2
                        : BgColor.Bg_7FBDF6,
                    },
                  ]}
                />
              )}
            />
          ) : null}
        </View>
        <View style={{ margin: moderateScale(10) }} />

        {currentState != "Finish" ? (
          <View
            style={{
              marginTop: moderateScale(20),
              paddingHorizontal: moderateScale(20),
            }}
          >
            <View style={ctn.ctn_spaceBet}>
              <Text style={[fonstStyle.f14_medium, txt.txt_606060]}>
                {t("list_notes")}
              </Text>

              <TouchableOpacity
                style={oth.cardPludArea}
                onPress={() => onOpenModal()}
              >
                {iconPlus()}
              </TouchableOpacity>
            </View>
          </View>
        ) : null}

        {/* รายการ */}
        <View
          style={{
            paddingHorizontal: moderateScale(10),
            marginTop: moderateScale(20),
          }}
        >
          <View style={ctn.ctn_titleHeaderManage}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("date")}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("message")}
            </Text>

            <View style={{ flexDirection: "row" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("media")}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("manage")}
              </Text>
            </View>
          </View>
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
            style={{ borderWidth: 2, borderColor: BgColor.Bg_F4F4F4 }}
          >
            <FlatList
              data={docProcessDetail}
              removeClippedSubviews={false}
              style={{ borderWidth: 2, borderColor: BgColor.Bg_F4F4F4 }}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) =>
                renderProcessDetail({ item, index })
              }
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("noData")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                <View style={{ margin: moderateScale(200) }} />
              }
            />
          </ScrollView>
        </View>

        {/* {Bottn save} */}
        <TouchableOpacity
          disabled={isBottonStutes}
          onPress={() => setModalSaveStutes(true)}
          style={[
            oth.cardBottonProcess,
            {
              opacity: isBottonStutes ? 0.5 : undefined,
            },
          ]}
        >
          {iconSaveProcess()}
        </TouchableOpacity>
      </>
    );
  };
  const renderItem = (item: any) => {
    return (
      <View
        style={{
          padding: 17,
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text style={[fonstStyle.f14_medium, txt.txt_606060]}>
          {item.label}
        </Text>

        <View
          style={{
            width: 20,
            height: 20,
            borderRadius: 4,
            marginRight: 10,
            backgroundColor:
              item.value === "finish" ? BgColor.Bg_84B8A2 : BgColor.Bg_7FBDF6,
          }}
        />
      </View>
    );
  };
  const goBackProcess = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            marginTop: moderateScale(15),
            paddingHorizontal: moderateScale(20),
          }}
        ></View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => onClose()}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const goBackDetail = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            marginTop: moderateScale(15),
            paddingHorizontal: moderateScale(20),
          }}
        ></View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => setModalDetail(false)}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderImgPost = ({ item, index }: any) => {
    return (
      <View style={{ margin: moderateScale(2) }}>
        <TouchableOpacity
          onPress={() => deleteImage(index)}
          style={ctn.ctn_imgPosting}
        >
          {iconDelete()}
        </TouchableOpacity>
        <FastImage
          style={img.img_posting}
          source={{ uri: item }}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={{ margin: moderateScale(10) }} />
      </View>
    );
  };
  const renderProcessDetail = ({ item, index }: any) => {
    return (
      <>
        <View
          style={{
            marginTop: moderateScale(10),
            paddingHorizontal: moderateScale(10),
          }}
        >
          <View style={ctn.ctn_listProcess}>
            <Text
              style={[fonstStyle.f14_medium, txt.txt_606060]}
              numberOfLines={1}
            >
              {moment(item.commentDate).format("DD/MM/YY")}
            </Text>

            <Text
              style={[fonstStyle.f14_medium, txt.txt_606060, { width: "35%" }]}
              numberOfLines={1}
            >
              {item.textComment}
            </Text>

            <View
            // style={btn.btn_btnManage}
            >
              {item.attachments && item.attachments.length > 0 && (
                <View style={{ marginTop: moderateScale(5) }}>
                  <Image
                    style={{ width: 40, height: 40, borderRadius: 10 }}
                    source={{ uri: item.attachments[0].thumbnailSmallUri }}
                    resizeMode="cover"
                  />
                </View>
              )}
            </View>

            <TouchableOpacity
              style={btn.btn_btnManage}
              onPress={() => onOpenModalDetail(item)}
            >
              <Image
                style={{ width: 20, height: 20 }}
                source={Images.adminManage}
                resizeMode="cover"
              />
            </TouchableOpacity>
          </View>
        </View>
        <View style={oth.line_profile} />
      </>
    );
  };
  const modalProcess = () => {
    return (
      <Modal animationType="slide" transparent={true} visible={isModalProcess}>
        <>
          {goBackProcess()}
          <ScrollView style={ctn.continueMain}>
            {/* Stutes */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              {/* {Message} */}
              <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                {t("more_details")}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              <TextInput
                style={[
                  txt.txt_inputProcess,
                  fonstStyle.f12_light,
                  txt.txt_606060,
                ]}
                ref={inputRef}
                placeholder={t("message")}
                onChangeText={setInputmessage}
                value={inputMessage}
                textAlignVertical="top"
                autoFocus={true}
                editable
                multiline
              />

              <View style={{ margin: moderateScale(10) }} />

              {/* {Title Upload} */}
              <View style={ctn.ctn_spaceBet}>
                <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                  {t("attach_photo")}
                </Text>
                {imageUris != "" ? (
                  <TouchableOpacity
                    style={oth.cardPludArea}
                    onPress={() => openActionSheet()}
                  >
                    {iconPlus()}
                  </TouchableOpacity>
                ) : null}
              </View>
              <View style={{ margin: moderateScale(10) }} />

              {/* {Preview} */}
              <ScrollView
                horizontal
                scrollEnabled
                nestedScrollEnabled
                showsHorizontalScrollIndicator={false}
              >
                {imageUris.map((item: any, index: number) => (
                  <View key={index}>{renderImgPost({ item, index })}</View>
                ))}
              </ScrollView>

              {/* {Botton Up images} */}
              {imageUris == "" ? (
                <TouchableOpacity
                  onPress={() => openActionSheet()}
                  style={btn.btn_uploadProcess}
                >
                  {iconUploadMedia()}
                  <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                    {t("tap_upload_photo")}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </View>
          </ScrollView>

          {/* {Bottn save} */}
          <TouchableOpacity
            disabled={isButtonDisabled}
            onPress={() => setModalSave(true)}
            style={[
              oth.cardBottonProcess,
              {
                opacity: isButtonDisabled ? 0.5 : undefined,
              },
            ]}
          >
            {iconSaveProcess()}
          </TouchableOpacity>
        </>
      </Modal>
    );
  };
  const modalDetail = () => {
    return (
      <Modal animationType="slide" transparent={true} visible={isModalDetail}>
        <>
          {goBackDetail()}
          <ScrollView
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
            style={ctn.continueMain}
          >
            {/* Stutes */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              {/* {Message} */}
              <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                {t("more_details")}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              <TextInput
                style={[
                  txt.txt_inputProcess,
                  fonstStyle.f12_light,
                  txt.txt_606060,
                ]}
                ref={inputRef}
                placeholder={textComment}
                onChangeText={setInputmessage}
                value={inputMessage}
                textAlignVertical="top"
                autoFocus={true}
                editable
                multiline
              />
              <View style={{ margin: moderateScale(10) }} />

              {/* {Title Upload} */}
              <View style={ctn.ctn_spaceBet}>
                <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                  {t("attach_photo")}
                </Text>

                <TouchableOpacity
                  style={oth.cardPludArea}
                  onPress={() => openActionSheet()}
                >
                  {iconPlus()}
                </TouchableOpacity>
              </View>
              <View style={{ margin: moderateScale(10) }} />
            </View>

            {/* {Preview} */}
            <View
              style={{
                paddingBottom: 20,
                paddingHorizontal: 20,
              }}
            >
              <ScrollView
                horizontal
                scrollEnabled
                nestedScrollEnabled
                showsHorizontalScrollIndicator={false}
              >
                {imageUris.map((item: any, index: number) => (
                  <View key={index}>{renderImgPost({ item, index })}</View>
                ))}
              </ScrollView>

              <View
                style={{
                  flexWrap: "wrap",
                  flexDirection: "row",
                  justifyContent: "flex-start",
                }}
              >
                {attachments.map((item: any, index: number) => {
                  const isLastInRow = (index + 1) % 3 === 0; // เช็คว่ารูปนี้เป็นตัวสุดท้ายของแถวหรือไม่
                  return (
                    <TouchableOpacity
                      key={index}
                      onPress={() => openFullImag(item)}
                    >
                      <FastImage
                        style={{
                          width: (screenWidth - 60) / 3,
                          height: screenHeight * 0.15,
                          marginBottom: 10,
                          marginRight: isLastInRow ? 0 : 10,
                          borderRadius: 20,
                        }}
                        source={{ uri: item.thumbnailMediumUri }}
                        resizeMode={FastImage.resizeMode.cover}
                      />

                      <TouchableOpacity
                        style={oth.cardEditProcess}
                        onPress={() => onOpenDelete(item)}
                      >
                        {iconEditImg()}
                      </TouchableOpacity>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            <View style={{ margin: moderateScale(200) }} />
          </ScrollView>

          {/* {Bottn save} */}
          <TouchableOpacity
            onPress={() => setModalDeleteProcess(true)}
            style={[oth.cardBottonDeleteProcess]}
          >
            {iconDeleteProcess()}
          </TouchableOpacity>

          {/* {Bottn save} */}
          <TouchableOpacity
            onPress={() => setModalEditUpdate(true)}
            style={[oth.cardBottonProcess]}
          >
            {iconSaveProcess()}
          </TouchableOpacity>
        </>
      </Modal>
    );
  };
  const modalFullImg = () => {
    return (
      <View style={{ flex: 1 }}>
        <ImageViewing
          images={[{ uri: imageFull }]}
          imageIndex={0}
          visible={isModalFullImg}
          onRequestClose={() => setModalFullImg(!isModalFullImg)}
        />
      </View>
    );
  };
  const alertModalSave = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalSave}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_recording")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalSave(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => callProcessAdd()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertModalSaveStutes = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalSaveStutes}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_status")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalSaveStutes(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => callUpdateStatus()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertModalSaveEdit = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalEditUpdate}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_recording")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalEditUpdate(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => callProcessEditUpdate()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertReject = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDelete}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("Confirm_delete_photo")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalDelete(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                onPress={() => handleDeleteFile()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertDelete = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDeleteProcess}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("confirm_delete")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => setModalDeleteProcess(false)}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                onPress={() => callProcessDelest()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const renderActionSheet = () => {
    return (
      <ActionSheet
        ref={actionSheetRef}
        gestureEnabled
        containerStyle={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      >
        <TouchableOpacity style={{ padding: 20 }} onPress={openCamera}>
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconCameraEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("open_camera")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity style={{ padding: 20 }} onPress={openGallery}>
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconGalleryEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("photo_gallery")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity style={{ padding: 20 }} onPress={closeActionSheet}>
          <Text
            style={[fonstStyle.f14_light, txt.txt_red, { textAlign: "center" }]}
          >
            {t("cancel")}
          </Text>
        </TouchableOpacity>
      </ActionSheet>
    );
  };
  return (
    <>
      {isLoadIng ? <Loading /> : null}
      <View style={ctn.continueMain}>
        {headerBar()}
        {content()}
        {modalProcess()}
        {modalDetail()}
        {modalFullImg()}
        {alertModalSave()}
        {alertModalSaveStutes()}
        {alertReject()}
        {alertModalSaveEdit()}
        {alertDelete()}
        {renderActionSheet()}
      </View>
    </>
  );
}
