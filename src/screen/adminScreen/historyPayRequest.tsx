import {
  Text,
  View,
  TextInput,
  ScrollView,
  FlatList,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import React, { useState } from "react";
import { Header as HeaderRNE } from "@rneui/themed";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import { createFilter } from "react-native-search-filter";
//Style
import ctn from "../../styleSheet/ctn";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import { goBack_gay } from "../../assets/svg/svg_naviagte";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//commponents
import LoadingApp from "../../components/loading/loadingApp";
//Translation
import { useTranslation } from "../i18n";
//Api
import { postInvoiceList } from "../../action/Mefarm_Admin_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setDocHistoryPayment,
  setDocHistoryRequest,
} from "../../Redux_Store/action";

export default function HistoryPayRequest({ navigation }: any) {
  const { t } = useTranslation();
  //Redux
  const dispatch = useDispatch();
  const docPaymentHistory = useSelector(
    (state: any) => state.docPaymentHistory
  );
  const docRequestHistory = useSelector(
    (state: any) => state.docRequestHistory
  );
  //Array
  const [filter, setfilter] = useState<any>([]);
  const [filterRequest, setFilterRequest] = useState<any>([]);
  const [originalList, setOriginalList] = useState<any[]>([]);
  const [originalListRequest, setOriginalListRequest] = useState<any[]>([]);
  //Numbrt
  const [activeMenu, setActiveMenu] = useState<any>(1);
  const [pageSizePayment, setPageSizePayment] = useState<number>(10);
  const [pageSizeRequest, setPageSizeRequest] = useState<number>(10);
  //True & False
  const [isLoadPayment, setLoadPayment] = useState<boolean>(false);
  const [isLoadRequest, setLoadRequest] = useState<boolean>(false);

  //Function
  useFocusEffect(
    React.useCallback(() => {
      callInvoicePayment();
      callInvoiceRequest();
    }, [])
  );
  const callInvoicePayment = async () => {
    try {
      setLoadPayment(true);
      const req = {
        searchText: "",
        pageSize: pageSizePayment,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        statusName: "confirm",
        typeName: "",
      };
      const res = await postInvoiceList(req);
      const data = res.model || "";
      // console.log(JSON.stringify(res.model, null, 2));
      dispatch(setDocHistoryPayment(data));
      setOriginalList(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadPayment(false);
    }
  };
  const callInvoiceRequest = async () => {
    try {
      setLoadRequest(true);
      const req = {
        searchText: "",
        pageSize: pageSizeRequest,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        statusName: "cancel",
        typeName: "",
      };
      const res = await postInvoiceList(req);
      const data = res.model || "";
      // console.log(JSON.stringify(res.model, null, 2));
      dispatch(setDocHistoryRequest(data));
      setOriginalListRequest(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadRequest(false);
    }
  };
  const handleButtonMenu = (buttonIndex: any) => {
    if (activeMenu === buttonIndex) {
      setActiveMenu(null);
    } else {
      setActiveMenu(buttonIndex);
    }
  };

  //Filter
  const searchUpdated = (text: string) => {
    if (text === "") {
      // ถ้าช่อง search ว่าง กลับไปใช้ต้นฉบับ
      dispatch(setDocHistoryPayment(originalList));
    } else {
      const filtered = originalList.filter(
        createFilter(text, ["invoiceNumber"])
      );
      dispatch(setDocHistoryPayment(filtered));
    }
  };

  const searchUpdatedRequest = (text: string) => {
    if (text === "") {
      // ถ้าช่อง search ว่าง กลับไปใช้ต้นฉบับ
      dispatch(setDocHistoryRequest(originalListRequest));
    } else {
      const filtered = originalListRequest.filter(
        createFilter(text, ["invoiceNumber"])
      );
      dispatch(setDocHistoryRequest(filtered));
    }
  };

  //Load
  const loadMoreDataPeyment = async () => {
    setLoadPayment(true);
    setPageSizePayment((prev) => prev + 10);
    await callInvoicePayment();
    setLoadPayment(false);
  };
  const loadMoreDataRequest = async () => {
    setLoadRequest(true);
    setPageSizeRequest((prev) => prev + 10);
    await callInvoiceRequest();
    setLoadRequest(false);
  };

  //Goto
  const goBack = () => {
    navigation.goBack();
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_EEF5F1}
      leftComponent={lefHeader()}
      centerComponent={centerComment()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>{goBack_gay()}</TouchableOpacity>
  );
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        ,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("history")}
    </Text>
  );
  const renderPayment = ({ item, index }: any) => {
    return (
      <View
        style={{
          marginTop: moderateScale(10),
        }}
      >
        <View style={oth.cardApprove}>
          <View style={ctn.ctn_spaceBet}>
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {item.invoiceNumber}
            </Text>

            {/* <View style={[oth.bgStatusArea]}>
              <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                {t("rental_fee")}
              </Text>
            </View> */}
          </View>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[fonstStyle.f14_bold, txt.txt_606060, { width: "50%" }]}
            numberOfLines={1}
          >
            {t("user_name")}: {item.customerName}
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[fonstStyle.f14_medium, txt.txt_606060, { width: "50%" }]}
            numberOfLines={1}
          >
            {t("approve")}: {item.adminConfirmName}
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          {/* <Text
            style={[fonstStyle.f14_light, txt.txt_606060, { width: "95%" }]}
            numberOfLines={5}
          >
            {t("comment_post")}: {item.descriptionTh || "-"}
          </Text> */}
          <View style={{ margin: moderateScale(2) }} />

          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {moment(item.invoiceDate)
                .locale(t("locale"))
                .format("DD MMMM YYYY HH:mm")}
            </Text>
            <View style={{ flexDirection: "column" }}>
              <View
                style={[oth.bgStatus, { backgroundColor: BgColor.Bg_E5EFE1 }]}
              >
                <Text style={[fonstStyle.f14_bold, txt.txt_6AB252]}>
                  {t("approve")}
                </Text>
              </View>
              <View style={{ margin: moderateScale(2) }} />
              <Text
                style={[
                  fonstStyle.f18_bold,
                  txt.txt_6AB252,
                  { textAlign: "right" },
                ]}
              >
                ฿ {item.invoiceAmount}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderRequest = ({ item, index }: any) => {
    return (
      <View
        style={{
          marginTop: moderateScale(10),
        }}
      >
        <View style={oth.cardApprove}>
          <View style={ctn.ctn_spaceBet}>
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {item.invoiceNumber}
            </Text>

            {/* <View style={[oth.bgStatusArea]}>
              <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                {t("rental_fee")}
              </Text>
            </View> */}
          </View>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[fonstStyle.f14_bold, txt.txt_606060, { width: "50%" }]}
            numberOfLines={1}
          >
            {t("user_name")}: {item.customerName}
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[fonstStyle.f14_medium, txt.txt_606060, { width: "50%" }]}
            numberOfLines={1}
          >
            {t("approve")}: {item.adminConfirmName}
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          <Text
            style={[fonstStyle.f14_light, txt.txt_606060, { width: "95%" }]}
            numberOfLines={5}
          >
            {t("comment_post")}: {item.descriptionTh || "-"}
          </Text>
          <View style={{ margin: moderateScale(2) }} />

          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {moment(item.invoiceDate)
                .locale(t("locale"))
                .format("DD MMMM YYYY HH:mm")}
            </Text>
            <View style={{ flexDirection: "column" }}>
              <View
                style={[oth.bgStatus, { backgroundColor: BgColor.Bg_FFDAC0 }]}
              >
                <Text style={[fonstStyle.f14_bold, txt.txt_orange]}>
                  {t("not_approved")}
                </Text>
              </View>
              <View style={{ margin: moderateScale(2) }} />
              <Text
                style={[
                  fonstStyle.f18_bold,
                  txt.txt_orange,
                  { textAlign: "right" },
                ]}
              >
                ฿ {item.invoiceAmount}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const content = () => {
    return (
      <>
        <View style={{ alignItems: "center" }}>
          {/* {Select} */}
          <View
            style={{
              flexDirection: "row",
              marginTop: moderateScale(10),
              marginBottom: moderateScale(10),
            }}
          >
            <TouchableOpacity
              onPress={() => handleButtonMenu(1)}
              style={{
                width: "45%",
                padding: 10,
                borderRadius: 5,
                backgroundColor:
                  activeMenu === 1 ? BgColor.Bg_7BAE6A : BgColor.Bg_FFFFFF,
              }}
            >
              <Text
                style={[
                  fonstStyle.f14_bold,
                  activeMenu === 1 ? txt.txt_white : txt.txt_606060,
                  { textAlign: "center" },
                ]}
              >
                {t("approve")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleButtonMenu(2)}
              style={{
                width: "45%",
                padding: 10,
                borderRadius: 5,
                backgroundColor:
                  activeMenu === 2 ? BgColor.Bg_FF9934 : BgColor.Bg_FFFFFF,
              }}
            >
              <Text
                style={[
                  fonstStyle.f14_bold,
                  activeMenu === 2 ? txt.txt_white : txt.txt_606060,
                  { textAlign: "center" },
                ]}
              >
                {t("not_approved")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={{ backgroundColor: BgColor.Bg_EEF5F1, flex: 1 }}>
          {/* {Search} */}
          <View style={oth.continueSearchHistory}>
            <View style={oth.contentSearchManage}>
              {activeMenu === 1 ? (
                <TextInput
                  style={[fonstStyle.f12_light, txt.txt_606060]}
                  placeholder={t("search")}
                  onChangeText={searchUpdated}
                />
              ) : (
                <TextInput
                  style={[fonstStyle.f12_light, txt.txt_606060]}
                  placeholder={t("search")}
                  onChangeText={searchUpdatedRequest}
                />
              )}
            </View>
          </View>

          {/* {DocPayment} */}
          {activeMenu === 1 && (
            <FlatList
              data={docPaymentHistory}
              style={{ backgroundColor: BgColor.Bg_EEF5F1 }}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={false}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) => renderPayment({ item, index })}
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("noData")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                <>
                  <View style={{ margin: moderateScale(20) }} />
                  {isLoadPayment ? LoadingApp() : null}
                  <View style={{ margin: moderateScale(200) }} />
                </>
              }
            />
          )}

          {activeMenu === 2 && (
            <FlatList
              data={docRequestHistory}
              style={{ backgroundColor: BgColor.Bg_EEF5F1 }}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={false}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) => renderRequest({ item, index })}
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("noData")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                <>
                  <View style={{ margin: moderateScale(20) }} />
                  {isLoadRequest ? LoadingApp() : null}
                  <View style={{ margin: moderateScale(200) }} />
                </>
              }
            />
          )}
        </View>
      </>
    );
  };

  return (
    <>
      {/* {isLoadIng ? <Loading /> : null} */}
      <View style={ctn.continueMain}>
        {headerBar()}
        {content()}
      </View>
    </>
  );
}
