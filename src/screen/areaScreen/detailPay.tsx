import {
  View,
  Text,
  Image,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import React, { useEffect, useState, useRef } from "react";
import MapView, { Polygon, PROVIDER_GOOGLE } from "react-native-maps";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import map from "../../styleSheet/map";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconQrPay,
  iconBanking,
  iconMapArea,
  iconQrPayChang,
  iconBankingChang,
} from "../../assets/svg/svg_other";
import { goBack_Bg } from "../../assets/svg/svg_naviagte";
//Components
import { useOrientation } from "../../hooks/useOrientation";
import LoadingFarm from "../../components/loading/loadingFarm";
import Null_Bar from "../../components/appBar/null_Bar";
import PayMent from "../../components/payMent/payMent";
import Package from "../../components/package/package";
import LoadingDetail from "../../components/loading/loadingDetail";
import Default_Bar from "../../components/appBar/default_Bar";
//Translation
import { useTranslation } from "../i18n";
//Api
import { getPayMentApi } from "../../action/Mefarm_Farm_API";
//Redux
import {
  setRegionData,
  setPolygonData,
  setPaymentMethods,
  setRegionPlot,
  setPolygonPlot,
} from "./../../Redux_Store/action";
import { useDispatch, useSelector } from "react-redux";

export default function DetailPay({ navigation, route }: any) {
  const orientation = useOrientation();
  //Rudux
  const dispatch = useDispatch();
  const region = useSelector((state: any) => state.region);
  const polygon = useSelector((state: any) => state.polygon);
  const regionPlot = useSelector((state: any) => state.regionPlot);
  const polygonPlot = useSelector((state: any) => state.polygonPlot);
  const paymentMethods = useSelector((state: any) => state.paymentMethods);

  const { t } = useTranslation();
  //State
  const mapViewRef = useRef<MapView>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  //String
  const [isPrice, setPrice] = useState<string>("");
  const [farmName, setFarmName] = useState<string>("");
  const [packAgeId, setpackAgeId] = useState<string>("");
  const [isNamePolt, setNamePolt] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [locationName, setLocationName] = useState<string>("");
  const [provinceName, setProvinceName] = useState<string>("");
  //Array
  const [isDataPolt, setDataPolt] = useState<any>([]);
  const [isDataPackage, setDataPackage] = useState<any>([]);
  const [selectPackAge, setSelectPackAge] = useState<any>([]);
  //OBJ
  const [paymentData, setPaymentData] = useState<any>({});
  //True & False
  const [mapReady, setMapReady] = useState<boolean>(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  //Number
  const [forceReload, setForceReload] = useState<number>(0);
  //Color
  const colors = [
    ["#66C78E", "#74cc99", "#83d2a3", "#83d2a3", "#9fdcb8"],
    ["#6EBBFF", "#82c4ff", "#95cdff", "#a9d7ff", "#bce0ff"],
    ["#478EFA", "#6EA5FF", "#82b1ff", "#82b1ff", "#82b1ff"],
    ["#FFD166", "#FFE599", "#ffeaad", "#ffefc0", "#fff4d4"],
    ["#FF7B51", "#FF9274", "#ffa188", "#ffb19b", "#ffc0af"],
    ["#FF6F91", "#FFA0B5", "#ffb4c4", "#ffc7d4", "#ffdbe3"],
    ["#FF564A", "#FF8479", "#ff968d", "#ffa8a0", "#ffbab4"],
    ["#0C62AF", "#4196E1", "#529fe4", "#63a9e6", "#74b2e9"],
  ];
  const colorBottom = [
    "#66C78E",
    "#6EBBFF",
    "#478EFA",
    "#FFD166",
    "#FF7B51",
    "#FF6F91",
    "#FF564A",
    "#0C62AF",
  ];
  //Function
  useFocusEffect(
    React.useCallback(() => {
      const loadData = async () => {
        calldata();
      };
      loadData();
    }, [dispatch])
  );
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!mapReady) {
        // console.warn("Map was not ready within 1 second. Forcing re-render.");
        setForceReload((prev) => prev + 1);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [mapReady, forceReload]);
  useEffect(() => {
    const callPayMent = async () => {
      try {
        setLoadIng(true);
        const response = await getPayMentApi();
        const payMentData = response.model || "";
        console.log(JSON.stringify(payMentData, null, 2));
        // setPaymentMethods(payMentData);
        dispatch(setPaymentMethods(payMentData));
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    };
    callPayMent();
  }, []);
  const calldata = async () => {
    try {
      setLoadIng(true);
      const params = route.params || {};
      // Package
      const dataPackage = params.dataPackage || [];
      setDataPackage(dataPackage);
      if (Array.isArray(dataPackage) && dataPackage.length > 0) {
        setpackAgeId(dataPackage[0].id);
      }

      // Plot
      const dataPolt = params.dataPolt || {};
      setDataPolt(dataPolt);
      setNamePolt(dataPolt.plotName || "");

      // Farm Detail
      const dataFram = params.dataFram || {};
      setFarmName(dataFram.farmName || "");
      setLocationName(dataFram.location || "");
      setDescription(dataFram.description || "");
      setProvinceName(dataFram.provinceName || "");

      // Map
      const geometries = dataPolt.geometries || [];
      if (Array.isArray(geometries) && geometries.length > 0) {
        const newPolygon = geometries.map((mapData: any) => ({
          // latitude: mapData.lat,
          // longitude: mapData.lng,
          latitude: Number(mapData.lat),
          longitude: Number(mapData.lng),
        }));
        const initialRegion = {
          latitude: newPolygon[0].latitude,
          longitude: newPolygon[0].longitude,
          latitudeDelta: 0.002,
          longitudeDelta: 0.002,
        };
        dispatch(setRegionData(initialRegion));
        setTimeout(() => {
          dispatch(setPolygonData(newPolygon));
        }, 200);
      } else {
        dispatch(setRegionData(null));
        dispatch(setPolygonData([]));
        // console.error("Invalid geometries data.");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const onSetPackAge = (item: any, index: number, isSelected: boolean) => {
    if (isSelected) {
      // ล้างข้อมูลถ้ากดซ้ำที่ปุ่มเดิม
      setSelectPackAge(null);
      setPrice("");
    } else {
      // ตั้งค่าข้อมูลเมื่อเลือกแพ็คเกจใหม่
      setSelectPackAge(item);
      setPrice(item.price);
    }
  };
  const goNext = async () => {
    navigation.navigate("QrCodepay", {
      isDataPolt: isDataPolt,
      isDataPackage: selectPackAge,
      paymentData: paymentData,
    });
  };
  const goBack = () => {
    navigation.goBack();
  };
  const selectPayMent = (item: any, index: number) => {
    // console.log(index);
    setSelectedIndex(index);
    setPaymentData(item);
  };

  //Ui
  const rendergoBack = () => (
    <>
      <TouchableOpacity
        style={btn.btn_costomGobackDetailPay}
        onPress={() => goBack()}
      >
        {goBack_Bg()}
      </TouchableOpacity>
      <View style={ctn.ctn_headerDetailPay}>
        <Text style={[fonstStyle.f14_bold, txt.txt_headerDetailPay]}>
          {isNamePolt}
        </Text>
      </View>
    </>
  );
  const renderMap = () => {
    return (
      <View style={map.containerMap}>
        {!mapReady && <LoadingDetail />}
        <MapView
          ref={mapViewRef}
          style={[map.map]}
          initialRegion={region}
          key={forceReload}
          zoomEnabled={true}
          scrollEnabled={true}
          toolbarEnabled={false}
          mapType="satellite"
          provider={PROVIDER_GOOGLE}
          onMapReady={() => {
            setMapReady(true);
          }}
        >
          {polygon.length > 0 && (
            <Polygon
              coordinates={polygon}
              strokeColor="yellow"
              strokeWidth={3}
              lineCap="square"
              lineJoin="miter"
            />
          )}
        </MapView>
      </View>
    );
  };
  const renderDetail = () => {
    return (
      <View style={ctn.ctn_detailPay}>
        <Text
          style={[
            fonstStyle.f14_bold,
            txt.txt_606060,
            {
              paddingHorizontal: moderateScale(20),
              marginTop: moderateScale(10),
            },
          ]}
        >
          {t("Payment_details")}
        </Text>
        <View style={{ margin: moderateScale(5) }} />

        {/* {line smill} */}
        <View style={oth.line_profile} />
        <ScrollView>
          <View
            style={{
              paddingHorizontal: moderateScale(20),
              marginTop: moderateScale(10),
            }}
          >
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("Cultivation_area")}
            </Text>
            <View style={{ margin: moderateScale(10) }} />
            <View style={{ flexDirection: "row" }}>
              {iconMapArea()}
              <View
                style={{
                  flexDirection: "column",
                  marginLeft: moderateScale(10),
                }}
              >
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {farmName || "-"}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {provinceName || "-"}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {locationName || "-"}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {description || "-"}
                </Text>
              </View>
            </View>
            <View style={{ margin: moderateScale(10) }} />
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("Package")}
            </Text>

            <Package
              data={isDataPackage}
              renderPackAge={renderPackAge}
              t={t}
              fonstStyle={fonstStyle}
              txt={txt}
              moderateScale={moderateScale}
            />
          </View>

          {/* {Pay} */}
          <PayMent
            paymentMethods={paymentMethods}
            selectedIndex={selectedIndex}
            selectPayMent={selectPayMent}
            t={t}
            btn={btn}
            fonstStyle={fonstStyle}
            txt={txt}
            moderateScale={moderateScale}
            iconBanking={iconBanking}
            iconBankingChang={iconBankingChang}
          />
          <View style={{ margin: "20%" }} />
        </ScrollView>

        {/* {Botton Pay} */}
        <View style={{ alignItems: "center", bottom: 20 }}>
          <TouchableOpacity
            disabled={selectedIndex === null}
            style={
              isPrice && selectedIndex != null
                ? btn.btn_detailPay
                : btn.btn_detailPayNon
            }
            onPress={() => (isPrice && selectedIndex != null ? goNext() : null)}
          >
            <View
              style={[
                isPrice && selectedIndex != null
                  ? ctn.ctn_btnDetailPay
                  : ctn.ctn_btnDetailPayNon,
              ]}
            >
              <Text
                style={[
                  fonstStyle.f14_bold,
                  isPrice && selectedIndex != null
                    ? txt.txt_white
                    : txt.txt_Seation,
                ]}
              >
                {t("Confirm_payment")}
              </Text>

              {isPrice && selectedIndex != null && (
                <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                  ฿ {isPrice}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderPackAge = ({ item, index }: any) => {
    const packageDetails = item.packageDetails || [];
    const isSelected = selectPackAge?.id === item.id;
    const selectedColors = colors[index % colors.length];
    const selectBottom = colorBottom[index % colorBottom.length];

    return (
      <View style={{ padding: moderateScale(2) }}>
        <LinearGradient
          colors={selectedColors}
          start={{ x: 0, y: 1 }}
          end={{ x: 0, y: 0 }}
          style={[oth.card_packAge]}
        >
          <View
            style={{ padding: moderateScale(20), alignItems: "flex-start" }}
          >
            <View style={{ alignItems: "center" }}>
              <View
                style={[btn.btn_bgPackage, { backgroundColor: selectBottom }]}
              >
                <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                  {item.packageName}
                </Text>
              </View>
            </View>

            <View style={{ margin: moderateScale(5) }} />
            {packageDetails.map((detail: any, detailIndex: number) =>
              detail.id ? (
                <React.Fragment key={`${detail.id}-${detailIndex}`}>
                  <Text
                    style={[
                      fonstStyle.f12_medium,
                      txt.txt_white,
                      { zIndex: 999 },
                    ]}
                  >
                    ๐ {detail.itemName}{" "}
                    {detail.quantity === 0 ? "" : detail.quantity}{" "}
                    {detail.unitName === "ไม่จำกัด" ? "" : detail.unitName}
                  </Text>
                </React.Fragment>
              ) : null
            )}

            <View style={{ margin: moderateScale(5) }} />
            <Text style={[fonstStyle.f16_bold, txt.txt_white]}>
              ฿ {item.price} / {item.unit}
            </Text>

            <Image
              style={img.img_gift}
              source={{ uri: item.imageUrl }}
              resizeMode="contain"
              blurRadius={4}
            />

            <TouchableOpacity
              style={[
                !isSelected ? btn.btn_selectPackage : btn.btn_selectButton,
                { backgroundColor: !isSelected ? selectBottom : "white" },
              ]}
              onPress={() => onSetPackAge(item, index, isSelected)}
            >
              <Text
                style={[
                  fonstStyle.f14_bold,
                  { color: isSelected ? selectBottom : "white" },
                ]}
              >
                {isSelected ? t("cancel") : t("select")}
              </Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continue}>
          {/* {rendergoBack()} */}
          {renderMap()}
          {renderDetail()}
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continue}>
          {/* {rendergoBack()} */}
          {renderMap()}
          {renderDetail()}
        </View>
      </SafeAreaView>
    );
  };

  if (!region || !regionPlot) {
    return <LoadingFarm />;
  }

  return (
    <>
      {/* {mapReady === false ? <LoadingFarm /> : null} */}
      <StatusBar barStyle={"dark-content"} hidden={false} />
      <Default_Bar onBack={goBack} title={isNamePolt} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
