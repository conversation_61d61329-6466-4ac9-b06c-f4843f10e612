import {
  View,
  Text,
  Modal,
  Image,
  FlatList,
  StatusBar,
  ScrollView,
  Dimensions,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import React, { useEffect, useState, useRef } from "react";
import MapView, { Polygon, PROVIDER_GOOGLE } from "react-native-maps";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import map from "../../styleSheet/map";
import fonstStyle, { FonstColor, BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconPlans,
  iconFileNon,
  iconMapArea,
  iconNextDetail,
  iconSelectPlant,
  iconCommentArea,
} from "../../assets/svg/svg_other";
import { goBack_Bg } from "../../assets/svg/svg_naviagte";
//Components
import Images from "../../utils/imageManager";
import Null_Bar from "../../components/appBar/null_Bar";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingFarm from "../../components/loading/loadingFarm";
import LoadingDetail from "../../components/loading/loadingDetail";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import Default_Bar from "../../components/appBar/default_Bar";
//Api
import {
  getFarmPackAge,
  getPoltFarmApi,
  getFarmDetailApi,
  getPoltDetailApi,
} from "../../action/Mefarm_Farm_API";
//Translation
import { useTranslation } from "../i18n";
//Redux
import {
  setDocPolt,
  setRegionData,
  setPolygonData,
  setRegionPlot,
  setPolygonPlot,
} from "./../../Redux_Store/action";
import { useDispatch, useSelector } from "react-redux";
import img from "../../styleSheet/img";

export default function DetailArea({ navigation, route }: any) {
  const orientation = useOrientation();
  //Rudux
  const dispatch = useDispatch();
  const region = useSelector((state: any) => state.region);
  const polygon = useSelector((state: any) => state.polygon);
  const regionPlot = useSelector((state: any) => state.regionPlot);
  const polygonPlot = useSelector((state: any) => state.polygonPlot);
  const docPolt = useSelector((state: any) => state.docPolt);
  const selectedFarm = useSelector((state: any) => state.selectedFarm);
  const { t } = useTranslation();
  //String
  const [isStatus, setStatus] = useState<string>("");
  const [isFarmId, setFarmId] = useState<string>("");
  const [location, setLocation] = useState<string>("");
  const [feedbacks, setFeedbacks] = useState<string>("");
  const [isNamePolt, setNamePolt] = useState<string>("");
  const [isFarmName, setFarmName] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [isProvinceName, setProvinceName] = useState<string>("");
  //Array
  const [framDetail, setFramDetail] = useState<any>([]);
  const [detailSoil, setDetailSoil] = useState<any>([]);
  const [docListFarm, setDocListFarm] = useState<any>([]);
  const [docListPlants, setDocListPlants] = useState<any>([]);
  const [isDocPackargeIn, setDocPackageIn] = useState<any>([]);
  const [detailRecmomended, setDetailRecommended] = useState<any>([]);
  //Null
  const mapRef = useRef<MapView>(null);
  const [isItemPolt, setItemPolt] = useState<any>(null);
  const [isChoosePolt, setChoosePolt] = useState<any>(null);
  const [selectedPlot, setSelectedPlot] = useState<any>(null);
  //Number
  const [activeIndex, setActiveIndex] = useState(0);
  const [activeButton, setActiveButton] = useState<any>(1);
  const [forceReload, setForceReload] = useState<number>(0);
  const [isSquareMeters, setSquareMeters] = useState<number>(0);
  const [forcePlotReload, setForcePlotReload] = useState<number>(0);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [mapReady, setMapReady] = useState<boolean>(false);
  const [checkMap, setCheckMap] = useState<boolean>(false);
  const [isShowVeget, setShowVeget] = useState<boolean>(false);
  const [mapPlotReady, setMapPlotReady] = useState<boolean>(false);

  const [imagesShow, setImagesShow] = useState<any>([]);
  const { width } = Dimensions.get("window");
  const handleScroll = (event: any) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / width);
    setActiveIndex(index);
  };

  useEffect(() => {
    if (docPolt && docPolt.length > 0) {
      if (isChoosePolt === null) {
        onSelectPolt(docPolt[0]);
      } else {
        onSelectPolt(isChoosePolt);
      }
    }
  }, [docPolt, isChoosePolt]);
  useFocusEffect(
    React.useCallback(() => {
      let isActive = true;

      const loadData = async () => {
        if (isActive) {
          await callFarmDetail();
          await callPoltFarm();
        }
      };
      loadData();
      return () => {
        isActive = false;
        // dispatch(setRegionData(null));
        // dispatch(setPolygonData([]));
        // dispatch(setPolygonsPlanting([]));
      };
    }, [dispatch, route.params?.docFarm?.id])
  );
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!mapReady) {
        setForceReload((prev) => prev + 1);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [mapReady, forceReload]);
  useEffect(() => {
    // if (!isShowVeget) return;
    if (mapPlotReady) return;

    const timer = setTimeout(() => {
      // if (!mapPlotReady) {
      setForcePlotReload((prev) => prev + 1);
      // }
    }, 100);

    return () => clearTimeout(timer);
  }, [mapPlotReady, forcePlotReload, isShowVeget]);
  const callFarmDetail = async () => {
    const params = route.params || {};
    const docFarm = params.docFarm || {};
    const idFarm = docFarm.id || "";
    setFarmId(idFarm);
    setDocListFarm(docFarm);

    try {
      setLoadIng(true);
      const response = await getFarmDetailApi(idFarm);
      const farmDetail = response.model || {};
      setFramDetail(farmDetail);
      setFarmName(farmDetail.farmName || "");
      setProvinceName(farmDetail.provinceName || "");
      setDocListPlants(farmDetail.recommendedPlant || []);
      setLocation(farmDetail.location || "");
      setDescription(farmDetail.description || "");
      setFeedbacks(farmDetail.feedbacks || "");
      setImagesShow(farmDetail.imagesDetail);
      // console.log("farmDetail", farmDetail);
      // Map
      const geometries = farmDetail.geometries || [];
      if (Array.isArray(geometries) && geometries.length > 0) {
        const newPolygon = geometries.map((mapData: any) => ({
          // latitude: mapData.lat,
          // longitude: mapData.lng,
          latitude: Number(mapData.lat),
          longitude: Number(mapData.lng),
        }));

        const initialRegion = {
          latitude: newPolygon[0].latitude,
          longitude: newPolygon[0].longitude,
          latitudeDelta: 0.002,
          longitudeDelta: 0.002,
        };
        // console.log("initialRegion", initialRegion);

        dispatch(setRegionData(initialRegion));
        // delay การ set polygon เพื่อให้ MapView แสดงก่อน
        setTimeout(() => {
          dispatch(setPolygonData(newPolygon));
        }, 200);
      } else {
        dispatch(setRegionData(null));
        dispatch(setPolygonData([]));
        // console.error("Invalid geometries data.");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callPoltFarm = async () => {
    try {
      setLoadIng(true);
      const response = await getPoltFarmApi(isFarmId);
      const poltArea = response.model || "";
      // console.log(JSON.stringify(poltArea, null, 2));
      // setDocPolt(poltArea);
      dispatch(setDocPolt(poltArea));
    } catch (error) {
      console.log("error:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const handleButtonClick = (buttonIndex: any) => {
    // console.log(activeButton, buttonIndex);
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };

  //On
  const onClosePolt = () => {
    setItemPolt(null);
    setShowVeget(false);
    setMapPlotReady(false);
  };
  const onPolt = () => {
    callPoltFarm();
    setShowVeget(true);
  };
  const onSelectPolt = async (item: any) => {
    const status = item.status || "";
    const name = item.plotName || "";
    const square = item.squareMeters || "";
    const number = item.numberOfSequence || "";
    const poltId = item.id || "";
    const recommendedPlant = Array.isArray(item.recommendedPlant)
      ? item.recommendedPlant
      : [];
    const plantNames = Object.fromEntries(
      recommendedPlant.map((plant: any) => [plant.id, plant.plantName])
    );
    const soilTypes = Array.isArray(item.soilTypes) ? item.soilTypes : [];
    const soilNames = Object.fromEntries(
      soilTypes.map((plant: any) => [plant.id, plant.soilTypeName])
    );
    const responseDetail = await getPoltDetailApi(poltId);
    const poltDetail = responseDetail.model || {};

    // Map
    const geometries = poltDetail.geometries || [];
    if (Array.isArray(geometries) && geometries.length > 0) {
      const newPolygon = geometries.map((mapData: any) => ({
        latitude: mapData.lat,
        longitude: mapData.lng,
      }));
      const initialRegion = {
        latitude: newPolygon[0].latitude,
        longitude: newPolygon[0].longitude,
        latitudeDelta: 0.0005,
        longitudeDelta: 0.0005,
      };
      dispatch(setRegionPlot(initialRegion));
      // delay การ set polygon เพื่อให้ MapView แสดงก่อน
      setTimeout(() => {
        dispatch(setPolygonPlot(newPolygon));
      }, 200); // 200ms หรือปรับตามต้องการ
    } else {
      dispatch(setRegionPlot(null));
      dispatch(setPolygonPlot([]));
      console.error("Invalid geometries data.");
    }

    const responsPackAge = await getFarmPackAge(poltId);
    const packAge = responsPackAge.model || "";
    setDocPackageIn(packAge);
    setItemPolt(poltDetail);
    setStatus(status);
    setNamePolt(name);
    setDetailSoil(soilNames);
    setSquareMeters(square);
    setSelectedPlot(number);
    setDetailRecommended(plantNames);
  };
  const onItemPolt = () => {
    setChoosePolt(isItemPolt); //สำคัญ
    setShowVeget(false);
    setMapPlotReady(false);
  };

  //FlasList
  const flasListPolt = () => (
    <FlatList
      data={docPolt}
      horizontal={true}
      nestedScrollEnabled={true}
      renderItem={renderPoltArea}
      contentContainerStyle={{
        alignItems: "center",
        justifyContent: "center",
        padding: moderateScale(5),
      }}
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item, index) => index.toString()}
    />
  );
  const flasListPlants = () => (
    <FlatList
      data={docListPlants}
      nestedScrollEnabled={true}
      renderItem={renderPlants}
      keyExtractor={(item, index) => index.toString()}
    />
  );

  //Go to
  const goDetail = () => {
    handleButtonClick(1);
  };
  const goLocation = () => {
    handleButtonClick(2);
  };
  const goPlanst = () => {
    handleButtonClick(3);
  };
  const goReview = () => {
    handleButtonClick(4);
  };
  const goNext = () => {
    navigation.navigate("DetailPay", {
      dataPolt: isChoosePolt,
      dataPackage: isDocPackargeIn,
      dataFram: framDetail,
    });
  };
  const goBack = () => {
    navigation.goBack();
    dispatch(setRegionData(null));
    dispatch(setPolygonData([]));
  };

  //Ui
  const rendergoBack = () => (
    <TouchableOpacity
      style={btn.btn_costomGobackDetailPay}
      onPress={() => goBack()}
    >
      {goBack_Bg()}
    </TouchableOpacity>
  );
  const renderGallery = () => {
    // const displayedImages = imagesShow.slice(0, 5);
    const displayedImages = imagesShow;
    const validImages = displayedImages.filter((img: any) => img?.imageUrl);

    // ถ้าไม่มีรูป valid
    if (validImages.length === 0) {
      return (
        <View style={img.img_noImageSlider}>
          <Image
            source={Images.imgOnArea}
            style={{ width: 150, height: 150 }}
          />
        </View>
      );
    }

    return (
      <View>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {validImages.map((image: any, index: number) => (
            <MyImageComponent
              key={image.id || index}
              imageUrl={image.imageUrl}
              style={{
                width,
                height:
                  orientation === "portrait"
                    ? moderateScale(250)
                    : moderateScale(150),
              }}
            />
          ))}
        </ScrollView>

        {/* Indicator */}
        <View style={oth.Indicator}>
          {validImages.map((_: any, index: number) => (
            <View
              key={index}
              style={[
                oth.IndicatorCheck,
                {
                  backgroundColor:
                    activeIndex === index
                      ? BgColor.Bg_84B8A2
                      : BgColor.Bg_FFFFFF,
                },
              ]}
            />
          ))}
        </View>
      </View>
    );
  };
  const renderMap = () => {
    return (
      <View style={map.containerDetailmap}>
        {!mapReady && <LoadingDetail />}
        <MapView
          style={map.map}
          key={forceReload}
          ref={mapRef}
          region={region}
          mapType="satellite"
          zoomEnabled={true}
          scrollEnabled={true}
          toolbarEnabled={false}
          provider={PROVIDER_GOOGLE}
          onMapReady={() => setMapReady(true)}
        >
          {polygon.length > 0 && (
            <Polygon
              coordinates={polygon}
              strokeColor="yellow"
              strokeWidth={3}
              lineCap="square"
              lineJoin="miter"
            />
          )}
        </MapView>
      </View>
    );
  };
  const renderPlants = ({ item }: any) => {
    return (
      <View style={{ marginTop: moderateScale(10) }}>
        <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
          ๐ {item.plantName}
        </Text>
      </View>
    );
  };
  const renderDetail = () => {
    return (
      <View style={ctn.ctn_detailArea}>
        <View style={ctn.ctn_bgDetailArea}>
          <ScrollView>
            <View style={{ alignItems: "center", flexDirection: "column" }}>
              <View style={{ paddingHorizontal: moderateScale(10) }}>
                {/* {Detail Area} */}
                <TouchableOpacity
                  style={[
                    activeButton === 1
                      ? oth.card_detailRai
                      : oth.card_detailRaiNon,
                  ]}
                  onPress={() => goDetail()}
                  disabled={activeButton === 1 ? true : false}
                >
                  {iconFileNon()}
                </TouchableOpacity>

                {/* {Location Area} */}
                <TouchableOpacity
                  style={[
                    activeButton === 2
                      ? oth.card_detailRai
                      : oth.card_detailRaiNon,
                  ]}
                  onPress={() => goLocation()}
                  disabled={activeButton === 2 ? true : false}
                >
                  {iconMapArea()}
                </TouchableOpacity>

                {/* {Planst Detail} */}
                <TouchableOpacity
                  style={[
                    activeButton === 3
                      ? oth.card_detailRai
                      : oth.card_detailRaiNon,
                  ]}
                  onPress={() => goPlanst()}
                  disabled={activeButton === 3 ? true : false}
                >
                  {iconPlans()}
                </TouchableOpacity>

                {/* {Review user} */}
                <TouchableOpacity
                  style={[
                    activeButton === 4
                      ? oth.card_detailRai
                      : oth.card_detailRaiNon,
                  ]}
                  onPress={() => goReview()}
                  disabled={activeButton === 4 ? true : false}
                >
                  {iconCommentArea()}
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ margin: "50%" }} />
          </ScrollView>
        </View>

        {/* {Detail} */}
        <View style={ctn.ctn_detailSelect}>
          <View style={{ paddingHorizontal: moderateScale(20) }}>
            <ScrollView showsVerticalScrollIndicator={false}>
              {/* {Detail Area} */}
              {activeButton === 1 && (
                <View style={{ marginTop: moderateScale(20) }}>
                  <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                    {t("Area_details")} {isFarmName}
                  </Text>
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {description || "-"}
                  </Text>
                </View>
              )}
              {/* {Location Area} */}
              {activeButton === 2 && (
                <View style={{ marginTop: moderateScale(20) }}>
                  <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                    {t("location")} {isFarmName}
                  </Text>
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {location || "-"}
                  </Text>
                  {renderMap()}
                </View>
              )}
              {/* {Planst Detail} */}
              {activeButton === 3 && (
                <>
                  <View style={{ marginTop: moderateScale(20) }}>
                    <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                      {t("Plants_suitable_for_cultivation")}
                    </Text>
                    {flasListPlants()}
                  </View>
                </>
              )}
              {/* {Review user} */}
              {activeButton === 4 && (
                <View style={{ marginTop: moderateScale(20) }}>
                  <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                    {t("Reviews_from_growers")}
                  </Text>
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {feedbacks || "-"}
                  </Text>
                </View>
              )}
              <View style={{ margin: "50%" }} />
            </ScrollView>
          </View>
        </View>
      </View>
    );
  };
  const renderBottom = () => {
    return (
      <View style={ctn.ctn_bottomArea}>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <TouchableOpacity onPress={() => onPolt()}>
            <View style={[btn.btn_selectArea]}>{iconSelectPlant()}</View>
          </TouchableOpacity>
          <View style={{ margin: moderateScale(5) }} />

          <TouchableOpacity
            onPress={() => (isChoosePolt === null ? null : goNext())}
          >
            <View
              style={[
                isChoosePolt === null
                  ? btn.btn_nextDetailAreaNon
                  : btn.btn_nextDetailArea,
              ]}
            >
              <View
                style={{ alignItems: "center", marginTop: moderateScale(10) }}
              >
                <View style={{ flexDirection: "row" }}>
                  <Text
                    style={[
                      fonstStyle.f14_bold,
                      {
                        color:
                          isChoosePolt === null
                            ? FonstColor.Tc_606060
                            : FonstColor.Tc_FFFFFF,
                      },
                    ]}
                  >
                    {t("next")}
                  </Text>
                  <View style={{ margin: moderateScale(5) }} />
                  {isChoosePolt === null ? null : iconNextDetail()}
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderPoltArea = ({ item, index }: any) => {
    const isSelected = item.numberOfSequence === selectedPlot;
    return (
      <>
        <TouchableOpacity
          style={[
            isSelected
              ? isStatus === "ว่าง"
                ? oth.card_poltAreaSelect
                : oth.card_poltAreaNon
              : oth.card_poltArea,
          ]}
          onPress={() => onSelectPolt(item)}
        >
          <Text
            style={[
              fonstStyle.f12_bold,
              {
                color: isSelected ? FonstColor.Tc_FFFFFF : FonstColor.Tc_606060,
              },
            ]}
          >
            {item.numberOfSequence}
            {/* {index + 1} */}
          </Text>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const renderModalPolt = () => {
    //ตัดทศนิยม ตารางเมตร
    const squareMeters: number = isSquareMeters;
    const roundedSquareMeters: number = Math.round(squareMeters);

    return (
      <Modal animationType="fade" transparent={true} visible={isShowVeget}>
        <View style={mod.mod_center}>
          <View style={mod.mod_ctnMap}>
            {/* {Map} */}
            <View style={{ alignItems: "center" }}>
              <View style={ctn.ctn_mapVetGet}>
                {!mapPlotReady && <LoadingDetail />} 
                <MapView
                  // ref={mapRef}
                  style={map.mapPlot}
                  key={forcePlotReload}
                  region={regionPlot}
                  mapType="satellite"
                  zoomEnabled={true}
                  scrollEnabled={true}
                  toolbarEnabled={false}
                  provider={PROVIDER_GOOGLE}
                  onMapReady={() => {
                    setMapPlotReady(true);
                  }}
                >
                  {mapPlotReady && polygonPlot.length > 0 && (
                    <Polygon
                      coordinates={polygonPlot}
                      strokeColor="yellow"
                      strokeWidth={3}
                      lineCap="square"
                      lineJoin="miter"
                    />
                  )}
                </MapView>
                {/* {Status} */}
                <View style={ctn.ctn_Status}>
                  <View
                    style={[
                      isStatus === "ว่าง"
                        ? oth.card_status
                        : oth.card_statusNon,
                    ]}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <Text
                        style={[fonstStyle.f12_bold, txt.txt_status]}
                        numberOfLines={1}
                      >
                        {isNamePolt}
                      </Text>
                      <View style={{ flexDirection: "row" }}>
                        <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                          {t("status")}{" "}
                        </Text>
                        <Text
                          style={[
                            fonstStyle.f12_bold,
                            {
                              color:
                                isStatus === "ว่าง"
                                  ? FonstColor.Tc_C0D576
                                  : FonstColor.Tc_FF9900,
                            },
                          ]}
                        >
                          {isStatus}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>

              {/* {ListNumber} */}
              {flasListPolt()}
            </View>

            {/* {Detail} */}
            <ScrollView
              style={{
                height: "20%",
                borderRadius: 20,
                backgroundColor: BgColor.Bg_F4F4F4,
                paddingHorizontal: 10,
              }}
            >
              <View style={{ marginTop: moderateScale(10) }}>
                <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                  {t("Area_size")}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {/* {isSquareMeters}  */}
                  {roundedSquareMeters} {t("Square_Meters")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />

                <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                  {t("Suitable_plants")}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                <FlatList
                  data={Object.values(detailRecmomended)}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item }: any) => (
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {item}
                    </Text>
                  )}
                />

                <View style={{ margin: moderateScale(5) }} />
                <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                  {t("Soil_type_of_soil")}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                <FlatList
                  data={Object.values(detailSoil)}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item }: any) => (
                    <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                      {item}
                    </Text>
                  )}
                />
              </View>
            </ScrollView>

            {/* {Select} */}
            <View style={{ alignItems: "flex-end", top: moderateScale(10) }}>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity
                  style={[oth.card_cancleVetGet]}
                  onPress={() => onClosePolt()}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_green]}>
                    {t("replies_cancle")}
                  </Text>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity
                  style={[
                    isStatus === "ว่าง"
                      ? oth.card_celectVetGet
                      : oth.card_celectVetGetNon,
                  ]}
                  disabled={isStatus === "ว่าง" ? false : true}
                  onPress={() => onItemPolt()}
                >
                  <Text
                    style={[
                      fonstStyle.f12_bold,
                      {
                        color:
                          isStatus === "ว่าง"
                            ? FonstColor.Tc_FFFFFF
                            : FonstColor.Tc_606060,
                      },
                    ]}
                  >
                    {t("confirm")}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  if (!region || !regionPlot) {
    return <LoadingFarm />;
  }

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continueMain}>
          {/* {rendergoBack()} */}
          {renderGallery()}
          {renderDetail()}
          {renderBottom()}
          {renderModalPolt()}
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continueMain, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          {/* {rendergoBack()} */}
          {/* {renderMap()} */}
          {renderDetail()}
          {renderBottom()}
          {renderModalPolt()}
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle={"dark-content"} hidden={false} />
      <Default_Bar onBack={goBack} title={isFarmName}/>
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
