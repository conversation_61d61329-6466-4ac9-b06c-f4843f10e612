import {
  View,
  Text,
  Image,
  Modal,
  Platform,
  StatusBar,
  ScrollView,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  AppState,
  FlatList,
} from "react-native";
import moment from "moment";
import { Badge } from "@rneui/themed";
import FastImage from "react-native-fast-image";
import React, { useEffect, useState, useRef } from "react";
import { useIsFocused } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import { useOrientation } from "../../hooks/useOrientation";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { FonstColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconPlus,
  iconNext,
  iconBanking,
  iconNextArea,
  iconNoPlants,
  iconBookMark,
  iconBookMarkOn,
  iconDeleteArea,
  iconDeleteAlert,
  iconBankingChang,
} from "../../assets/svg/svg_other";
//Components
import { decrypt } from "../../action/encryption";
import Loading from "../../components/loading/loading";
import Null_Bar from "../../components/appBar/null_Bar";
import PayMentAfter from "../../components/payMent/payMentAfter";
import PayMentRenew from "../../components/payMent/payMentRenew";
//Translation
import { useTranslation } from "../i18n";
//Api
import {
  seedApi,
  getPayMentApi,
  deleteRestFarm,
  getFarmDasdoard,
} from "../../action/Mefarm_Farm_API";
import { getFarmbadge } from "../../action/Mefarm_Admin_API";
import { getProfileApi } from "../../action/Mefarm_Identity_API";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useDispatch, useSelector } from "react-redux";
import { setDocSeedList } from "../../Redux_Store/action";
import Images from "../../utils/imageManager";
import { postAdminFarmPlant } from "../../action/Mefarm_Admin_API";

export default function Plusarea({ navigation, route }: any) {
  const orientation = useOrientation();
  const dispatch = useDispatch();
  const docSeedlist = useSelector((state: any) => state.docSeedlist);

  const { t } = useTranslation();
  const isFocused = useIsFocused();
  //True & False
  const [checked, setChecked] = useState<boolean>(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  //Array
  const [docRewnew, setDocRenew] = useState<any>([]);
  const [docMyFarm, setDocMyFarm] = useState<any>([]);
  const [userRoles, setUserRoles] = useState<any>([]);
  // const [docSeedlist, setDocSeedList] = useState<any[]>([]);
  const [docAfterPay, setDocAfterPay] = useState<any>([]);
  const [docRenewalList, setRenewalList] = useState<any>([]);
  const [docMyFarmList, setDocMyFarmList] = useState<any>([]);
  const [docReserVation, setDocReserVation] = useState<any>([]);
  const [paymentMethods, setPaymentMethods] = useState<any>([]);
  //OBJ
  const [paymentData, setPaymentData] = useState<any>({});
  //String
  const [typePay, setTpePay] = useState<String>("");
  const [typeRenew, setTypeRenew] = useState<String>("");
  const [reservationId, setReservationId] = useState<String>("");
  //Number
  const [allBange, setAllBange] = useState<number>(0);
  const [activeButton, setActiveButton] = useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [bookmarkedItem, setBookmarkedItem] = useState<any>(null);
  const actionSheetPayRef = useRef<ActionSheetRef>(null);
  const actionSheetPayRenewRef = useRef<ActionSheetRef>(null);
  const openActionSheetPay = () => actionSheetPayRef.current?.show();
  const closeActionSheetPay = () => actionSheetPayRef.current?.hide();
  const openActionSheetPayRenew = () => actionSheetPayRenewRef.current?.show();
  const closeActionSheetPayRenew = () => actionSheetPayRenewRef.current?.hide();
  //Route
  const params = route.params || "";

  useEffect(() => {
    if (params.screenType === "Bookmarker") {
      const checkBookmarkAndNavigate = async () => {
        const bookmarkState = await AsyncStorage.getItem("bookMarkState");
        const bookmark = await AsyncStorage.getItem("bookMark");
        if (bookmarkState && JSON.parse(bookmarkState) && bookmark) {
          const bookmarkedItem = JSON.parse(bookmark);
          // ไปหน้า ManageFarmUser พร้อมส่งข้อมูล
          navigation.navigate("ManageFarmUser", {
            docDetailMyfarm: bookmarkedItem,
          });
        }
      };
      checkBookmarkAndNavigate();
    }
  }, [params.screenType]);
  useEffect(() => {
    // โหลดข้อมูลทันทีเมื่อ component mount (เปิดแอปใหม่)
    // callSeed();
    callFarmDasboard();
    callMyProfile();
    callBage();

    // โหลดข้อมูลซ้ำเมื่อแอปกลับมา foreground
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (nextAppState === "active") {
        callSeed();
        callFarmDasboard();
        callMyProfile();
        callBage();
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);
  useEffect(() => {
    callSeed();
    callFarmDasboard();
    callMyProfile();
    callBage();
  }, [isFocused]);
  useEffect(() => {
    const callPayMent = async () => {
      try {
        setLoadIng(true);
        const response = await getPayMentApi();
        const payMentData = response.model || "";
        // console.log(JSON.stringify(payMentData, null, 2));
        setPaymentMethods(payMentData);
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    };
    callPayMent();
  }, []);
  const callMyProfile = async () => {
    try {
      setLoadIng(true);
      const response = await getProfileApi();

      // ตรวจสอบ userRoles
      // console.log("Raw userRoles:", response.model.userRoles);
      let userRoles = response.model.userRoles;
      // console.log(userRoles);

      if (typeof userRoles === "string") {
        try {
          // Decrypt the userRoles token
          userRoles = decrypt(userRoles);

          // Parse the decrypted string into an object
          userRoles = JSON.parse(userRoles);
        } catch (decryptError) {
          console.error("Error during decryption or parsing:", decryptError);
          throw new Error("Failed to decrypt or parse userRoles");
        }
      }

      // Successfully set the userRoles state after decryption and parsing
      setUserRoles(userRoles);
    } catch (error) {
      console.log("Error:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const callSeed = async () => {
    try {
      setLoadIng(true);
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await seedApi(req);
      const seedData = response.model || "";
      setDocSeedList(seedData);
      dispatch(setDocSeedList(seedData));
      // console.log(JSON.stringify(seedData, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callAdminPlant = async () => {
    try {
      setLoadIng(true);
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await postAdminFarmPlant(req);
      const seedData = response.model || "";
      // setDocSeedList(seedData);
      // dispatch(setDocSeedList(seedData));
      console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callFarmDasboard = async () => {
    try {
      setLoadIng(true);
      const response = await getFarmDasdoard();
      const docFarm = response.model || "";
      const listMyFarm = docFarm.myFarmList || "";
      const docReservationList = docFarm.myReservationList || "";
      const myRenewalList = docFarm.myRenewalList || "";
      setDocMyFarm(docFarm);
      setDocMyFarmList(listMyFarm);
      setDocReserVation(docReservationList);
      setRenewalList(myRenewalList);
      // console.log(JSON.stringify(docFarm, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callBage = async () => {
    try {
      const res = await getFarmbadge();

      const data = res.model || "";
      // console.log(data);
      const manageCount = data.invoiceManageCount || 0;
      const rentCount = data.invoiceRentCount || 0;
      const seedCount = data.invoiceSeedCount || 0;
      const requestCount = data.manageRequestCount || 0;

      const allCount = manageCount + rentCount + seedCount + requestCount;
      // console.log(">>>", allCount);
      setAllBange(allCount);
    } catch (error) {
      console.log(error);
    }
  };
  const openModelDelete = (item: any) => {
    // console.log(item);
    setReservationId(item.id);
    setModalDelete(true);
  };
  const reload = () => {
    callSeed();
    callFarmDasboard();
  };
  const selectPayMent = (item: any, index: number) => {
    // console.log(index);
    setSelectedIndex(index);
    setActiveButton(index);
    setPaymentData(item);
  };
  useEffect(() => {
    const loadBookmark = async () => {
      try {
        const bookmarkState = await AsyncStorage.getItem("bookMarkState");
        const bookmark = await AsyncStorage.getItem("bookMark");
        // console.log("bookmarkState", bookmarkState);
        // console.log("bookmark", bookmark);

        if (bookmarkState) {
          setChecked(JSON.parse(bookmarkState));
        }

        if (bookmark) {
          setBookmarkedItem(JSON.parse(bookmark));
        }
      } catch (error) {
        console.error("Error loading bookmark state:", error);
      }
    };

    loadBookmark();
  }, []);

  const onBookMark = async (item: any) => {
    try {
      // console.log("onBookMark called with item:", item.id);
      // console.log("Current checked state:", checked);
      const existingBookmark = await AsyncStorage.getItem("bookMark");
      const existingItem = existingBookmark
        ? JSON.parse(existingBookmark)
        : null;
      // console.log("Existing bookmark:", existingItem ? existingItem.id : "none");
      const isSameItem = existingItem && existingItem.id === item.id;
      console.log("Is same item?", isSameItem);

      if (checked && isSameItem) {
        // console.log("Removing bookmark");
        setChecked(false);
        setBookmarkedItem(null);

        await AsyncStorage.removeItem("bookMark");
        await AsyncStorage.setItem("bookMarkState", JSON.stringify(false));

        // console.log("After removal - checked:", false);
        // console.log("After removal - bookmarkedItem:", null);
      } else {
        // console.log("Setting new bookmark");
        setChecked(true);
        setBookmarkedItem(item);

        await AsyncStorage.removeItem("bookMark");
        await AsyncStorage.setItem("bookMark", JSON.stringify(item));
        await AsyncStorage.setItem("bookMarkState", JSON.stringify(true));

        // console.log("After setting - checked:", true);
        // console.log("After setting - bookmarkedItem:", item.id);
      }
    } catch (error) {
      console.error("Error handling bookmark:", error);
    }
  };

  //Go to
  const goArea = () => {
    navigation.navigate("SelectArea");
  };
  const goDetail = (item: any) => {
    // console.log(item);
    navigation.navigate("ManageFarmUser", { docDetailMyfarm: item });
  };
  const goDelete = async () => {
    try {
      setLoadIng(true);
      // console.log("??????", reservationId);
      const response = await deleteRestFarm(reservationId);
      // console.log("response...", response);
      callFarmDasboard();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setModalDelete(false);
    }
  };
  const goReturnPay = (typePay: any, item: any) => {
    openActionSheetPay();
    setDocAfterPay(item);
    setTpePay(typePay);
  };
  const goRenew = (typePay: any, item: any) => {
    openActionSheetPayRenew();
    setDocRenew(item);
    setTypeRenew(typePay);
  };
  const goPayRenew = () => {
    navigation.navigate("QrCodepay", {
      typeRenew: typeRenew || "",
      docRewnew: docRewnew || "",
      activeButton: activeButton,
      paymentData: paymentData,
    });
  };
  const goPayMent = () => {
    navigation.navigate("QrCodepay", {
      type: typePay || "",
      itemAfterPay: docAfterPay || "",
      activeButton: activeButton,
      paymentData: paymentData,
    });
  };
  const goPlantDetail = (item: any) => {
    navigation.navigate("PlantDetail", { docDetail: item });
  };

  //Ui
  const plusArea = () => {
    return (
      <>
        {docMyFarmList.length === 0 && docReserVation.length === 0 ? (
          <TouchableOpacity style={ctn.ctn_nonArea} onPress={() => goArea()}>
            <FastImage
              style={img.img_nonArea}
              source={Images.Threeleaves}
              resizeMode={FastImage.resizeMode.contain}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
            <View style={{ margin: moderateScale(10) }} />
            <Text
              style={[txt.txt_nonArea, fonstStyle.f14_bold, txt.txt_606060]}
            >
              {t("nonArea")}
            </Text>
            <Text
              style={[txt.txt_nonArea, fonstStyle.f14_bold, txt.txt_606060]}
            >
              {t("click_Build_Area")}
            </Text>
          </TouchableOpacity>
        ) : null}
      </>
    );
  };
  const HeaderAfterFarm = () => {
    return (
      <>
        {docReserVation.length !== 0 ? (
          <>
            <View style={ctn.ctn_txtRest}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Payment_waiting_area")}
              </Text>
            </View>
            <View style={{ margin: moderateScale(5) }} />
            <FlatList
              data={docReserVation}
              horizontal={true}
              nestedScrollEnabled={true}
              style={{
                paddingHorizontal: moderateScale(10),
                paddingVertical: moderateScale(5),
              }}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderAfterPay}
              showsHorizontalScrollIndicator={false}
            />
          </>
        ) : null}
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const HeaderRenew = () => {
    return (
      <>
        {docRenewalList.length !== 0 ? (
          <>
            <View style={ctn.ctn_txtRest}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("Renew_package")}
              </Text>
            </View>
            <View style={{ margin: moderateScale(5) }} />
            <FlatList
              data={docRenewalList}
              horizontal={true}
              nestedScrollEnabled={true}
              style={{
                paddingHorizontal: moderateScale(10),
                paddingVertical: moderateScale(5),
              }}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderRenew}
              showsHorizontalScrollIndicator={false}
            />
          </>
        ) : null}
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const HeaderMyFarm = () => {
    return (
      <>
        {docMyFarmList.length !== 0 ? (
          <>
            <View
              style={
                docMyFarmList.length !== 0 ? ctn.ctn_myFarmNon : ctn.ctn_myFarm
              }
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("my_Farm")}
              </Text>

              <View style={{ flexDirection: "row" }}>
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {t("add_plants")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <TouchableOpacity
                  style={oth.cardPludArea}
                  onPress={() => goArea()}
                >
                  {iconPlus()}
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ margin: moderateScale(5) }} />
            <FlatList
              data={docMyFarmList}
              horizontal={true}
              nestedScrollEnabled={true}
              style={{
                paddingHorizontal: moderateScale(10),
                paddingVertical: moderateScale(5),
              }}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderMyFarm}
              showsHorizontalScrollIndicator={false}
            />
          </>
        ) : null}
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const renderAfterPay = ({ item }: any) => {
    const currentTime = Date.now(); // เวลาในหน่วย บ
    const endDate = new Date(item.endDate).getTime(); // เวลาในหน่วย บ
    const remainingHours = Math.max(
      0,
      Math.floor((endDate - currentTime) / (1000 * 60 * 60))
    ); //  izarโมง (ไม่น้อยกว่า 0)
    return (
      <>
        {docReserVation.length !== 0 ? (
          <View style={[ctn.ctn_farmPackAge, { height: moderateScale(90) }]}>
            <FastImage
              style={[img.img_MyFarm, { height: moderateScale(90) }]}
              source={{ uri: item.imageUrl }}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
            <View
              style={{
                flexDirection: "column",
                padding: moderateScale(5),
                justifyContent: "center",
              }}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {item.plotName}
              </Text>
              <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                @{item.farmName}
              </Text>
              <View style={{ flexDirection: "row" }}>
                <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                  {item.cancelRemark === null ? item.status : item.cancelRemark}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                {/* ตรวจสอบว่าหมดเวลาหรือไม่ */}
                {remainingHours > 0 ? (
                  <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                    ({t("time_left")} {remainingHours} {t("hour")})
                  </Text>
                ) : (
                  <Text style={[fonstStyle.f12_bold, txt.txt_red]}>
                    ({t("payment_time_expired")})
                  </Text>
                )}
              </View>
              <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                {moment(item.startDate).format("DD/MM/YYYY")} -{" "}
                {moment(item.endDate).format("DD/MM/YYYY")}
              </Text>
            </View>

            <View style={{ padding: moderateScale(10), flexDirection: "row" }}>
              <TouchableOpacity
                style={oth.cardIconArea}
                onPress={() => goReturnPay("typePayAfter", item)}
              >
                <FastImage
                  style={img.img_MobilePay}
                  source={Images.MobilePayment}
                  resizeMode={FastImage.resizeMode.contain}
                  onLoadStart={() => setLoadIng(true)}
                  onLoadEnd={() => setLoadIng(false)}
                />
              </TouchableOpacity>
              <View style={{ margin: moderateScale(2) }} />
              <TouchableOpacity
                style={oth.cardIconArea}
                onPress={() => openModelDelete(item)}
              >
                {iconDeleteArea()}
              </TouchableOpacity>
            </View>
          </View>
        ) : null}
        {/* <View style={{ margin: moderateScale(10) }} /> */}
      </>
    );
  };
  const renderRenew = ({ item }: any) => {
    const currentTime = Date.now(); // เวลาในหน่วย บ
    const endDate = new Date(item.endDate).getTime(); // เวลาในหน่วย บ
    const remainingHours = Math.max(
      0,
      Math.floor((endDate - currentTime) / (1000 * 60 * 60))
    );
    return (
      <>
        {docRenewalList.length !== 0 ? (
          <View style={[ctn.ctn_farmPackAge, { height: moderateScale(90) }]}>
            <FastImage
              style={[img.img_MyFarm, { height: moderateScale(90) }]}
              source={{ uri: item.imageUrl }}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
            <View
              style={{
                flexDirection: "column",
                padding: moderateScale(5),
                justifyContent: "center",
              }}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {item.plotName}
              </Text>
              <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                @{item.farmName}
              </Text>
              <View style={{ flexDirection: "row" }}>
                <Text
                  style={[
                    fonstStyle.f12_bold,
                    {
                      color:
                        item.status === "รอยืนยัน"
                          ? FonstColor.Tc_4FA5F4
                          : FonstColor.Tc_FFD000,
                    },
                  ]}
                >
                  {item.cancelRemark === null ? item.status : item.cancelRemark}
                </Text>
                <View style={{ margin: moderateScale(2) }} />
                {/* ตรวจสอบว่าหมดเวลาหรือไม่ */}

                <Text
                  style={[
                    fonstStyle.f12_bold,
                    remainingHours > 0 && item.status != "รอยืนยัน"
                      ? txt.txt_yellow
                      : txt.txt_directed,
                    {
                      textDecorationLine:
                        remainingHours > 0 && item.status != "รอยืนยัน"
                          ? "none"
                          : "line-through",
                    },
                  ]}
                >
                  ({t("time_left")} {remainingHours} {t("hour")})
                </Text>
              </View>
              {/* {item.status ? ( */}
              <Text
                style={[
                  fonstStyle.f12_bold,
                  remainingHours > 0 && item.status != "รอยืนยัน"
                    ? txt.txt_yellow
                    : txt.txt_directed,
                  {
                    textDecorationLine:
                      remainingHours > 0 && item.status != "รอยืนยัน"
                        ? "none"
                        : "line-through",
                  },
                ]}
              >
                {moment(item.startDate).format("DD/MM/YYYY")} -{" "}
                {moment(item.endDate).format("DD/MM/YYYY")}
              </Text>
              {/* ) : null} */}
            </View>

            <View style={{ padding: moderateScale(10), flexDirection: "row" }}>
              {item.status === "รอยืนยัน" ? (
                <FastImage
                  style={img.img_hourGlass}
                  source={Images.gift_blue}
                  resizeMode={FastImage.resizeMode.contain}
                  onLoadStart={() => setLoadIng(true)}
                  onLoadEnd={() => setLoadIng(false)}
                />
              ) : (
                <>
                  {remainingHours > 0 ? (
                    <TouchableOpacity
                      style={oth.cardIconArea}
                      onPress={() =>
                        remainingHours > 0
                          ? goRenew("typeRenew", item)
                          : undefined
                      }
                    >
                      <FastImage
                        style={img.img_giftStutes}
                        source={Images.gift_yellow}
                        resizeMode={FastImage.resizeMode.contain}
                        onLoadStart={() => setLoadIng(true)}
                        onLoadEnd={() => setLoadIng(false)}
                      />
                    </TouchableOpacity>
                  ) : (
                    <FastImage
                      style={img.img_giftStutes}
                      source={Images.gift_yellow}
                      resizeMode={FastImage.resizeMode.contain}
                      onLoadStart={() => setLoadIng(true)}
                      onLoadEnd={() => setLoadIng(false)}
                    />
                  )}
                </>
              )}
            </View>
          </View>
        ) : null}
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const renderMyFarm = ({ item }: any) => {
    // Check if this item is the bookmarked item
    const isBookmarked = bookmarkedItem && bookmarkedItem.id === item.id;

    return (
      <>
        <View
          style={
            item.isAwaitingRenewal === true
              ? ctn.ctn_farmPackAgeNon
              : ctn.ctn_farmPackAge
          }
        >
          {item.isSuccess && (
            <TouchableOpacity
              style={ctn.ctn_bookMark}
              onPress={() => onBookMark(item)}
            >
              {isBookmarked ? iconBookMarkOn() : iconBookMark()}
            </TouchableOpacity>
          )}

          <FastImage
            style={img.img_MyFarm}
            source={{ uri: item.imageUrl }}
            resizeMode={FastImage.resizeMode.cover}
            onLoadStart={() => setLoadIng(true)}
            onLoadEnd={() => setLoadIng(false)}
          />
          <TouchableOpacity
            disabled={item.isAwaitingRenewal === true}
            onPress={() => (item.isSuccess ? goDetail(item) : undefined)}
            style={ctn.ctn_columnArea}
          >
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.plotName}
            </Text>
            <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
              @{item.farmName}
            </Text>
            <Text
              style={[
                fonstStyle.f12_bold,
                {
                  color: item.isSuccess
                    ? FonstColor.Tc_84B8A2
                    : FonstColor.Tc_4FA5F4,
                },
              ]}
            >
              {item.status}
            </Text>
          </TouchableOpacity>

          <View style={{ padding: moderateScale(15) }}>
            {item.isSuccess ? (
              <TouchableOpacity
                disabled={item.isAwaitingRenewal === true}
                style={oth.cardIconArea}
                onPress={() => goDetail(item)}
              >
                {iconNextArea()}
              </TouchableOpacity>
            ) : (
              <Image
                style={img.img_hourGlass}
                source={Images.Hourglass}
                resizeMode="contain"
              />
            )}
          </View>
        </View>
        <View style={{ margin: moderateScale(5) }} />
      </>
    );
  };
  const renderMyPlans = ({ item }: any) => {
    return (
      <TouchableOpacity
        style={{ padding: moderateScale(20), alignItems: "center" }}
        onPress={() => goPlantDetail(item)}
      >
        <FastImage
          style={img.img_Myseeds}
          source={{ uri: item.imageUrl }}
          resizeMode={FastImage.resizeMode.contain}
          onLoadStart={() => setLoadIng(true)}
          onLoadEnd={() => setLoadIng(false)}
        />
        <View style={{ alignItems: "center", marginTop: moderateScale(10) }}>
          <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
            {item.seedName || "-"}
          </Text>
          <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
            {item.typeName || "-"}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  const seedMyFarm = () => {
    return (
      <>
        {docMyFarmList.length !== 0 ? (
          <>
            <View style={ctn.ctn_Seeds}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("my_seeds")}
              </Text>
            </View>

            <FlatList
              data={docSeedlist}
              horizontal={true}
              nestedScrollEnabled={true}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderMyPlans}
              showsHorizontalScrollIndicator={false}
              ListFooterComponent={
                <View style={{ marginTop: moderateScale(10) }}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("PlantList")}
                    style={{ padding: moderateScale(15) }}
                  >
                    {iconNext()}
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[fonstStyle.f12_light, txt.txt_horizontalPlus]}
                    >
                      {t("see_all")}
                    </Text>
                  </TouchableOpacity>
                </View>
              }
            />
          </>
        ) : null}
      </>
    );
  };
  const renderModal = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={modalDelete}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconDeleteAlert()}</View>
            </View>
            <View style={{ bottom: "20%" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("cancel_the_order")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_modFlase]}>
                {t("Payment_or_not")}
              </Text>
            </View>
            <View style={{ flexDirection: "row", bottom: 10 }}>
              <TouchableOpacity
                style={mod.mod_Cancle}
                onPress={() => setModalDelete(false)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                  {t("replies_cancle")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />
              <TouchableOpacity style={mod.mod_Agee} onPress={() => goDelete()}>
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const actionSheetPay = () => (
    <ActionSheet ref={actionSheetPayRef} gestureEnabled>
      <View style={mod.mod_View}>
        <Text
          style={[fonstStyle.f14_bold, txt.txt_606060, { textAlign: "center" }]}
        >
          {t("Payment_channels")}
        </Text>
        <View style={{ margin: moderateScale(10) }} />
        <PayMentAfter
          paymentMethods={paymentMethods}
          selectedIndex={selectedIndex}
          selectPayMent={selectPayMent}
          btn={btn}
          iconBanking={iconBanking}
          iconBankingChang={iconBankingChang}
          moderateScale={moderateScale}
        />
        <View style={{ margin: moderateScale(10) }} />
        <View style={btn.btn_ctnPayment}>
          <TouchableOpacity
            style={mod.mod_Cancle}
            onPress={closeActionSheetPay}
          >
            <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
              {t("replies_cancle")}
            </Text>
          </TouchableOpacity>
          <View style={{ margin: moderateScale(5) }} />
          <TouchableOpacity
            disabled={selectedIndex === null}
            style={selectedIndex != null ? mod.mod_Agee : mod.mod_AgeeNon}
            onPress={() => {
              if (selectedIndex != null) {
                closeActionSheetPay();
                goPayMent();
              }
            }}
          >
            <Text
              style={[
                fonstStyle.f12_bold,
                selectedIndex != null ? txt.txt_white : txt.txt_606060,
              ]}
            >
              {t("confirm")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ActionSheet>
  );
  const actionSheetPayRenew = () => (
    <ActionSheet ref={actionSheetPayRenewRef} gestureEnabled>
      <View style={mod.mod_View}>
        <Text
          style={[fonstStyle.f14_bold, txt.txt_606060, { textAlign: "center" }]}
        >
          {t("Payment_channels")}
        </Text>
        <View style={{ margin: moderateScale(10) }} />
        <PayMentRenew
          paymentMethods={paymentMethods}
          selectedIndex={selectedIndex}
          selectPayMent={selectPayMent}
          btn={btn}
          iconBanking={iconBanking}
          iconBankingChang={iconBankingChang}
          moderateScale={moderateScale}
        />
        <View style={{ margin: moderateScale(10) }} />
        <View style={btn.btn_ctnPayment}>
          <TouchableOpacity
            style={mod.mod_Cancle}
            onPress={closeActionSheetPayRenew}
          >
            <Text style={[fonstStyle.f12_bold, txt.txt_yellow]}>
              {t("replies_cancle")}
            </Text>
          </TouchableOpacity>
          <View style={{ margin: moderateScale(5) }} />
          <TouchableOpacity
            disabled={selectedIndex === null}
            style={selectedIndex != null ? mod.mod_AgeeRenew : mod.mod_AgeeNon}
            onPress={() => {
              if (selectedIndex != null) {
                closeActionSheetPayRenew();
                goPayRenew();
              }
            }}
          >
            <Text
              style={[
                fonstStyle.f12_bold,
                selectedIndex != null ? txt.txt_white : txt.txt_606060,
              ]}
            >
              {t("agree")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ActionSheet>
  );
  const bottonAdmin = () => {
    const shouldShowButton =
      userRoles.includes("eWgjfJ/Y2Sutghg7RC9v4w==") ||
      userRoles.includes("kP0Dzc2FoP0elAkG46yGJw==");

    return (
      <>
        {shouldShowButton && (
          <View
            style={{
              bottom:
                orientation === "portrait"
                  ? Platform.OS === "ios"
                    ? "12%"
                    : "9%"
                  : "15%",
              alignItems: "flex-end",
              right: 10,
            }}
          >
            <TouchableOpacity
              style={btn.btn_bottomMenuAdmin}
              onPress={() => navigation.navigate("AdminPage")}
            >
              {allBange != 0 && (
                <Badge
                  value={allBange}
                  status="error"
                  badgeStyle={oth.bageNotiAdmin}
                />
              )}
              <Image
                style={img.img_iconEllipse}
                source={Images.Ellipse}
                resizeMode="cover"
              />
              <Image
                style={{ width: 46, height: 46 }}
                source={Images.admin}
                resizeMode="cover"
              />
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        {isLoadIng ? <Loading /> : null}
        <Null_Bar />
        <SafeAreaView />
        <ScrollView
          style={ctn.continueMain}
          refreshControl={
            <RefreshControl refreshing={isLoadIng} onRefresh={reload} />
          }
        >
          {plusArea()}
          {HeaderAfterFarm()}
          {HeaderRenew()}
          {HeaderMyFarm()}
          {/* {seedMyFarm()} */}
          {renderModal()}
          {actionSheetPay()}
          {actionSheetPayRenew()}
          {/* {modalPayrenew()} */}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
        {bottonAdmin()}
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        {isLoadIng ? <Loading /> : null}
        <Null_Bar />
        <SafeAreaView />
        <ScrollView
          style={ctn.continueMain}
          refreshControl={
            <RefreshControl refreshing={isLoadIng} onRefresh={reload} />
          }
        >
          {plusArea()}
          {HeaderAfterFarm()}
          {HeaderRenew()}
          {HeaderMyFarm()}
          {/* {seedMyFarm()} */}
          {renderModal()}
          {actionSheetPay()}
          {actionSheetPayRenew()}
          {/* {modalPayrenew()} */}
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
        {bottonAdmin()}
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
