import {
  Text,
  View,
  <PERSON>ert,
  Switch,
  FlatList,
  StatusBar,
  Platform,
  TextInput,
  ScrollView,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  KeyboardAvoidingView,
} from "react-native";
import "moment/locale/th";
import moment from "moment";
import RNFS from "react-native-fs";
import Share from "react-native-share";
import Video from "react-native-video";
import FastImage from "react-native-fast-image";
import DatePicker from "react-native-date-picker";
import { HubConnection } from "@microsoft/signalr";
import { useIsFocused } from "@react-navigation/native";
import { launchCamera } from "react-native-image-picker";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import React, { useState, useEffect, useRef } from "react";
import ImageCropPicker from "react-native-image-crop-picker";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
//StyleSheet
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconNon,
  editSvg,
  iconUser,
  iconEmail,
  iconPhone,
  iconLogOut,
  iconSetting,
  iconBirdDay,
  iconPinDetail,
  iconSaveDetail,
  iconCameraEdit,
  iconGalleryEdit,
  iconGalleryModal,
} from "../../assets/svg/svg_other";
//Translation
import { useTranslation } from "../i18n";
//Api
import {
  upDateCover,
  getAllImgApi,
  upDateProfile,
  saveProfileApi,
  getAllVideoApi,
} from "../../action/Mefarm_Identity_API";
import { decrypt, encrypt } from "../../action/encryption";
import { putPublic, postFramPublic } from "../../action/Mefarm_Farm_API";
import { shareSocial, deletePostApi } from "../../action/Mefarm_Social_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setVisitList,
  setDocAllImage,
  setDocAllVideo,
  updatePostLikes,
  setDocListProfile,
} from "../../Redux_Store/action";
//commponents
import PostItem from "../../components/PostItem";
import { logout } from "../../utils/authManager";
import { useAuthTokens } from "../../hooks/useAuthTokens";
import { useProfileData } from "../../hooks/useProfileData";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import { setupNetworkListener } from "../../utils/networkManager";
import { setupSignalRConnection } from "../../utils/signalRManager";
import { useFarmDashboard } from "../../hooks/useFarmDashboard";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import {
  ModalTrue,
  ModalFalse,
  ModalCover,
  ModalLogOut,
  ModalFullImg,
  ModalProfile,
  ModalPermission,
} from "../../components/modal/modal";

export default function ProfileUser({ navigation, route }: any) {
  // ย้าย hooks �ん้งหมดมาไว้ด้านบน�ん
  const orientation = useOrientation();
  const { accessToken, userId } = useAuthTokens();
  const isFocused = useIsFocused();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // Custom hooks
  const { isLoading: isProfileLoading, fetchProfileData } = useProfileData();
  const { isLoading: isFarmLoading, fetchFarmDashboard } = useFarmDashboard();
  
  // Redux selectors
  const docListProfile = useSelector((state: any) => state.docListProfile);
  const followCounter = useSelector((state: any) => state.followCounter);
  const docAllImage = useSelector((state: any) => state.docAllImage) || [];
  const docAllVideo = useSelector((state: any) => state.docAllVideo) || [];
  const docMyFarmList = useSelector((state: any) => state.docMyFarmList) || [];

  // State declarations
  const [isMyName, setMyName] = useState<string>("");
  const [isMyEmail, setMyEmail] = useState<string>("");
  const [isMyPhone, setMyPhone] = useState<string>("");
  const [isMyUserId, setMyUserId] = useState<string>("");
  const [typeAction, setTypeAction] = useState<string>("");
  const [isMyAddress, setMyAddress] = useState<string>("");
  const [isMyBirthDay, setMyBirthDay] = useState<string>("");
  //Array
  const [imageFull, setImageFull] = useState<any>([]);
  //True & False
  const [isMuted, setIsMuted] = useState(true);
  const [isModalTrue, setModalTrue] = useState(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isModalProfile, setModalProfile] = useState<boolean>(false);
  const [isModalError, setModalError] = useState<boolean>(false);
  const [isModalCover, setModalCover] = useState<boolean>(false);
  const [isModalLogOut, setModalLogOut] = useState<boolean>(false);
  const [isModalPermiss, setModalPermiss] = useState<boolean>(false);
  const [isEditProfile, setEditProfile] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  const [openDatePicker, setOpenDatePicker] = useState<boolean>(false);
  const [isLoadLikes, setLoadLikes] = useState<{ [key: number]: boolean }>({});
  const [isLoadShare, setLoadShare] = useState<{ [key: number]: boolean }>({});
  //Number
  const [pageSize, setPageSize] = useState<number>(10);
  const [activeButton, setActiveButton] = useState<any>(5);
  //Null
  const connectionRef = useRef<any>(null);
  const [imgUriShow, setImgUriShow] = useState<any>(null);
  const [coverUri, setCoverUri] = useState<any | null>(null);
  const [imageUri, setImageUri] = useState<any | null>(null);
  const [coverUriShow, setcoverUriShow] = useState<any>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [coverName, setCoverName] = useState<string | null>(null);
  const [coverType, setCoverType] = useState<string | null>(null);
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [connection, setConnection] = useState<null | HubConnection>(null);

  const [isShowFullText, setIsShowFullText] = useState<boolean[]>(
    Array.isArray(docListProfile) && docListProfile.length > 0
      ? docListProfile.map(() => false)
      : []
  );
  const [imageDimensions, setImageDimensions] = useState<any>(null);
  //Date Picker
  const dateChange = new Date();
  //Number
  const NUM_OF_LINES = 5;
  const [enabledSwitches, setEnabledSwitches] = useState<{
    [key: string]: boolean;
  }>({});
  const scrollRef = useRef<any>(null);
  const actionSheetRef = useRef<ActionSheetRef>(null);
  const scrollToTop = () => {
    scrollRef.current?.scrollTo({ y: 0, animated: true });
  };
  useEffect(() => {
    const unsubscribe = navigation.addListener("tabPress", (e: any) => {
      scrollToTop();
    });

    return unsubscribe;
  }, [navigation]);
  useFocusEffect(
    React.useCallback(() => {
      callMyProfile();
      callAllImg();
      callAllVideo();
      callFarmDasboard();
      requestCameraPermission();
    }, [dispatch])
  );
  useEffect(() => {
  let signalRCleanup: (() => void) | null = null;

  const initConnection = async () => {
    try {
      const result = await setupSignalRConnection(
        dispatch,
        (newConnection) => {
          setConnection(newConnection);
          connectionRef.current = newConnection;
          joinRoom(newConnection); // ส่ง connection ์เชื่อมต่อสำเร็จเข้าไป
        },
        { page: "profile", forceReconnect: true } // เ่่ม forceReconnect
      );
      signalRCleanup = result.cleanup;
    } catch (error) {
      console.error("Error setting up SignalR connection:", error);
    }
  };

  if (isFocused) {
    initConnection();
  }

  return () => {
    if (signalRCleanup) {
      console.log("Cleaning up SignalR connection on profileUser unmount");
      signalRCleanup();
    }
  };
}, [isFocused, dispatch, userId, accessToken, pageSize]);

const joinRoom = async (conn?: HubConnection) => {
  const activeConnection = conn || connectionRef.current;
  if (!activeConnection || activeConnection.state !== "Connected") return;

  const joinData = {
    roomId: userId,
    followerUserId: userId,
    accessToken: accessToken,
    followingUserId: userId,
    isDescending: true,
    firstPageSize: pageSize,
    sendType: "normal",
  };

  try {
    await activeConnection.invoke("JoinRoom", joinData);
  } catch (err) {
    console.error("Error invoking JoinRoom:", err);
  }
};
  useEffect(() => {
    const unsubscribe = setupNetworkListener(
      isFocused,
      connection,
      joinRoom,
      navigation
    );

    return () => {
      unsubscribe();
      // console.log("Network listener cleaned up.");
    };
  }, [isFocused, connection]);
  const callFarmDasboard = async () => {
    try {
      setLoadIng(true);
      await fetchFarmDashboard();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callMyProfile = async () => {
    try {
      setLoadIng(true);
      const result = await fetchProfileData();

      if (result.success && result.data) {
        const {
          userId,
          firstName,
          dateOfBirth,
          mobileNo,
          email,
          address,
          profileImageUrl,
          coverImageUrl,
        } = result.data;

        setMyUserId(userId);
        setMyEmail(email);
        setMyName(firstName);
        setMyPhone(mobileNo);
        setMyAddress(address);
        setImgUriShow(profileImageUrl);
        setcoverUriShow(coverImageUrl);
        setMyBirthDay(dateOfBirth);
      } else {
        console.log("error");
      }
    } catch (error) {
      console.log("err?????", error);
      setEditProfile(false);
    } finally {
      setLoadIng(false);
    }
  };
  const callAllImg = async () => {
    try {
      setLoadIng(true);
      const newDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      const newPageSize = pageSize;
      const response = await getAllImgApi(newDate, newPageSize);
      const imgReq = response.model || "";
      // console.log(JSON.stringify(imgReq, null, 2));
      dispatch(setDocAllImage(imgReq));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callAllVideo = async () => {
    try {
      setLoadIng(true);
      const newDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      const newPageSize = pageSize;
      const response = await getAllVideoApi(newDate, newPageSize);
      const video = response.model || "";
      dispatch(setDocAllVideo(video));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callVisit = async () => {
    try {
      setLoadPosts(true);
      const req = {
        searchText: "",
        pageSize: pageSize,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const res = await postFramPublic(req);
      const data = res.model || "";
      // console.log(JSON.stringify(data, null, 2));
      dispatch(setVisitList(data));
    } catch (error) {
      console.error("Unexpected error:", error);
    } finally {
      setLoadPosts(false);
    }
  };
  const handleButtonClick = (buttonIndex: any) => {
    // console.log(activeButton, buttonIndex);
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };
  const confirmDate = (date: any) => {
    //console.log("data,,,,,", date);
    setOpenDatePicker(false);
    const dateDate = moment(date).format();

    setMyBirthDay(dateDate);
  };
  const handleSaveProfile = async () => {
    try {
      setLoadIng(true);
      const request = {
        userId: encrypt(isMyUserId || ""),
        firstName: encrypt(isMyName || ""),
        dateOfBirth: isMyBirthDay || "",
        mobileNo: encrypt(isMyPhone || ""),
        email: encrypt(isMyEmail || ""),
        address: encrypt(isMyAddress || ""),
      };

      const response = await saveProfileApi(request);
      if (response?.success) {
        setModalTrue(true);
      } else {
        setModalError(true);
      }
    } catch (error) {
      console.log("err => ", error);
      setEditProfile(true);
    } finally {
      setLoadIng(false);
    }
  };
  const reload = () => {
    setLoadIng(true);
    const timeoutId = setTimeout(() => {
      callMyProfile();
      callAllImg();
      callAllVideo();
      callFarmDasboard();
      joinRoom();
    }, 1000);
    return () => {
      clearTimeout(timeoutId);
      setLoadIng(false);
    };
  };
  const onChange = () => {
    if (isEditProfile) {
      handleSaveProfile();
    }
    setEditProfile(!isEditProfile);
  };
  const onLogOut = async () => {
    logout(navigation);
    setModalLogOut(false);
  };
  const onSwitch = async (item: any) => {
    const current = enabledSwitches[item.id] ?? item.isPublic;
    const toggled = !current;
    setEnabledSwitches((prev) => ({ ...prev, [item.id]: toggled }));

    try {
      const res = await putPublic(item.id, toggled);
      callVisit();
    } catch (err) {
      console.log("Error:", err);
      setEnabledSwitches((prev) => ({ ...prev, [item.id]: current }));
    }
  };
  const onShare = async (item: any, index: number) => {
    try {
      setLoadShare((prevState) => ({ ...prevState, [index]: true }));
      // console.log(JSON.stringify(item, null, 2));
      const req = await shareSocial(item.id);
      const data = req.model.sharedUrl || "";
      // console.log(req);
      const result = await Share.open({
        title: "Mefarm",
        message: "",
        url: data,
      });
      console.log("แชร์สำเร็จ:", result);
    } catch (error: any) {
      if (error.message !== "User did not share") {
        Alert.alert("แชร์ไม่สำเร็จ", error.message);
      }
    } finally {
      joinRoom();
      setLoadShare((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        console.log("error");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const openCamera = async () => {
    try {
      const options: any = {
        mediaType: "photo",
        saveToPhotos: true,
        quality: 1,
        includeBase64: true,
      };

      launchCamera(options, async (response: any) => {
        if (response.didCancel) {
          return;
        }

        if (response.errorCode) {
          setModalPermiss(true);
          return;
        }

        if (response.assets && response.assets.length > 0) {
          const asset = response.assets[0];
          setImageUri(asset.uri);
          setFileName(asset.fileName || "unknown");
          setFileType(asset.type);

          const base64Data =
            asset.base64 || (await convertImageToBase64(asset.uri));

          const req = {
            files: [
              {
                fileName: asset.fileName || "unknown",
                contentType: asset.type,
                base64Data: base64Data,
              },
            ],
          };
          // console.log(req);

          let data: any = await upDateProfile(req);
          if (data?.success) {
            setModalTrue(true);
          } else {
            console.log("Error updating profile");
          }

          const timeoutId = setTimeout(() => {
            callMyProfile();
            callAllImg();
            callAllVideo();
            callFarmDasboard();
          }, 5000);

          return () => {
            clearTimeout(timeoutId);
            setLoadIng(false);
          };
        }
      });
    } catch (err: any) {
      if (err && err.code === "E_PICKER_CANCELLED") {
        return;
      }
      setModalPermiss(true); // แสดง ModalPermission ์ error
    }
  };
  const openCameraCover = async () => {
    try {
      const options: any = {
        mediaType: "photo",
        saveToPhotos: true,
        quality: 1,
        includeBase64: true,
      };

      launchCamera(options, async (response: any) => {
        if (response.didCancel) {
          return;
        }

        if (response.errorCode) {
          setModalPermiss(true);
          return;
        }

        if (response.assets && response.assets.length > 0) {
          const asset = response.assets[0];
          setCoverUri(asset.uri);
          setCoverName(asset.fileName || "unknown");
          setCoverType(asset.type);

          const base64Data =
            asset.base64 || (await convertImageToBase64(asset.uri));

          const req = {
            files: [
              {
                fileName: asset.fileName || "unknown",
                contentType: asset.type,
                base64Data: base64Data,
              },
            ],
          };
          // console.log(req);

          let data: any = await upDateCover(req);
          if (data?.success) {
            setModalTrue(true);
          } else {
            console.log("Error updating profile");
          }

          const timeoutId = setTimeout(() => {
            callMyProfile();
            callAllImg();
            callAllVideo();
            callFarmDasboard();
          }, 5000);

          return () => {
            clearTimeout(timeoutId);
            setLoadIng(false);
          };
        }
      });
    } catch (err) {
      console.log("error", err);
    }
  };
  const selectImageFromLibrary = async () => {
    try {
      const image = await ImageCropPicker.openPicker({
        cropping: true,
        cropperCircleOverlay: true,
        freeStyleCropEnabled: true,
        mediaType: "photo",
        compressImageQuality: 1,
      });
      setImageUri(image.path);
      setFileName(image.filename || "unknown");
      setFileType(image.mime);

      const base64Data = await convertImageToBase64(image.path);

      const req = {
        files: [
          {
            fileName: image.filename || "unknown",
            contentType: image.mime,
            base64Data: base64Data,
          },
        ],
      };
      // console.log(req);

      let data: any = await upDateProfile(req);
      if (data?.success) {
        setModalTrue(true);
      } else {
        console.log("Error updating profile");
      }

      const timeoutId = setTimeout(() => {
        callMyProfile();
        callAllImg();
        callAllVideo();
        callFarmDasboard();
      }, 5000);

      return () => {
        clearTimeout(timeoutId);
        setLoadIng(false);
      };
    } catch (err: any) {
      if (err && err.code === "E_PICKER_CANCELLED") {
        return;
      }
      setModalPermiss(true);
    }
  };
  const selectCoverFromLibrary = async () => {
    try {
      const image = await ImageCropPicker.openPicker({
        cropping: true,
        freeStyleCropEnabled: true,
        mediaType: "photo",
        compressImageQuality: 1,
      });
      setCoverUri(image.path);
      setCoverName(image.filename || "unknown");
      setCoverType(image.mime);
      const base64Data = await convertImageToBase64(image.path);
      const req = {
        files: [
          {
            fileName: image.filename || "unknown",
            contentType: image.mime,
            base64Data: base64Data,
          },
        ],
      };

      let data: any = await upDateCover(req);
      if (data?.success) {
        setModalTrue(true);
      } else {
        console.log("Error updating profile");
      }
      const timeoutId = setTimeout(() => {
        callMyProfile();
        callAllImg();
        callAllVideo();
        callFarmDasboard();
      }, 5000);

      return () => {
        clearTimeout(timeoutId);
        setLoadIng(false);
      };
    } catch (err: any) {
      if (err && err.code === "E_PICKER_CANCELLED") {
        return;
      }
      setModalPermiss(true);
    }
  };
  const openFullImag = (item: any) => {
    setImageFull(item.originalUrl);
    setModalFullImg(true);
  };
  const chunkArray = (array: any[], size: number) => {
    return array.reduce((acc, _, i) => {
      if (i % size === 0) acc.push(array.slice(i, i + size));
      return acc;
    }, [] as any[][]);
  };
  const toggleMenu = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const goEditPost = (item: any) => {
    navigation.navigate("EditPost", {
      docList: item,
    });
    setOpenIndex(null);
  };
  const onDelete = async (item: any, indexItem: number) => {
    try {
      setLoadIng(true);
      const socialPostId = item.id || "";
      let data = await deletePostApi(socialPostId);

      if (data.success) {
        // let deleteData = isDocListPost;
        let deleteData = docListProfile;
        if (data.model.id == deleteData[indexItem].id) {
          deleteData.splice(indexItem, 1);
        }

        setDocListProfile(deleteData);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setOpenIndex(null);
      joinRoom();
    }
  };
  const toggleFullText = (index: number) => {
    setIsShowFullText((x) => {
      const nextState = x ? [...x] : [];
      nextState[index] = !nextState[index];
      return nextState;
    });
  };
  const goLike = async (item: any, index: number) => {
    try {
      setLoadLikes((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("LikePostMessage", item.id);
        dispatch(updatePostLikes(item.id));
      }
    } catch (error) {
      console.log(error);
    } finally {
      joinRoom();
      setLoadLikes((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goComment = (item: any) => {
    const comment = "comment";
    navigation.navigate("DetailPost", {
      docList: item,
      typeComment: comment,
    });
  };
  const goDetailPost = (item: any, seePost: string) => {
    navigation.navigate("DetailPost", {
      docList: item,
      seePost: seePost,
    });
  };
  const loadMorePosts = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await joinRoom();
    setLoadPosts(false);
  };
  const loadMoreImages = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await callAllImg();
    setLoadPosts(false);
  };
  const loadMoreFarms = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await joinRoom();
    setLoadPosts(false);
  };
  const openActionSheet = (type: string) => {
    actionSheetRef.current?.show();
    setTypeAction(type);
  };
  const closeActionSheet = () => {
    actionSheetRef.current?.hide();
  };

  //Ui
  const headerConten = () => (
    <>
      {/* {Cover Img} */}
      <>
        {activeButton === 5 ? (
          <TouchableOpacity
            style={ctn.ctn_covenProfile}
            onPressIn={() =>
              activeButton === 5 ? openActionSheet("cover") : null
            }
          >
            <View style={ctn.ctn_editSvg}>{editSvg()}</View>
          </TouchableOpacity>
        ) : null}
        <TouchableOpacity
          style={img.img_coverProfile}
          onPress={() => setModalCover(!isModalCover)}
          disabled={activeButton === 5}
        >
          <MyImageComponent
            imageUrl={coverUriShow || coverUri}
            style={[
              img.img_coverProfile,
              !(coverUriShow || coverUri) && {
                backgroundColor: BgColor.Bg_ECECEC,
              },
            ]}
          />
        </TouchableOpacity>
      </>

      {/* {Profile Img} */}
      <View style={ctn.ctn_optionProfile}>
        <View style={ctn.ctn_mainProfile}>
          <>
            {activeButton === 5 ? (
              <TouchableOpacity
                style={ctn.ctn_editMainProfile}
                onPressIn={() =>
                  activeButton === 5 ? openActionSheet("profile") : null
                }
              >
                <View style={ctn.ctn_editSvg}>{editSvg()}</View>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              style={img.img_mainProfile}
              onPress={() => setModalProfile(!isModalProfile)}
              disabled={activeButton === 5}
            >
              <MyImageComponent
                imageUrl={imgUriShow || imageUri}
                style={img.img_mainProfile}
              />
            </TouchableOpacity>
          </>
        </View>
      </View>

      {/* {Setting & LogOut} */}

      <TouchableOpacity
        style={ctn.ctn_iconSetting}
        onPress={() => navigation.navigate("SettingApp")}
      >
        {iconSetting()}
      </TouchableOpacity>

      <TouchableOpacity
        style={ctn.ctn_iconLogout}
        onPress={() => setModalLogOut(true)}
      >
        {iconLogOut()}
      </TouchableOpacity>

      {/* {ชื่อ} */}
      <View style={ctn.ctn_menuProfile}>
        <View style={ctn.ctn_nameProfile}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>{isMyName}</Text>
        </View>

        {/* {number} */}
        <View style={{ margin: moderateScale(5) }} />
        <View style={ctn.ctn_spaceNumber}>
          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter?.post || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light, txt.txt_A6A6A6]}>
              {t("postNews")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={oth.line_vatical} />
          <View style={{ margin: moderateScale(10) }} />

          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter?.followers || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light, txt.txt_A6A6A6]}>
              {t("followers")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={oth.line_vatical} />
          <View style={{ margin: moderateScale(10) }} />

          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter?.following || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light, txt.txt_A6A6A6]}>
              {t("following")}
            </Text>
          </View>
        </View>

        {/* {Tap Select} */}
        <View style={ctn.ctn_spaceHeader}>
          {/* {โพสต์} */}
          <TouchableOpacity
            style={[activeButton === 1 ? ctn.ctn_Active : ctn.ctn_nonActive]}
            onPress={() => handleButtonClick(1)}
            disabled={activeButton === 1 ? true : false}
          >
            <Text
              style={[
                activeButton === 1 ? txt.txt_Active : txt.txt_nonActive,
                fonstStyle.f12_bold,
              ]}
            >
              {t("postNews")}
            </Text>
          </TouchableOpacity>

          {/* {ภาพ} */}
          <TouchableOpacity
            style={[activeButton === 2 ? ctn.ctn_Active : ctn.ctn_nonActive]}
            onPress={() => handleButtonClick(2)}
            disabled={activeButton === 2 ? true : false}
          >
            <Text
              style={[
                activeButton === 2 ? txt.txt_Active : txt.txt_nonActive,
                fonstStyle.f12_bold,
              ]}
            >
              {t("images")}
            </Text>
          </TouchableOpacity>

          {/* {ภาพ} */}
          <TouchableOpacity
            style={[activeButton === 3 ? ctn.ctn_Active : ctn.ctn_nonActive]}
            onPress={() => handleButtonClick(3)}
            disabled={activeButton === 3 ? true : false}
          >
            <Text
              style={[
                activeButton === 3 ? txt.txt_Active : txt.txt_nonActive,
                fonstStyle.f12_bold,
              ]}
            >
              {t("videos")}
            </Text>
          </TouchableOpacity>

          {/* {ค้า} */}
          <TouchableOpacity
            style={[activeButton === 4 ? ctn.ctn_Active : ctn.ctn_nonActive]}
            onPress={() => handleButtonClick(4)}
            disabled={activeButton === 4 ? true : false}
          >
            <Text
              style={[
                activeButton === 4 ? txt.txt_Active : txt.txt_nonActive,
                fonstStyle.f12_bold,
              ]}
            >
              {t("product")}
            </Text>
          </TouchableOpacity>

          {/* {เกี่ยวข้อง} */}
          <TouchableOpacity
            style={[activeButton === 5 ? ctn.ctn_Active : ctn.ctn_nonActive]}
            onPress={() => handleButtonClick(5)}
            disabled={activeButton === 5 ? true : false}
          >
            <Text
              style={[
                activeButton === 5 ? txt.txt_Active : txt.txt_nonActive,
                fonstStyle.f12_bold,
              ]}
            >
              {t("about")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
  const renderPagePic = ({ item }: any) => {
    return (
      <TouchableOpacity onPress={() => openFullImag(item)}>
        <MyImageComponent
          imageUrl={item.thumbnailUrl}
          style={ctn.ctn_ActivePost}
        />
      </TouchableOpacity>
    );
  };
  const renderVideo = ({ item, index }: any) => {
    return (
      <>
        <Video
          source={{ uri: item.originalUrl }}
          style={[ctn.ctn_ActiveVideo]}
          controls={true}
          resizeMode="cover"
          bufferConfig={{
            minBufferMs: 3000,
            maxBufferMs: 10000,
            bufferForPlaybackMs: 1500,
            bufferForPlaybackAfterRebufferMs: 3000,
          }}
          disableFocus={true}
          disableDisconnectError={true}
        />
        {/* <TouchableOpacity
          onPress={() => setIsMuted(!isMuted)}
          style={oth.mutedVideoProfile}
        >
          {isMuted ? (
            <VolumeX color="white" size={24} />
          ) : (
            <Volume2 color="white" size={24} />
          )}
        </TouchableOpacity> */}
      </>
    );
  };
  const renderMyFarm = ({ item, index }: any) => {
    return (
      <View
        style={{
          marginTop: moderateScale(10),
          paddingHorizontal: moderateScale(10),
        }}
      >
        <View style={ctn.ctn_farmPackProfile}>
          <MyImageComponent imageUrl={item.imageUrl }  style={img.img_MyFarm}/>
          <View style={{ margin: moderateScale(5) }} />
          <View style={ctn.ctn_productProfile}>
            <View style={{ flex: 2 }}>
              <Text
                numberOfLines={2}
                style={[fonstStyle.f14_bold, txt.txt_606060]}
              >
                {item.plotName}
              </Text>
              <Text
                numberOfLines={2}
                style={[fonstStyle.f12_light, txt.txt_606060]}
              >
                @{item.farmName}
              </Text>
              <View style={{ margin: moderateScale(2) }} />
              <View style={{ flexDirection: "row" }}>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {t("area")}{" "}
                </Text>
                <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                  {item.squareMeters}{" "}
                </Text>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {t("Square_Meters")}
                </Text>
              </View>
              <Text
                numberOfLines={2}
                style={[fonstStyle.f12_bold, txt.txt_green]}
              >
                {item.status}
              </Text>
            </View>

            <View style={{ flex: 1, alignItems: "flex-end" }}>
              <Text style={[fonstStyle.f12_light, txt.txt_green]}>
                {t("set_public")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <Switch
                trackColor={{
                  false: BgColor.Bg_D6D6D6,
                  true: BgColor.Bg_84B8A2,
                }}
                thumbColor={
                  item.isPublic ? BgColor.Bg_FFFFFF : BgColor.Bg_FFFFFF
                }
                onValueChange={() => onSwitch(item)}
                value={enabledSwitches[item.id] ?? item.isPublic}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderDetail = () => {
    return (
      <>
        {activeButton === 5 ? (
          <View
            style={{
              padding: moderateScale(20),
              backgroundColor: BgColor.Bg_FFFFFF,
            }}
          >
            <View style={ctn.ctn_information}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("basic_information")}
              </Text>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity onPress={() => setEditProfile(false)}>
                  {!isEditProfile ? null : iconNon()}
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity onPress={() => onChange()}>
                  {!isEditProfile ? editSvg() : iconSaveDetail()}
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ margin: moderateScale(10) }} />

            <View style={{ flexDirection: "row" }}>
              <View style={ctn.ctn_unDerIcon}>{iconUser()}</View>
              <View style={{ margin: moderateScale(5) }} />
              <View style={ctn.ctn_detail}>
                {!isEditProfile ? (
                  <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                    {isMyName}
                  </Text>
                ) : (
                  <TextInput
                    style={[
                      fonstStyle.f12_light,
                      txt.txt_editProfile,
                      txt.txt_606060,
                    ]}
                    onChangeText={setMyName}
                    value={isMyName}
                    inputAccessoryViewID="Done"
                  />
                )}
                <View style={{ margin: moderateScale(2) }} />
                <Text
                  style={[
                    fonstStyle.f12_light,
                    txt.txt_fixText,
                    txt.txt_606060,
                  ]}
                >
                  {t("name")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <View style={oth.line_profile} />
              </View>
            </View>
            <View style={{ margin: moderateScale(5) }} />

            <View style={{ flexDirection: "row" }}>
              <View style={ctn.ctn_unDerIcon}>{iconBirdDay()}</View>
              <View style={{ margin: moderateScale(5) }} />
              <View style={ctn.ctn_detail}>
                {!isEditProfile ? (
                  <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                    {/* {isMyBirthDay} */}
                    {/* {moment(isMyBirthDay).format("DD MMMM YYYY")} */}
                    {isMyBirthDay && moment(isMyBirthDay).isValid()
                      ? moment(isMyBirthDay).format("DD MMMM YYYY")
                      : ""}
                  </Text>
                ) : (
                  <TouchableOpacity onPress={() => setOpenDatePicker(true)}>
                    <Text
                      style={[
                        fonstStyle.f12_light,
                        txt.txt_editProfile,
                        txt.txt_606060,
                      ]}
                    >
                      {/* {isMyBirthDay} */}
                      {/* {moment(isMyBirthDay).format("DD MMMM YYYY")} */}
                      {isMyBirthDay && moment(isMyBirthDay).isValid()
                        ? moment(isMyBirthDay).format("DD MMMM YYYY")
                        : ""}
                    </Text>
                    <DatePicker
                      modal
                      open={openDatePicker}
                      date={dateChange}
                      mode="date"
                      locale={t("locale")}
                      title={null}
                      confirmText={t("confirm")}
                      cancelText={t("cancel")}
                      theme="auto"
                      onConfirm={(date) => {
                        confirmDate(date);
                      }}
                      onCancel={() => {
                        setOpenDatePicker(false);
                      }}
                    />
                  </TouchableOpacity>
                )}
                <View style={{ margin: moderateScale(2) }} />
                <Text
                  style={[
                    fonstStyle.f12_light,
                    txt.txt_fixText,
                    txt.txt_606060,
                  ]}
                >
                  {t("birth_day")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <View style={oth.line_profile} />
              </View>
            </View>

            <View style={{ margin: moderateScale(10) }} />
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("contact")}
            </Text>
            <View style={{ margin: moderateScale(10) }} />

            <View style={{ flexDirection: "row" }}>
              <View style={ctn.ctn_unDerIcon}>{iconPhone()}</View>
              <View style={{ margin: moderateScale(5) }} />
              <View style={ctn.ctn_detail}>
                {!isEditProfile ? (
                  <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                    {isMyPhone}
                  </Text>
                ) : (
                  <TextInput
                    style={[
                      fonstStyle.f12_light,
                      txt.txt_editProfile,
                      txt.txt_606060,
                    ]}
                    // onChangeText={(text) => {
                    //   if (text != null || text != "") {
                    //     if (text.length <= 10) {
                    //       setMyPhone(text);
                    //     }
                    //   }
                    // }}
                    value={isMyPhone}
                    keyboardType="numeric"
                    maxLength={10}
                  />
                )}
                <Text
                  style={[
                    fonstStyle.f12_light,
                    txt.txt_fixText,
                    txt.txt_606060,
                  ]}
                >
                  {t("phone_nember")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <View style={oth.line_profile} />
              </View>
            </View>
            <View style={{ margin: moderateScale(5) }} />

            <View style={{ flexDirection: "row" }}>
              <View style={ctn.ctn_unDerIcon}>{iconEmail()}</View>
              <View style={{ margin: moderateScale(5) }} />
              <View style={ctn.ctn_detail}>
                {/* <View style={{ margin: moderateScale(5) }} /> */}
                {!isEditProfile ? (
                  <Text
                    style={[fonstStyle.f12_bold, txt.txt_606060]}
                    numberOfLines={2}
                  >
                    {isMyEmail}
                  </Text>
                ) : (
                  <TextInput
                    style={[
                      fonstStyle.f12_light,
                      txt.txt_editProfile,
                      txt.txt_606060,
                    ]}
                    onChangeText={setMyEmail}
                    value={isMyEmail}
                    keyboardType="email-address"
                  />
                )}
                <Text
                  style={[
                    fonstStyle.f12_light,
                    txt.txt_fixText,
                    txt.txt_606060,
                  ]}
                >
                  {t("email")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <View style={oth.line_profile} />
              </View>
            </View>
            <View style={{ margin: moderateScale(5) }} />

            <View style={{ flexDirection: "row" }}>
              <View style={ctn.ctn_unDerIcon}>{iconPinDetail()}</View>
              <View style={{ margin: moderateScale(5) }} />
              <View style={ctn.ctn_detail}>
                {/* <View style={{ margin: moderateScale(5) }} /> */}
                {!isEditProfile ? (
                  <Text
                    style={[fonstStyle.f12_bold, txt.txt_606060]}
                    numberOfLines={5}
                  >
                    {isMyAddress}
                  </Text>
                ) : (
                  <TextInput
                    style={[
                      fonstStyle.f12_light,
                      txt.txt_editProfile,
                      txt.txt_606060,
                    ]}
                    onChangeText={setMyAddress}
                    value={isMyAddress}
                  />
                )}
                <Text
                  style={[
                    fonstStyle.f12_light,
                    txt.txt_fixText,
                    txt.txt_606060,
                  ]}
                >
                  {t("address")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <View style={oth.line_profile} />
              </View>
            </View>
            <View style={{ margin: moderateScale(120) }} />
          </View>
        ) : null}
      </>
    );
  };
  const onScroll = ({ nativeEvent }: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isScrolledToEnd =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    // เช็คว่า ท้าย scroll และ อยู่  tab ไหน
    if (isScrolledToEnd && !isLoadPosts) {
      switch (activeButton) {
        case 1:
          loadMorePosts(); // function โหลดข้อมูล ของ tab 1
          break;
        case 2:
          loadMoreImages(); // function โหลดข้อมูล ของ tab 2
          break;
        case 4:
          loadMoreFarms(); // function โหลดข้อมูล ของ tab 4
          break;
        default:
          break;
      }
    }
  };
  const renderActionSheet = () => {
    return (
      <ActionSheet
        ref={actionSheetRef}
         gestureEnabled
        containerStyle={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      >
        <TouchableOpacity
          style={{ padding: 20 }}
          onPress={() => {
            typeAction === "profile" ? openCamera() : openCameraCover();
          }}
        >
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconCameraEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("open_camera")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity
          style={{ padding: 20 }}
          onPress={() => {
            typeAction === "profile"
              ? selectImageFromLibrary()
              : selectCoverFromLibrary();
          }}
        >
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconGalleryEdit()}
            <View style={{ margin: moderateScale(5) }} />

            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("photo_gallery")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity style={{ padding: 20 }} onPress={closeActionSheet}>
          <Text
            style={[fonstStyle.f14_light, txt.txt_red, { textAlign: "center" }]}
          >
            {t("cancel")}
          </Text>
        </TouchableOpacity>
      </ActionSheet>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1, backgroundColor: BgColor.Bg_EDEDED }}
      >
        <ScrollView
          ref={scrollRef}
          nestedScrollEnabled={true}
          style={[ctn.continue]}
          showsVerticalScrollIndicator={false}
          onScroll={onScroll}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl refreshing={isLoadIng} onRefresh={reload} />
          }
        >
          {headerConten()}
          {activeButton === 1 && (
            <FlatList
              data={docListProfile}
              keyExtractor={(item, index) => `post-${item.id || index}`}
              renderItem={({ item, index }) => (
                <PostItem
                  item={item}
                  index={index}
                  isUserId={userId}
                  isShowFullText={isShowFullText}
                  toggleFullText={toggleFullText}
                  openIndex={openIndex}
                  toggleMenu={toggleMenu}
                  isMuted={isMuted}
                  setIsMuted={setIsMuted}
                  isLoadLikes={isLoadLikes}
                  isLoadShare={isLoadShare}
                  setLoadIng={setLoadIng}
                  imageDimensions={imageDimensions}
                  setImageDimensions={setImageDimensions}
                  NUM_OF_LINES={NUM_OF_LINES}
                  t={t}
                  goDetailPost={goDetailPost}
                  goLike={goLike}
                  goComment={goComment}
                  onShare={onShare}
                  goEditPost={goEditPost}
                  onDelete={onDelete}
                  goFriend={(item: any) => navigation.navigate("ProfileUser")}
                  navigation={navigation}
                />
              )}
              ListFooterComponent={
                <>
                  <View style={{ margin: moderateScale(20) }} />
                  {isLoadPosts && <LoadingApp />}
                  <View style={{ margin: moderateScale(120) }} />
                </>
              }
              showsVerticalScrollIndicator={false}
              refreshing={isLoadPosts}
              // onRefresh={reload}
              onEndReached={loadMorePosts}
              onEndReachedThreshold={0.2}
            />
          )}
          {activeButton === 2 && (
            <View>
              {chunkArray(docAllImage, 3).map((row: any, rowIndex: any) => (
                <View
                  key={rowIndex}
                  style={[ctn.ctn_spaceBet, { justifyContent: "flex-start" }]}
                >
                  {row.map((item: any, index: number) => (
                    <View key={index} style={{ width: "33.3%" }}>
                      {renderPagePic({ item })}
                    </View>
                  ))}
                </View>
              ))}
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts && <LoadingApp />}
              <View style={{ margin: moderateScale(120) }} />
            </View>
          )}
          {activeButton === 3 && (
            <View>
              {chunkArray(docAllVideo, 3).map((row: any, rowIndex: any) => (
                <View
                  key={rowIndex}
                  style={[ctn.ctn_spaceBet, { justifyContent: "flex-start" }]}
                >
                  {row.map((item: any, index: number) => (
                    <View key={index} style={{ width: "33.3%" }}>
                      {renderVideo({ item, index })}
                    </View>
                  ))}
                </View>
              ))}
              <View style={{ margin: moderateScale(120) }} />
            </View>
          )}
          {activeButton === 4 && (
            <>
              {docMyFarmList.map((item: any, index: number) =>
                renderMyFarm({ item, index })
              )}
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts && <LoadingApp />}
              <View style={{ margin: moderateScale(120) }} />
            </>
          )}
          {activeButton === 5 && renderDetail()}
          <ModalFullImg
            images={[{ uri: imageFull }]}
            visible={isModalFullImg}
            onClose={() => setModalFullImg(false)}
          />
          <ModalProfile
            images={[{ uri: imgUriShow }]}
            visible={isModalProfile}
            onClose={() => setModalProfile(false)}
          />
          <ModalCover
            images={[{ uri: coverUriShow }]}
            visible={isModalCover}
            onClose={() => setModalCover(false)}
          />
          {renderActionSheet()}
        </ScrollView>
      </KeyboardAvoidingView>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1, backgroundColor: BgColor.Bg_EDEDED }}
        >
          <ScrollView
            ref={scrollRef}
            nestedScrollEnabled={true}
            style={[ctn.continue]}
            showsVerticalScrollIndicator={false}
            onScroll={onScroll}
            scrollEventThrottle={16}
            refreshControl={
              <RefreshControl refreshing={isLoadIng} onRefresh={reload} />
            }
          >
            {headerConten()}
            {activeButton === 1 && (
              <FlatList
                data={docListProfile}
                keyExtractor={(item, index) => `post-${item.id || index}`}
                renderItem={({ item, index }) => (
                  <PostItem
                    item={item}
                    index={index}
                    isUserId={userId}
                    isShowFullText={isShowFullText}
                    toggleFullText={toggleFullText}
                    openIndex={openIndex}
                    toggleMenu={toggleMenu}
                    isMuted={isMuted}
                    setIsMuted={setIsMuted}
                    isLoadLikes={isLoadLikes}
                    isLoadShare={isLoadShare}
                    setLoadIng={setLoadIng}
                    imageDimensions={imageDimensions}
                    setImageDimensions={setImageDimensions}
                    NUM_OF_LINES={NUM_OF_LINES}
                    t={t}
                    goDetailPost={goDetailPost}
                    goLike={goLike}
                    goComment={goComment}
                    onShare={onShare}
                    goEditPost={goEditPost}
                    onDelete={onDelete}
                    goFriend={(item: any) => navigation.navigate("ProfileUser")}
                    navigation={navigation}
                  />
                )}
                ListFooterComponent={
                  <>
                    <View style={{ margin: moderateScale(20) }} />
                    {isLoadPosts && <LoadingApp />}
                    <View style={{ margin: moderateScale(120) }} />
                  </>
                }
                showsVerticalScrollIndicator={false}
                refreshing={isLoadPosts}
                onRefresh={reload}
                onEndReached={loadMorePosts}
                onEndReachedThreshold={0.2}
                nestedScrollEnabled
              />
            )}
            {activeButton === 2 && (
              <View>
                {chunkArray(docAllImage, 3).map((row: any, rowIndex: any) => (
                  <View
                    key={rowIndex}
                    style={[ctn.ctn_spaceBet, { justifyContent: "flex-start" }]}
                  >
                    {row.map((item: any, index: number) => (
                      <View key={index} style={{ width: "33.3%" }}>
                        {renderPagePic({ item })}
                      </View>
                    ))}
                  </View>
                ))}
                <View style={{ margin: moderateScale(20) }} />
                {isLoadPosts && <LoadingApp />}
                <View style={{ margin: moderateScale(120) }} />
              </View>
            )}
            {activeButton === 3 && (
              <View>
                {chunkArray(docAllVideo, 3).map((row: any, rowIndex: any) => (
                  <View
                    key={rowIndex}
                    style={[ctn.ctn_spaceBet, { justifyContent: "flex-start" }]}
                  >
                    {row.map((item: any, index: number) => (
                      <View key={index} style={{ width: "33.3%" }}>
                        {renderVideo({ item, index })}
                      </View>
                    ))}
                  </View>
                ))}
                <View style={{ margin: moderateScale(120) }} />
              </View>
            )}
            {activeButton === 4 && (
              <>
                {docMyFarmList.map((item: any, index: number) =>
                  renderMyFarm({ item, index })
                )}
                <View style={{ margin: moderateScale(20) }} />
                {isLoadPosts && <LoadingApp />}
                <View style={{ margin: moderateScale(120) }} />
              </>
            )}
            {activeButton === 5 && renderDetail()}
            <ModalFullImg
              images={[{ uri: imageFull }]}
              visible={isModalFullImg}
              onClose={() => setModalFullImg(false)}
            />
            <ModalProfile
              images={[{ uri: imgUriShow }]}
              visible={isModalProfile}
              onClose={() => setModalProfile(false)}
            />
            <ModalCover
              images={[{ uri: coverUriShow }]}
              visible={isModalCover}
              onClose={() => setModalCover(false)}
            />
            {renderActionSheet()}
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle={"light-content"} hidden={false} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
      <ModalPermission
        visible={isModalPermiss}
        onClose={() => setModalPermiss(false)}
      />
      <ModalTrue visible={isModalTrue} onClose={() => setModalTrue(false)} />
      <ModalFalse visible={isModalError} onClose={() => setModalError(false)} />
      <ModalLogOut
        visible={isModalLogOut}
        onClose={() => setModalLogOut(false)}
        logOut={onLogOut}
      />
    </>
  );
}
