import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Bar,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
} from "react-native";
import "moment/locale/th";
import Share from "react-native-share";
import { HubConnection } from "@microsoft/signalr";
import { Header as HeaderRNE } from "@rneui/themed";
import ImageViewing from "react-native-image-viewing";
import { useIsFocused } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import React, { useState, useEffect, useRef, useCallback } from "react";
//StyleSheet
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
import { goBack_gay } from "../../assets/svg/svg_naviagte";
//Translation
import { useTranslation } from "../i18n";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { updatePostLikes } from "../../Redux_Store/action";
//commponents
import Images from "../../utils/imageManager";
import PostItem from "../../components/PostItem";
import { useAuthTokens } from "../../hooks/useAuthTokens";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import { setupNetworkListener } from "../../utils/networkManager";
import { setupSignalRConnection } from "../../utils/signalRManager";
import { MyImageComponent } from "../../components/cacheFiles/cache";
//Api
import { shareSocial } from "../../action/Mefarm_Social_API";

export default function Profilefriend({ navigation, route }: any) {
  const orientation = useOrientation();
  const { accessToken, userId } = useAuthTokens();

  const params = route.params || "";
  const docFriend = params.item || "";
  const friendId = docFriend.userId || "";
  const imgFriend = docFriend.profileImageUrl || "";
  const friendName = docFriend.firstName || "";
  //State
  const isFocused = useIsFocused();
  const { t } = useTranslation();
  //Array
  const [comment, setComment] = useState({ comments: [] });
  //True & False
  const [isMuted, setIsMuted] = useState(true);
  const [showHeader, setShowHeader] = useState(true);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isModalImang, setModalImage] = useState<boolean>(false);
  const [isModalCover, setModalCover] = useState<boolean>(false);
  const [isUserScrolled, setIsUserScrolled] = useState<boolean>(false);
  const [isLoadLikes, setLoadLikes] = useState<{ [key: number]: boolean }>({});
  const [isLoadShare, setLoadShare] = useState<{ [key: number]: boolean }>({});
  //Number
  const NUM_OF_LINES = 5;
  const [pageSize, setPageSize] = useState<number>(10);
  const scrollY = useRef(new Animated.Value(0)).current;
  //Null
  const connectionRef = useRef<any>(null);
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [imageDimensions, setImageDimensions] = useState<any>(null);
  const [connection, setConnection] = useState<null | HubConnection>(null);
  //Dispatch
  const dispatch = useDispatch();
  const docListFriend = useSelector((state: any) => state.docListFriend);
  const docProfile = useSelector((state: any) => state.docProfile);
  const followCounter = useSelector((state: any) => state.followCounter);

  const [isShowFullText, setIsShowFullText] = useState<boolean[]>(
    Array.isArray(docListFriend) && docListFriend.length > 0
      ? docListFriend.map(() => false)
      : []
  );

  //Function
  useEffect(() => {
    const listener = scrollY.addListener(({ value }) => {
      setShowHeader(value < 100);
    });

    return () => scrollY.removeListener(listener);
  }, []);
  useEffect(() => {
    let signalRCleanup: (() => void) | null = null;

    const initConnection = async () => {
      try {
        const result = await setupSignalRConnection(
          dispatch,
          (newConnection) => {
            setConnection(newConnection);
            connectionRef.current = newConnection;
            // joinRoom จะถูกเรียกหลัง connection พร้อม
            joinRoom(newConnection);
          },
          { page: "friend", forceReconnect: true } // <-- เพิ่ม forceReconnect
        );
        signalRCleanup = result.cleanup;
      } catch (error) {
        console.error("Error setting up SignalR connection:", error);
      }
    };

    if (isFocused) {
      initConnection();
    }

    return () => {
      if (signalRCleanup) {
        console.log("Cleaning up SignalR connection on profileFriend unmount");
        signalRCleanup();
      }
    };
  }, [isFocused, dispatch, friendId, accessToken, pageSize]);

  const joinRoom = async (conn?: HubConnection) => {
    const activeConnection = conn || connectionRef.current;
    if (!activeConnection || activeConnection.state !== "Connected") return;

    const joinData = {
      roomId: friendId,
      followerUserId: friendId,
      accessToken: accessToken,
      followingUserId: friendId,
      isDescending: true,
      firstPageSize: pageSize,
      sendType: "normal",
    };

    console.log("Join Room Data:", joinData);

    try {
      await activeConnection.invoke("JoinRoom", joinData);
    } catch (err) {
      console.error("Error invoking JoinRoom:", err);
    }
  };
  useEffect(() => {
    const unsubscribe = setupNetworkListener(
      isFocused,
      connection,
      joinRoom,
      navigation
    );

    return () => {
      unsubscribe();
      // console.log("Network listener cleaned up.");
    };
  }, [isFocused, connection]);
  const onRefreshHandler = useCallback(async () => {
    setLoadPosts(true);
    setIsUserScrolled(false);
    await joinRoom();
    setLoadPosts(false);
  }, []);

  const toggleMenu = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const goEditPost = (item: any) => {
    navigation.navigate("EditPost", {
      docList: item,
    });
    setOpenIndex(null);
  };
  const goLike = async (item: any, index: number) => {
    try {
      setLoadLikes((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("LikePostMessage", item.id);
        dispatch(updatePostLikes(item.id));
      }
    } catch (error) {
      console.log(error);
    } finally {
      joinRoom();
      setLoadLikes((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goComment = (item: any) => {
    setComment(item);
    const comment = "comment";
    navigation.navigate("DetailPost", {
      docList: item,
      typeComment: comment,
    });
  };
  const goDetailPost = (item: any, seePost: string) => {
    navigation.navigate("DetailPost", {
      docList: item,
      seePost: seePost,
    });
  };
  const goBack = () => {
    navigation.goBack();
  };
  const loadMoreData = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await joinRoom();
    setTimeout(() => {
      setLoadPosts(false);
    }, 1000);
  };
  const onShare = async (item: any, index: number) => {
    try {
      setLoadShare((prevState) => ({ ...prevState, [index]: true }));
      const req = await shareSocial(item.id);
      const data = req.model.sharedUrl || "";
      const result = await Share.open({
        title: "Mefarm",
        message: "",
        url: data,
      });
      console.log("แชร์สำเร็จ:", result);
    } catch (error: any) {
      if (error.message !== "User did not share") {
        Alert.alert("แชร์ไม่สำเร็จ", error.message);
      }
    } finally {
      joinRoom();
      setLoadShare((prevState) => ({ ...prevState, [index]: false }));
    }
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      placement="left"
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={centerComment()}
      // backgroundImage={Images.bgApp}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>{goBack_gay()}</TouchableOpacity>
  );
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {friendName}
    </Text>
  );
  const headerConten = () => (
    <>
      {/* {Cover Img} */}
      <>
        <TouchableOpacity onPress={() => setModalCover(!isModalCover)}>
          <MyImageComponent
            imageUrl={docProfile?.coverImageUrl || undefined}
            style={[img.img_coverProfileFriend]}
          />
        </TouchableOpacity>
      </>

      {/* {Profile Img} */}
      <View style={ctn.ctn_optionProfileFriend}>
        <View style={ctn.ctn_mainProfile}>
          <TouchableOpacity
            style={img.img_mainProfile}
            onPress={() => setModalImage(!isModalImang)}
          >
            <MyImageComponent
              imageUrl={docProfile?.profileImageUrl || undefined}
              style={img.img_mainProfile}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* {ชื่อ} */}
      <View style={ctn.ctn_menuProfile}>
        <View style={ctn.ctn_nameProfile}>
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {friendName}
          </Text>
        </View>

        {/* {number} */}
        <View style={ctn.ctn_spaceFriend}>
          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter.post || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light]}>
              {t("postNews")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={oth.line_vatical} />
          <View style={{ margin: moderateScale(10) }} />
          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter.followers || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light]}>
              {t("followers")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(10) }} />
          <View style={oth.line_vatical} />
          <View style={{ margin: moderateScale(10) }} />
          <View style={{ flexDirection: "column" }}>
            <Text style={[txt.txt_Number, fonstStyle.f14_bold]}>
              {followCounter.following || 0}
            </Text>
            <Text style={[txt.txt_tap, fonstStyle.f12_light]}>
              {t("following")}
            </Text>
          </View>
        </View>
      </View>

      {/* <View style={{ marginTop: moderateScale(10) }} /> */}
    </>
  );
  const renderModalImage = () => {
    return (
      <View style={{ flex: 1 }}>
        <ImageViewing
          images={[{ uri: docProfile?.profileImageUrl || "" }]}
          imageIndex={0}
          visible={isModalImang}
          onRequestClose={() => setModalImage(!isModalImang)}
        />
      </View>
    );
  };
  const renderModalCover = () => {
    return (
      <View style={{ flex: 1 }}>
        <ImageViewing
          images={[{ uri: docProfile?.coverImageUrl || "" }]}
          imageIndex={0}
          visible={isModalCover}
          onRequestClose={() => setModalCover(!isModalCover)}
        />
      </View>
    );
  };

  const mainPortrait = () => {
    return (
      <>
        <FlatList
          data={docListFriend}
         removeClippedSubviews={false}
          keyExtractor={(item, index) => `post-${item.id || index}`}
          renderItem={({ item, index }) => (
            <PostItem
              item={item}
              index={index}
              isUserId={userId}
              isShowFullText={isShowFullText}
              toggleFullText={(idx) => {
                const newState = [...isShowFullText];
                newState[idx] = !newState[idx];
                setIsShowFullText(newState);
              }}
              openIndex={openIndex}
              toggleMenu={toggleMenu}
              isMuted={isMuted}
              setIsMuted={setIsMuted}
              isLoadLikes={isLoadLikes}
              isLoadShare={isLoadShare}
              setLoadIng={setLoadIng}
              imageDimensions={imageDimensions}
              setImageDimensions={setImageDimensions}
              NUM_OF_LINES={NUM_OF_LINES}
              t={t}
              goDetailPost={goDetailPost}
              goLike={goLike}
              goComment={goComment}
              onShare={onShare}
              goEditPost={goEditPost}
              onDelete={() => undefined}
              goFriend={() => undefined}
              navigation={navigation}
            />
          )}
          ListHeaderComponent={headerConten()}
          ListFooterComponent={
            <>
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts ? LoadingApp() : null}
              <View style={{ margin: moderateScale(120) }} />
              {renderModalCover()}
              {renderModalImage()}
            </>
          }
          refreshing={isLoadPosts}
          onRefresh={onRefreshHandler}
          onEndReached={loadMoreData}
          onEndReachedThreshold={0.2}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        />
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <FlatList
          data={docListFriend}
          removeClippedSubviews={false}
          keyExtractor={(item, index) => `post-${item.id || index}`}
          renderItem={({ item, index }) => (
            <PostItem
              item={item}
              index={index}
              isUserId={userId}
              isShowFullText={isShowFullText}
              toggleFullText={(idx) => {
                const newState = [...isShowFullText];
                newState[idx] = !newState[idx];
                setIsShowFullText(newState);
              }}
              openIndex={openIndex}
              toggleMenu={toggleMenu}
              isMuted={isMuted}
              setIsMuted={setIsMuted}
              isLoadLikes={isLoadLikes}
              isLoadShare={isLoadShare}
              setLoadIng={setLoadIng}
              imageDimensions={imageDimensions}
              setImageDimensions={setImageDimensions}
              NUM_OF_LINES={NUM_OF_LINES}
              t={t}
              goDetailPost={goDetailPost}
              goLike={goLike}
              goComment={goComment}
              onShare={onShare}
              goEditPost={goEditPost}
              onDelete={() => undefined}
              goFriend={() => undefined}
              navigation={navigation}
            />
          )}
          ListHeaderComponent={headerConten()}
          ListFooterComponent={
            <>
              <View style={{ margin: moderateScale(20) }} />
              {isLoadPosts ? LoadingApp() : null}
              <View style={{ margin: moderateScale(120) }} />
              {renderModalCover()}
              {renderModalImage()}
            </>
          }
          refreshing={isLoadPosts}
          onRefresh={onRefreshHandler}
          onEndReached={loadMoreData}
          onEndReachedThreshold={0.2}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        />
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {showHeader && headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
