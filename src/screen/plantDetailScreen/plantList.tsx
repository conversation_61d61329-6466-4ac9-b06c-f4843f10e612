import {
  StyleSheet,
  Text,
  View,
  FlatList,
  <PERSON>Bar,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Default_Bar from "../../components/appBar/default_Bar";
import { moderateScale, verticalScale } from "react-native-size-matters";
import FastImage from "react-native-fast-image";
import Loading from "../../components/loading/loading";
import LoadingApp from "../../components/loading/loadingApp";
import { useOrientation } from "../../hooks/useOrientation";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Api
import { seedApi } from "../../action/Mefarm_Farm_API";

//Translation
import { useTranslation } from "../i18n";
import { useDispatch, useSelector } from "react-redux";
import { setDocSeedList } from "../../Redux_Store/action";

export default function PlantList({ navigation }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const docSeedlist = useSelector((state: any) => state.docSeedlist);

  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);

  useEffect(() => {
    callSeed();
    // callAdminPlant();
  }, []);

  const callSeed = async () => {
    try {
      setLoadIng(true);
      const req = {
        searchText: "",
        pageSize: pageSize,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await seedApi(req);
      const seedData = response.model || "";
      setDocSeedList(seedData);
      dispatch(setDocSeedList(seedData));
      // console.log(JSON.stringify(seedData, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const goBack = () => {
    navigation.goBack();
  };
  const goPlantDetail = (item: any) => {
    navigation.navigate("PlantDetail", { docDetail: item });
  };
  const loadMoreData = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await callSeed();
    setLoadPosts(false);
  };
  const renderToday = ({ item }: any) => {
    return (
      <TouchableOpacity
        style={[ctn.ctn_contentNoti]}
        onPress={() => goPlantDetail(item)}
      >
        <View style={ctn.ctn_imgNoti}>
          <FastImage
            style={img.img_imgNoti}
            source={{ uri: item.imageUrl }}
            resizeMode={FastImage.resizeMode.contain}
          />
        </View>
        <View style={{ margin: moderateScale(8) }} />
        <View style={ctn.ctn_txtContent}>
          <Text
            style={[
              fonstStyle.f12_bold,
              item.isRead === true ? txt.txt_gray : txt.txt_606060,
            ]}
          >
            {item.seedName || "-"}
          </Text>
          <View style={{ margin: moderateScale(2) }} />
          <Text
            style={[
              fonstStyle.f12_light,
              item.isRead === true ? txt.txt_gray : txt.txt_606060,
            ]}
          >
            {item.typeName || "-"}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  const mainPortrait = () => {
    return (
      <>
        <FlatList
          data={docSeedlist}
          removeClippedSubviews={false}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) =>
            item.id?.toString() || index.toString()
          }
          renderItem={({ item, index }) => renderToday({ item, index })}
          ListEmptyComponent={
            <View style={{ alignItems: "center", padding: 10 }}>
              <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                {t("no_notiList")}
              </Text>
            </View>
          }
          ListFooterComponent={
            isLoadPosts ? (
              <View style={{ alignItems: "center", margin: 20 }}>
                {LoadingApp()}
              </View>
            ) : null
          }
          onScroll={({ nativeEvent }) => {
            const { layoutMeasurement, contentOffset, contentSize } =
              nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
        />
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <FlatList
          data={docSeedlist}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) =>
            item.id?.toString() || index.toString()
          }
          renderItem={({ item, index }) => renderToday({ item, index })}
          ListEmptyComponent={
            <View style={{ alignItems: "center", padding: 10 }}>
              <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                {t("no_notiList")}
              </Text>
            </View>
          }
          ListFooterComponent={
            isLoadPosts ? (
              <View style={{ alignItems: "center", margin: 20 }}>
                {LoadingApp()}
              </View>
            ) : null
          }
          onScroll={({ nativeEvent }) => {
            const { layoutMeasurement, contentOffset, contentSize } =
              nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
        />
      </SafeAreaView>
    );
  };
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      <Default_Bar onBack={goBack} title={t("my_seeds")} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}

const styles = StyleSheet.create({});
