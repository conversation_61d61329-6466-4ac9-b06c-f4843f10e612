import {
  Text,
  View,
  Modal,
  FlatList,
  Platform,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import FastImage from "react-native-fast-image";
import { Header as HeaderRNE } from "@rneui/themed";
import { useFocusEffect } from "@react-navigation/native";
import {
  Swipeable,
  GestureHandlerRootView,
} from "react-native-gesture-handler";
import React, { useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { moderateScale, verticalScale } from "react-native-size-matters";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import { iconDeleteNoti } from "../../assets/svg/svg_other";
//Components
import Images from "../../utils/imageManager";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
import { setNotificationCount } from "../../Redux_Store/action";
//Api
import {
  pusCount,
  deleteNoti,
  unReadNoti,
  pushReadAll,
  pushNotiList,
} from "../../action/Mefarm_Realtime_API";
//Translation
import { useTranslation } from "../i18n";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { setDocListPost } from "../../Redux_Store/action";

export default function Noti({ navigation, route, children }: any) {
  const orientation = useOrientation();

  const dispatch = useDispatch();
  const notificationCount = useSelector(
    (state: any) => state.notificationCount
  );
  const docListPost = useSelector((state: any) => state.docListPost);
  // console.log(docListPost);
  const { t } = useTranslation();
  //String
  const [deleteId, setDeleteId] = useState<string>("");
  //int
  const [countAll, setCountAll] = useState(0);
  const [lengthToday, setLengthToday] = useState(0);
  const [lengthDefore, setLengthDefore] = useState(0);
  const [pageSize, setPageSize] = useState<number>(10);
  //Array
  const [docToday, setDocToday] = useState<any>([]);
  const [docDefore, setDocDefore] = useState<any>([]);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isOpenToday, setOpenToday] = useState<boolean>(true);
  const [isOpenBefore, setOpenBefore] = useState<boolean>(true);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [isModalDelete, setModalDelete] = useState<boolean>(false);
  //Function
  useFocusEffect(
    React.useCallback(() => {
      callPushNotiList();
      pushNotiCount();
    }, [])
  );
  const callPushNotiList = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        farmUserPlotId: "",
        lastCreatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        pageSize: pageSize,
      };
      const res = await pushNotiList(req);
      // console.log(JSON.stringify(res, null, 2));
      const dataToday = res.model.today || [];
      const dataDefore = res.model.before || [];
      setDocToday(dataToday);
      setDocDefore(dataDefore);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const pushNotiCount = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const res = await pusCount(userIdLogin);
      const totalNotifications = res.model || "";
      // setCountAll(totalNotifications);
      // await updateNotificationCount(totalNotifications);
      // console.log("res...", data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const pushNotiReadAll = async () => {
    try {
      setLoadIng(true);
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const res = await pushReadAll(userIdLogin);
      console.log("res...", res);
      // reloadScreen();
      dispatch(setNotificationCount(0));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callReadNoti = async (item: any, index: number) => {
    try {
      setLoadIng(true);
      const notiId = item.id || "";
      const isRead = true || "";
      const navigate = item.notificationData || {};
      console.log(navigate.type);

      if (item.isRead === true) {
        if (navigate.type === "post") {
          navigation.navigate("Bottom_Tab", {
            screen: "Home",
            params: { postId: navigate.postId },
          });
        } else if (
          navigate.type === "reservation" ||
          navigate.type === "watering"
        ) {
          navigation.navigate("Bottom_Tab", {
            screen: "PlusArea",
          });
        }
      } else {
        const res = await unReadNoti(notiId, isRead);
        callPushNotiList();
        dispatch(setNotificationCount(notificationCount - 1));
        if (navigate.type === "post") {
          navigation.navigate("Bottom_Tab", {
            screen: "Home",
            params: { postId: navigate.postId },
          });
        } else if (
          navigate.type === "reservation" ||
          navigate.type === "watering"
        ) {
          navigation.navigate("Bottom_Tab", {
            screen: "PlusArea",
          });
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callDeleteNoto = async () => {
    try {
      setLoadIng(true);
      const res = await deleteNoti(deleteId);
      callPushNotiList();
      setModalDelete(false);
      reloadScreen();
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const reloadScreen = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: route.name }], // โหลดใหม่โดยใช้ชื่อ route ปัจจุบัน
    });
  };
  const openDelete = (item: any, index: number) => {
    setModalDelete(true);
    setDeleteId(item.id);
  };
  const loadMoreData = async () => {
    setLoadPosts(true);
    setPageSize((prev) => prev + 10);
    await callPushNotiList();
    setLoadPosts(false);
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_84B8A2}
      centerComponent={centerEdit()}
      backgroundImage={Images.bgApp}
    />
  );
  const centerEdit = () => (
    <Text style={[fonstStyle.f16_bold, txt.txt_white]}>{t("noti")}</Text>
  );
  const readAll = () => {
    return (
      <>
        <View
          style={{
            padding: 20,
            alignItems: "flex-end",
            backgroundColor: BgColor.Bg_FFFFFF,
          }}
        >
          <TouchableOpacity onPress={() => pushNotiReadAll()}>
            <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
              {t("readall")}
            </Text>
          </TouchableOpacity>
        </View>
        <View style={oth.line_notiNon} />
      </>
    );
  };
  const renderHeaderToday = () => {
    return (
      <>
        <View style={ctn.ctn_txtheader}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("today")}
            </Text>
            <View style={{ margin: moderateScale(5) }} />
          </View>
          {/* <TouchableOpacity onPress={() => setOpenToday(!isOpenToday)}>
            {isOpenToday === false ? iconUpNoti() : iconDownNoti()}
          </TouchableOpacity> */}
        </View>
        {/* {isOpenToday === false ? <View style={oth.line_notiNon} /> : null} */}
      </>
    );
  };
  const renderHeaderDefore = () => {
    return (
      <>
        <View style={ctn.ctn_txtheader}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("before")}
            </Text>
            <View style={{ margin: moderateScale(5) }} />
          </View>
          {/* <TouchableOpacity onPress={() => setOpenBefore(!isOpenBefore)}>
            {isOpenBefore === false ? iconUpNoti() : iconDownNoti()}
          </TouchableOpacity> */}
        </View>
        {/* {isOpenBefore === false ? <View style={oth.line_notiNon} /> : null} */}
      </>
    );
  };
  const renderRightActions = (item: any, index: number) => (
    <TouchableOpacity
      onPress={() => openDelete(item, index)}
      style={ctn.ctn_rightAction}
    >
      {iconDeleteNoti()}
    </TouchableOpacity>
  );
  const renderToday = ({ item, index }: any) => {
    return (
      <Swipeable renderRightActions={() => renderRightActions(item, index)}>
        <TouchableOpacity
          style={[ctn.ctn_contentNoti]}
          onPress={() => callReadNoti(item, index)}
        >
          <View style={ctn.ctn_imgNoti}>
            <FastImage
              style={img.img_imgNoti}
              source={Images.LogoMeFarmHug}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
          </View>
          <View style={{ margin: moderateScale(8) }} />
          <View style={ctn.ctn_txtContent}>
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationTitle}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationBody}
            </Text>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };
  const renderDefore = ({ item, index }: any) => {
    return (
      <Swipeable renderRightActions={() => renderRightActions(item, index)}>
        <TouchableOpacity
          style={[ctn.ctn_contentNoti]}
          onPress={() => callReadNoti(item, index)}
        >
          <View style={ctn.ctn_imgNoti}>
            <FastImage
              style={img.img_imgNoti}
              source={Images.LogoMeFarmHug}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={() => setLoadIng(true)}
              onLoadEnd={() => setLoadIng(false)}
            />
          </View>
          <View style={{ margin: moderateScale(8) }} />
          <View style={ctn.ctn_txtContent}>
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationTitle}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text
              style={[
                fonstStyle.f12_bold,
                item.isRead === true ? txt.txt_gray : txt.txt_606060,
              ]}
            >
              {item.notificationBody}
            </Text>
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };
  const modalDelete = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalDelete}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconDeleteNoti()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_light]}>
                {t("delete_noti")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => setModalDelete(false)}
              >
                <Text style={[fonstStyle.f12_light, txt.txt_orange]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonDelete}
                onPress={() => callDeleteNoto()}
              >
                <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <ScrollView
          nestedScrollEnabled={true}
          style={[ctn.continueMain]}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            // Destructure nativeEvent immediately to avoid synthetic event pooling issues
            const nativeEvent = event.nativeEvent;
            const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
        >
          <GestureHandlerRootView>
            {readAll()}
            {renderHeaderToday()}
            <FlatList
              data={docToday}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={10}
              updateCellsBatchingPeriod={50}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) => renderToday({ item, index })}
              getItemLayout={(data, index) => ({
                length: 80, // Approximate item height
                offset: 80 * index,
                index,
              })}
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("no_notiList")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                isLoadPosts ? (
                  <View style={{ alignItems: "center", margin: 20 }}>
                    {LoadingApp()}
                  </View>
                ) : null
              }
            />

            {renderHeaderDefore()}
            <FlatList
              data={docDefore}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={10}
              updateCellsBatchingPeriod={50}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) => renderDefore({ item, index })}
              getItemLayout={(data, index) => ({
                length: 80, // Approximate item height
                offset: 80 * index,
                index,
              })}
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("no_notiList")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                isLoadPosts ? (
                  <View style={{ alignItems: "center", margin: 20 }}>
                    {LoadingApp()}
                  </View>
                ) : null
              }
            />

            {modalDelete()}
          </GestureHandlerRootView>
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <ScrollView
          nestedScrollEnabled={true}
          style={[ctn.continueMain]}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            // Destructure nativeEvent immediately to avoid synthetic event pooling issues
            const nativeEvent = event.nativeEvent;
            const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
            const isScrolledToEnd =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;

            if (isScrolledToEnd && !isLoadPosts) {
              loadMoreData();
            }
          }}
        >
          <GestureHandlerRootView>
            {readAll()}
            {renderHeaderToday()}

            {docToday?.length > 0 ? (
              docToday.map((item: any, index: number) =>
                renderToday({ item, index })
              )
            ) : (
              <View style={{ alignItems: "center", padding: 10 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                  {t("no_notiList")}
                </Text>
              </View>
            )}
            {isLoadPosts ? (
              <>
                {LoadingApp()} <View style={{ margin: moderateScale(20) }} />
              </>
            ) : null}

            {renderHeaderDefore()}

            {docDefore?.length > 0 ? (
              docDefore.map((item: any, index: number) =>
                renderDefore({ item, index })
              )
            ) : (
              <View style={{ alignItems: "center", padding: 10 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                  {t("no_notiList")}
                </Text>
              </View>
            )}
            {isLoadPosts ? LoadingApp() : null}
            {modalDelete()}
          </GestureHandlerRootView>
          <View style={{ margin: moderateScale(120) }} />
        </ScrollView>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="light-content" hidden={false} />
      {headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
