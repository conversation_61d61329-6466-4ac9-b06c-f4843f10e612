import { StyleSheet, Text, View, StatusBar } from "react-native";
import React, { useEffect, useState } from "react";
import Default_Bar from "../../components/appBar/default_Bar";
import Loading from "../../components/loading/loading";
//Api
import { getPlantDetail } from "../../action/Mefarm_Farm_API";

export default function PlantDetail({ navigation, route }: any) {
  const params = route.params || "";
  const docDetailPlant = params.docDetailPlant || "";
  console.log("???",docDetailPlant);
  

  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>([]);

  useEffect(() => {
    // callPlantDetail();
  }, []);

  // const callPlantDetail = async () => {
  //   try {
  //     setLoadIng(true);
  //     const res = await getPlantDetail(id);
  //     const dataDetail = res.model || [];
  //     console.log(res);
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     setLoadIng(false);
  //   }
  // };

  const goBack = () => {
    navigation.goBack();
  };
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      <Default_Bar onBack={goBack} title={"test"}/>
    </>
  );
}

const styles = StyleSheet.create({});
