import {
  Text,
  View,
  Image,
  FlatList,
  StatusBar,
  TextInput,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from "react-native";
import moment from "moment";
import React, { useState, useEffect } from "react";
import { createFilter } from "react-native-search-filter";
import { moderateScale } from "react-native-size-matters";
import { MyImageComponent } from "../../components/cacheFiles/cache";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconMarket1,
  iconMarket2,
  iconMaeket3,
  iconNextArea,
} from "../../assets/svg/svg_other";
//commponents
import Images from "../../utils/imageManager";
import Loading from "../../components/loading/loading";
import LoadingApp from "../../components/loading/loadingApp";
import Null_Bar from "../../components/appBar/null_Bar";
//Translation
import { useTranslation } from "../i18n";
//Api
import { postFramPublic } from "../../action/Mefarm_Farm_API";
import { postAdminFarmPlant } from "../../action/Mefarm_Admin_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { setVisitList } from "../../Redux_Store/action";

export default function MaketFram({ navigation, route }: any) {
  //Redux
  const dispatch = useDispatch();
  const docVisitList = useSelector((state: any) => state.docVisitList);
  //State
  const { t } = useTranslation();
  //Array
  const [originalList, setOriginalList] = useState<any[]>([]);
  const [docPantList, setDocPantList] = useState<any>([]);
  //Number
  const [activeTap, setActiveTap] = useState<any>(1);
  const [activeMenu, setActiveMenu] = useState<any>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  //Ispage
  const isPages = route.name || "";

  //Function
  useEffect(() => {
    callVisit();
    callAdminPlant();
  }, [dispatch]);
  const callAdminPlant = async () => {
    try {
      setLoadIng(true);
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await postAdminFarmPlant(req);
      const plantData = response.model || "";
      setDocPantList(plantData);
      console.log(JSON.stringify(plantData, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const callVisit = async () => {
    try {
      setLoadPosts(true);
      const req = {
        searchText: "",
        pageSize: pageSize,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const res = await postFramPublic(req);
      const data = res.model || "";
      // console.log(JSON.stringify(data, null, 2));
      dispatch(setVisitList(data));
      setOriginalList(data);
    } catch (error) {
      console.error("Unexpected error:", error);
    } finally {
      setLoadPosts(false);
    }
  };
  const searchUpdated = (text: string) => {
    if (text === "") {
      // ถ้าช่อง search ว่าง กลับไปใช้ต้นฉบับ
      dispatch(setVisitList(originalList));
    } else {
      const filtered = originalList.filter(
        createFilter(text, ["farmName", "plotName"])
      );
      dispatch(setVisitList(filtered));
    }
  };
  const handleButtonMenu = (buttonIndex: any) => {
    if (activeMenu === buttonIndex) {
      setActiveMenu(null);
    } else {
      setActiveMenu(buttonIndex);
    }
  };
  const handleButtonTap = (buttonIndex: any) => {
    if (activeTap === buttonIndex) {
      setActiveTap(null);
    } else {
      setActiveTap(buttonIndex);
    }
  };
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;

    const isScrolledToEnd =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (isScrolledToEnd && !isLoadPosts) {
      // console.log("ScrollView reached the end, loading more...");
      loadMoreData();
    }
  };
  const loadMoreData = async () => {
    setLoadPosts(true);

    setPageSize((prev) => prev + 10);
    await callVisit();
  };
  const goDetail = (item: any) => {
    navigation.navigate("ManageFarmUser", {
      docDetailMyfarm: item,
      isPages: isPages,
    });
  };
  const goDetailPlant = (item: any) => {
    console.log(item);
    navigation.navigate("PlantDetail", {
      docDetailPlant: item,
    });
  };
  //FlatList
  const flasListArea = () => (
    <FlatList
      data={docVisitList}
      contentContainerStyle={{ justifyContent: "center" }}
      renderItem={renderArea}
      nestedScrollEnabled={true}
      keyExtractor={(item, index) => index.toString()}
      numColumns={2}
      onScroll={handleScroll}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={() => (
        <>
          <View style={{ margin: moderateScale(20) }} />
          {isLoadPosts ? LoadingApp() : null}
          <View style={{ margin: moderateScale(120) }} />
        </>
      )}
    />
  );

  const flasListPlant = () => (
    <FlatList
      data={docPantList}
      contentContainerStyle={{ justifyContent: "center" }}
      renderItem={renderPlant}
      nestedScrollEnabled={true}
      keyExtractor={(item, index) => index.toString()}
      numColumns={3}
      onScroll={handleScroll}
      showsVerticalScrollIndicator={false}
      // ListFooterComponent={() => (
      //   <>
      //     <View style={{ margin: moderateScale(20) }} />
      //     {isLoadPosts ? LoadingApp() : null}
      //     <View style={{ margin: moderateScale(120) }} />
      //   </>
      // )}
    />
  );

  //Ui
  const header = () => {
    return (
      <View style={ctn.ctn_menuMaket}>
        <View style={oth.cardMenuMaket}>
          <View style={[ctn.ctn_spaceBet]}>
            {/* Menu1 */}
            <TouchableOpacity
              onPress={() => handleButtonMenu(1)}
              disabled={activeMenu === 1}
              style={[
                oth.cardBottonMaket,
                {
                  backgroundColor:
                    activeMenu === 1 ? BgColor.Bg_346359 : undefined,
                },
              ]}
            >
              {iconMarket1()}
            </TouchableOpacity>

            {/* Menu2 */}
            <TouchableOpacity
              onPress={() => handleButtonMenu(2)}
              disabled={activeMenu === 2}
              style={[
                oth.cardBottonMaket,
                {
                  backgroundColor:
                    activeMenu === 2 ? BgColor.Bg_346359 : undefined,
                },
              ]}
            >
              {iconMarket2()}
            </TouchableOpacity>

            {/* Menu3 */}
            <TouchableOpacity
              onPress={() => handleButtonMenu(3)}
              disabled={activeMenu === 3}
              style={[
                oth.cardBottonMaket,
                {
                  backgroundColor:
                    activeMenu === 3 ? BgColor.Bg_346359 : undefined,
                },
              ]}
            >
              {iconMaeket3()}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };
  const contendt = () => {
    return (
      <>
        {activeMenu === 1 ? (
          <>
            <View
              style={{
                paddingHorizontal: moderateScale(20),
                marginTop: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("visit_gardan")}
              </Text>
            </View>

            {/* {ค้นหา} */}
            <View style={oth.continueSearchManage}>
              <View style={oth.contentSearchManage}>
                <TextInput
                  style={[fonstStyle.f12_light, txt.txt_606060]}
                  placeholder={t("search")}
                  onChangeText={(text) => searchUpdated(text)}
                />
              </View>
            </View>

            <View
              style={{
                flex: 1,
                justifyContent: "center",
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              {flasListArea()}
            </View>
          </>
        ) : null}
        {activeMenu === 2 ? (
          <>
            <View
              style={{
                paddingHorizontal: moderateScale(20),
                marginTop: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("plants_grown_now")}
              </Text>
            </View>

            {/* {ค้นหา} */}
            <View style={oth.continueSearchManage}>
              <View style={oth.contentSearchManage}>
                <TextInput
                  style={[fonstStyle.f12_light, txt.txt_606060]}
                  placeholder={t("search")}
                  // onChangeText={(text) => searchUpdated(text)}
                />
              </View>
            </View>

            <View
              style={{
                flex: 1,
                justifyContent: "center",
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              {flasListPlant()}
            </View>
          </>
        ) : null}
        {activeMenu === 3 ? (
          <>
            {/* selectMenu */}
            <View style={ctn.ctn_selectTapBuySell}>
              <View
                style={{
                  marginTop: moderateScale(20),
                  paddingHorizontal: moderateScale(20),
                }}
              >
                <View
                  style={[ctn.ctn_spaceBet, { marginTop: moderateScale(20) }]}
                >
                  {/* Tap1 */}
                  <TouchableOpacity
                    onPress={() => handleButtonTap(1)}
                    disabled={activeTap === 1}
                    style={[
                      oth.cardBottonTapBuySell,
                      {
                        backgroundColor:
                          activeTap === 1 ? BgColor.Bg_FFFFFF : undefined,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        fonstStyle.f14_bold,
                        activeTap === 1 ? txt.txt_606060 : txt.txt_white,
                      ]}
                    >
                      {t("seeds")}
                    </Text>
                  </TouchableOpacity>

                  {/* Tap2 */}
                  <TouchableOpacity
                    onPress={() => handleButtonTap(2)}
                    disabled={activeTap === 2}
                    style={[
                      oth.cardBottonTapBuySell,
                      {
                        backgroundColor:
                          activeTap === 2 ? BgColor.Bg_FFFFFF : undefined,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        fonstStyle.f14_bold,
                        activeTap === 2 ? txt.txt_606060 : txt.txt_white,
                      ]}
                    >
                      {t("buy-Sell")}
                    </Text>
                  </TouchableOpacity>

                  {/* Tap3 */}
                  <TouchableOpacity
                    onPress={() => handleButtonTap(3)}
                    disabled={activeTap === 3}
                    style={[
                      oth.cardBottonTapBuySell,
                      {
                        backgroundColor:
                          activeTap === 3 ? BgColor.Bg_FFFFFF : undefined,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        fonstStyle.f14_bold,
                        activeTap === 3 ? txt.txt_606060 : txt.txt_white,
                      ]}
                    >
                      {t("change")}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <ScrollView
              contentContainerStyle={{
                flex: 1,
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                  Coming soon
                </Text>
              </View>
            </ScrollView>
          </>
        ) : null}
      </>
    );
  };
  const renderArea = ({ item }: any) => {
    return (
      <>
        <View style={[oth.animated_selectArea]}>
          <View style={ctn.ctn_selectArea}>
            <MyImageComponent
              imageUrl={item.imageUrl}
              style={[
                item.imageUrl ? img.img_selectArea : img.img_selectAreaNon,
              ]}
            />
            <View style={oth.card_detailArea}>
              <View style={{ flexDirection: "column" }}>
                <Text
                  numberOfLines={2}
                  style={[
                    fonstStyle.f12_bold,
                    txt.txt_detailArea,
                    { width: moderateScale(80) },
                  ]}
                >
                  {item.plotName}
                </Text>
                <Text style={[fonstStyle.f10_light, txt.txt_detailArea]}>
                  @{item.farmName}
                </Text>
                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f10_bold, txt.txt_606060]}>
                    {item.squareMeters}{" "}
                  </Text>
                  <Text style={[fonstStyle.f10_light, txt.txt_white]}>
                    {t("Square_Meters")}
                  </Text>
                </View>
              </View>

              {/* {Next} */}
              <View style={[ctn.ctn_nextArea, { bottom: moderateScale(10) }]}>
                <TouchableOpacity
                  style={oth.card_nextArea}
                  onPress={() => goDetail(item)}
                >
                  {iconNextArea()}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </>
    );
  };
  const renderPlant = ({ item }: any) => {
    return (
      <TouchableOpacity
        style={{ alignItems: "center" }}
        onPress={goDetailPlant}
      >
        <MyImageComponent
          imageUrl={item.image.imageUrl}
          style={[item.imageUrl ? img.img_selectArea : img.img_selectAreaNon]}
        />
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {item.plantNameTh}
        </Text>
        <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
          {item.typeNameTh}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <>
      {isLoadIng ? <Loading /> : null}
      <StatusBar barStyle="dark-content" hidden={false} />
      <Null_Bar />
      <View style={[ctn.continueMain]}>
        {header()}
        {contendt()}
      </View>
    </>
  );
}
