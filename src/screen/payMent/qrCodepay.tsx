import {
  View,
  Text,
  Image,
  Alert,
  Platform,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import RNFS from "react-native-fs";
import React, { useEffect, useState } from "react";
import Clipboard from "@react-native-clipboard/clipboard";
import { moderateScale } from "react-native-size-matters";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Share from "react-native-share";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import { iconCopyText, iconSaveImages } from "../../assets/svg/svg_other";
//Api
import {
  getFarmConfirmApi,
  postRequestservice,
} from "../../action/Mefarm_Farm_API";
//Components
import { useOrientation } from "../../hooks/useOrientation";
import LoadingSave from "../../components/loading/loadingSave";
import Default_Bar from "../../components/appBar/default_Bar";
import Images from "../../utils/imageManager";
//Translation
import { useTranslation } from "../i18n";

export default function QrCodepay({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const params = route.params || "";
  const type = params.type || ""; //มาจาก Function goReturnPay จากหน้า PlusArea
  const typeRenew = params.typeRenew || ""; //มาจาก Fonction goPayRenew จากหน้า PlusArea
  const docRewnew = params.docRewnew || ""; //มาจาก Fonction goPayRenew จากหน้า PlusArea
  const pageDetailMangeFarm = params.pageDetailMangeFarm || ""; //มาจากหน้า DetailMangeFarm
  const items = params.items || ""; //มาจากหน้า DetailMangeFarm
  const sumPrice = params.sumPrice || ""; //มาจากหน้า DetailMangeFarm
  const farmUserPlotId = params.farmUserPlotId || "";
  const pageDetailDelivery = params.pageDetailDelivery || ""; //มาจากหน้า DetailDelivery
  const selectProduct = params.selectProduct || ""; //มาจากหน้า DetailDelivery
  const idAddress = params.idAddress || ""; //มาจากหน้า DetailDelivery
  const itemDocStutesPay = params.itemDocStutesPay || "";
  const AfterinvoiceNumber = params.AfterinvoiceNumber || ""; //เลข invoice ชำระภาย
  const invoiceAmount = params.invoiceAmount || "";
  const docDetailMyfarm = params.docDetailMyfarm || "";
  const paymentData = params.paymentData || "";
  const PaymentMethodId = paymentData.id || "";
  //String
  const [isPrice, setPrice] = useState<string>("");
  const [isPoltId, setPoltId] = useState<string>("");
  const [restPoltId, setRestPoltId] = useState<string>("");
  const [isPackAgeId, setPackAgeId] = useState<string>("");
  const [copiedText, setCopiedText] = useState<string>("");
  const [isStorePrice, setStorePrice] = useState<string>("");
  const [isItemNamePolt, setItemNamePolt] = useState<string>("");
  const [isProvinceName, setProvinceName] = useState<string>("");
  const [restPackAgeId, setRestPackAgeId] = useState<string>("");
  const [accountNumber, setAccountNumber] = useState<string>("");
  //Array
  const [dataPolt, setDataPolt] = useState<any>([]);
  const [dataPackage, setDataPackage] = useState<any>([]);
  const [itemAfterPay, setItemAfterPay] = useState<any>([]);
  //Null
  const [isItemImgPolt, setItemImgPolt] = useState<any>(null);
  //True & False
  const [isLoadIng, setLoadIng] = useState<boolean>(false);

  //Function;
  useEffect(() => {
    const afterPayData = async () => {
      const params = route.params || "";
      const itemAfterPay = params.itemAfterPay || "";
      const packagePrice = itemAfterPay.packagePrice || "";
      const packageId = itemAfterPay.packageId || "";
      const poltId = itemAfterPay.id || "";
      setStorePrice(packagePrice);
      setRestPackAgeId(packageId);
      setRestPoltId(poltId);
      setItemAfterPay(itemAfterPay);
      // console.log("itemAfterPay", itemAfterPay);
    };
    afterPayData();
  }, []);
  useEffect(() => {
    const params = route.params || "";
    //PackAge
    const isDataPackage = params.isDataPackage || "";
    const packPrice = isDataPackage.price || "";
    const packAgeId = isDataPackage.id || "";
    setPrice(packPrice);
    setPackAgeId(packAgeId);
    setDataPackage(isDataPackage);
    // console.log(JSON.stringify(isDataPackage, null, 2));

    //Polt
    const isDataPolt = params.isDataPolt || "";
    const poltId = isDataPolt.id || "";
    setPoltId(poltId);
    setDataPolt(isDataPolt);

    //Whatever
    const itemImgPolt = params.itemImgPolt || "";
    const itemNamePolt = params.itemNamePolt || "";
    const itemProvinceName = params.itemProvinceName || "";
    setItemImgPolt(itemImgPolt);
    setItemNamePolt(itemNamePolt);
    setProvinceName(itemProvinceName);
  }, []);
  const returnType = async () => {
    navigation.goBack();
  };
  const copyToClipboard = (text: string) => {
    console.log(text, accountNumber);
    Clipboard.setString(text);
    setCopiedText(text);
  };

  //Go to
  const goBack = () => {
    navigation.navigate("Bottom_Tab", {
      screen: "PlusArea",
    });
  };
  const goAfter = async () => {
    if (pageDetailMangeFarm === "DetailMangeFarm") {
      try {
        setLoadIng(true);
        const req = {
          farmUserPlotId: farmUserPlotId,
          masterPaymentMethodId: PaymentMethodId,
          details: items,
        };
        const response = await postRequestservice(req);
        console.log(JSON.stringify(response, null, 2));
        if (response?.success) {
          navigation.navigate("ManageFarmUser", {
            docDetailMyfarm: docDetailMyfarm,
          });
        } else {
          Alert.alert("แจ้ง", "ข้อพลาด ลองใหม่ครั้ง", [
            { text: "OK", onPress: () => console.log("OK Pressed") },
          ]);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    } else if (pageDetailDelivery === "DetailDelivery") {
      // goSusseed();
      navigation.navigate("ManageFarmUser", {
        docDetailMyfarm: docDetailMyfarm,
      });
    } else {
      try {
        setLoadIng(true);
        const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
        const req = {
          userId: userIdLogin,
          farmPackageId: isPackAgeId,
          farmPlotId: isPoltId,
          masterPaymentMethodId: PaymentMethodId,
        };

        const response = await getFarmConfirmApi(req);
        const reservationId = response.model || "";
        await AsyncStorage.setItem(
          "reservation",
          JSON.stringify(reservationId)
        );
        if (response?.success) {
          // goSusseed();
          goBack();
        } else {
          console.log("error");
          await AsyncStorage.removeItem("reservation");
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoadIng(false);
      }
    }
  };
  const goPayMent = async () => {
    navigation.navigate("UpLoadpay", {
      isPoltId: isPoltId || "",
      isPackAgeId: isPackAgeId || "",
      payMentId: PaymentMethodId || "",
      farmUserPlotId: farmUserPlotId || "",
      PaymentMethodId: PaymentMethodId || "",
      docRewnew: docRewnew || "", //ต่อ
      typeRenew: typeRenew || "", //Type ต่อ
      itemAfterPay: itemAfterPay || "", //จ่ายค่า
      itemDocManagePay: items || "", //จ่ายค่า
      itemSelectProduct: selectProduct || "", //จ่ายค่าเก็บผล
      itemDocStutesPay: itemDocStutesPay || "", //จ่ายค่า
      AfterinvoiceNumber: AfterinvoiceNumber || "",
      idAddress: idAddress || "", //ส่ง
      docDetailMyfarm: docDetailMyfarm || "",
    });
  };

  const saveImageAlternative = async (imageUrl: string) => {
    try {
      console.log("Starting to save image from URL:", imageUrl);

      // ดาวน์โหลดภาพจาก URL
      const filename = "qrcode_" + new Date().getTime() + ".jpg";
      const localFilePath = `${RNFS.CachesDirectoryPath}/${filename}`;

      console.log("Will download to local path:", localFilePath);

      // ดาวน์โหลดไฟล์และรอให้เสร็จสิ้น
      const downloadResult = await RNFS.downloadFile({
        fromUrl: imageUrl,
        toFile: localFilePath,
        background: true,
        discretionary: true,
        progressDivider: 10,
        progressInterval: 1000,
      }).promise;

      // console.log("Download result:", downloadResult);

      // ตรวจสอบว่าดาวน์โหลดสำเร็จหรือไม่
      if (downloadResult.statusCode !== 200) {
        throw new Error(
          `Download failed with status code: ${downloadResult.statusCode}`
        );
      }

      // ตรวจสอบว่าไฟล์อยู่หรือไม่
      const exists = await RNFS.exists(localFilePath);
      // console.log("File exists:", exists);

      if (!exists) {
        throw new Error("Downloaded file does not exist");
      }

      // ตรวจสอบขนาดไฟล์
      const fileStats = await RNFS.stat(localFilePath);
      // console.log("File stats:", fileStats);

      if (fileStats.size <= 0) {
        throw new Error("Downloaded file is empty");
      }

      // สร้าง URI ที่ต้องแชร์
      const fileUri =
        Platform.OS === "android" ? `file://${localFilePath}` : localFilePath;

      console.log("File URI for sharing:", fileUri);

      // ใช้ Share API
      const shareOptions = {
        title: "Save QR Code",
        url: fileUri,
        type: "image/jpeg",
        failOnCancel: false,
      };

      console.log("Share options:", shareOptions);

      const result = await Share.open(shareOptions);
      console.log("Share result:", result);

      if (!result.dismissedAction) {
        Alert.alert("สำเร็จ", "บันทึกรูปภาพแล้ว");
      }
    } catch (error) {
      console.log("Error in alternative save:", error);
      Alert.alert("ข้อพลาด", `ไม่สามารถึกรูปภาพได้: ${error}`);
    }
  };

  //Ui
  const content = () => {
    return (
      <>
        <ScrollView>
          <View style={{ alignItems: "center", marginTop: moderateScale(10) }}>
            <View style={ctn.ctn_account}>
              <Text style={[fonstStyle.f14_bold, txt.txt_fixText]}>
                {t("bank")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {paymentData.methodName}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <Text style={[fonstStyle.f14_medium, txt.txt_fixText]}>
                {t("Account_Name")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {paymentData.description}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <View style={ctn.ctn_coppy}>
                <Text style={[fonstStyle.f14_medium, txt.txt_fixText]}>
                  {t("Account_Number")}
                </Text>

                <TouchableOpacity
                  onPress={() => copyToClipboard(paymentData.accountNumber)}
                  disabled={paymentData.accountNumber === null}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      opacity: paymentData.accountNumber === null ? 0.5 : 1,
                    }}
                  >
                    {iconCopyText()}
                    <View style={{ margin: moderateScale(2) }} />

                    <Text style={[fonstStyle.f10_bold, txt.txt_fixText]}>
                      {copiedText ? t("Copied") : t("Copy_account_number")}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View style={{ margin: moderateScale(5) }} />

              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {paymentData.accountNumber || "-"}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <Text style={[fonstStyle.f14_medium, txt.txt_fixText]}>
                {t("Payment_Amount")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />

              <View style={[ctn.ctn_spaceBet, { width: "100%" }]}>
                <Text style={[fonstStyle.f16_bold, txt.txt_green]}>
                  ฿{" "}
                  {isPrice ||
                    isStorePrice ||
                    docRewnew.packagePrice ||
                    sumPrice ||
                    itemDocStutesPay.invoiceAmount ||
                    invoiceAmount}
                </Text>

                {paymentData.detailImageUrl != null && (
                  <TouchableOpacity
                    style={btn.btn_saveQr}
                    onPress={() =>
                      saveImageAlternative(paymentData.detailImageUrl)
                    }
                  >
                    <View style={ctn.ctn_saveQr}>
                      {iconSaveImages()}
                      <Text style={[fonstStyle.f10_bold, txt.txt_white, {}]}>
                        {t("save_qrCode")}
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <Image
              style={[img.img_qrcode]}
              source={{ uri: paymentData.detailImageUrl }}
              resizeMode="contain"
            />
          </View>
          <View style={{ margin: "20%" }} />
        </ScrollView>

        {/* {bottom} */}
        <View style={ctn.ctn_bottomQrPay}>
          <View style={{ flexDirection: "row" }}>
            {/* {!pageDetailDelivery && ( */}
            <TouchableOpacity
              onPress={() => (type || typeRenew ? returnType() : goAfter())}
              style={btn.btn_paylater}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_green]}>
                {t("Pay_later")}
              </Text>
            </TouchableOpacity>
            {/* )} */}
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity
              onPress={() => goPayMent()}
              style={btn.btn_goUpload}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_white]}>
                {t("next")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        {isLoadIng ? <LoadingSave /> : null}
        <View style={{ flex: 1 }}>{content()}</View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        {isLoadIng ? <LoadingSave /> : null}
        <View style={{ flex: 1 }}>{content()}</View>
      </SafeAreaView>
    );
  };
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      <Default_Bar onBack={() => navigation.goBack()} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
