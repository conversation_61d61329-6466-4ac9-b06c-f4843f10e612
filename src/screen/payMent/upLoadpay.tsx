import React, { useEffect, useState } from "react";
import RNFS from "react-native-fs";
import {
  View,
  Text,
  Image,
  Alert,
  StatusBar,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
//Svg
import { iconDeletepay, iconUploadPay } from "../../assets/svg/svg_other";
//StyleSheet
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Api
import {
  renewalApi,
  postHarvest,
  postRequestPaid,
  registerFarmApi,
  getFarmConfirmApi,
  postServiceManage,
  postRequestservice,
} from "../../action/Mefarm_Farm_API";
//Components
import { useOrientation } from "../../hooks/useOrientation";
import LoadingSave from "../../components/loading/loadingSave";
import Default_Bar from "../../components/appBar/default_Bar";
//Translation
import { useTranslation } from "../i18n";
import { moderateScale } from "react-native-size-matters";

export default function UpLoadpay({ navigation, route }: any) {
  const orientation = useOrientation();
  const { t } = useTranslation();

  const [imageUri, setImageUri] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);

  const [isReservaId, setReservaId] = useState<string>("");
  const [isPoltStore, setPoltStore] = useState<string>("");
  const [isFarmStore, setFarmStore] = useState<string>("");
  const [isLoadIng, setLoadIng] = useState<boolean>(false);

  const params = route.params || "";
  const isPackAgeId = params.isPackAgeId || "";
  const isPoltId = params.isPoltId || "";
  const payMentId = params.payMentId || "";

  //After Pay
  const itemAfterPay = params.itemAfterPay || "";
  const packageId = itemAfterPay.packageId || "";
  const farmPlotId = itemAfterPay.farmPlotId || "";
  const id = itemAfterPay.id || "";

  //Renew Pay
  const typeRenew = params.typeRenew || "";
  const docRewnew = params.docRewnew || "";
  const invoiceNo = docRewnew.invoiceNo || "";
  const farmUserPackageId = docRewnew.farmUserPackageId || "";
  const masterPaymentMethodId = docRewnew.masterPaymentMethodId || "";

  //Manage Pay
  const itemDocManagePay = params.itemDocManagePay || "";

  //SelectProduct Pay
  const itemSelectProduct = params.itemSelectProduct || "";
  const idAddress = params.idAddress || "";
  // console.log(">>>", itemSelectProduct);

  //After Manage Pay
  const itemDocStutesPay = params.itemDocStutesPay || "";
  const AfterinvoiceNumber = params.AfterinvoiceNumber || "";
  // console.log('???', itemDocStutesPay);

  const farmUserPlotId = params.farmUserPlotId || "";
  const PaymentMethodId = params.PaymentMethodId || "";

  const docDetailMyfarm = params.docDetailMyfarm || "";
  //Function
  useEffect(() => {
    const storeData = async () => {
      const reservationData = (await AsyncStorage.getItem("reservation")) || "";
      // console.log("???", reservationData);

      if (reservationData) {
        const parsedData = JSON.parse(reservationData);
        const reservationId = parsedData.farmPlotReservationId || "";
        const poltStoreId = parsedData.farmPlotId || "";
        const farmStoreId = parsedData.farmPackageId || "";

        setReservaId(reservationId);
        setPoltStore(poltStoreId);
        setFarmStore(farmStoreId);
      } else {
        // console.log("error");
      }
    };
    storeData();
  });
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const selectImageFromLibrary = () => {
    launchImageLibrary(
      { mediaType: "photo", selectionLimit: 1 },
      (response: any) => {
        if (!response.didCancel) {
          if (response.assets && response.assets.length > 0) {
            const asset = response.assets[0];
            setImageUri(asset.uri);
            setFileName(asset.fileName);
            setFileType(asset.type);
          } else if (response.uri) {
            setImageUri(response.uri);
            setFileName(response.fileName);
            setFileType(response.type);
          }
        }
      }
    );
  };
  const onDelete = async () => {
    setImageUri(null);
    setFileName(null);
    setFileType(null);
  };
  const goBack = () => {
    // navigation.popToTop();
    navigation.navigate("Bottom_Tab", {
      screen: "PlusArea",
    });
  };
  //Go to
  const goSave = async () => {
    try {
      setLoadIng(true);
      //reservation
      const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
      const req = {
        userId: userIdLogin,
        farmPackageId: isPackAgeId,
        farmPlotId: isPoltId,
      };
      const response = await getFarmConfirmApi(req);
      const reservationId = response.model || "";
      const farmPlotReservationId = reservationId.farmPlotReservationId || "";

      //Go save pay
      const base64Data = imageUri ? await convertImageToBase64(imageUri) : "";
      const obj = {
        farmPackageId: packageId || isPackAgeId,
        farmPlotId: farmPlotId || isPoltId,
        farmPlotReservationId: id || farmPlotReservationId,
        masterPaymentMethodId: payMentId,
        imagesFiles: [
          {
            fileName: fileName || "unknown",
            contentType: fileType || "image/jpeg",
            base64Data: base64Data || "string",
          },
        ],
      };

      let data = await registerFarmApi(obj);
      console.log("data....", data);
      if (data?.success) {
        await AsyncStorage.removeItem("reservation");
        goBack();
      } else {
        console.log("error");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const goRenewal = async () => {
    try {
      setLoadIng(true);
      const base64Data = imageUri ? await convertImageToBase64(imageUri) : "";
      const obj = {
        invoiceNumber: invoiceNo,
        farmUserPackageId: farmUserPackageId,
        masterPaymentMethodId: masterPaymentMethodId,
        imagesFiles: [
          {
            fileName: fileName || "unknown",
            contentType: fileType || "image/jpeg",
            base64Data: base64Data || "string",
          },
        ],
      };
      // console.log("obj", obj);
      const response = await renewalApi(obj);
      if (response?.success) {
        // goSusseed();
        goBack();
        // console.log(JSON.stringify(response, null, 2));
      } else {
        Alert.alert("เกิดข้อผิดพลาด");
      }
      // navigation.navigate("PlusArea");
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const goManage = async () => {
    try {
      setLoadIng(true);
      const req = {
        farmUserPlotId: farmUserPlotId,
        masterPaymentMethodId: PaymentMethodId,
        details: itemDocManagePay,
      };
      const response = await postRequestservice(req);
      const resData = response.model || "";
      const invoiceNumber = resData.invoiceNumber || "";
      // console.log(JSON.stringify(response, null, 2));

      //Go save Manage pay
      const base64Data = imageUri ? await convertImageToBase64(imageUri) : "";

      const obj = {
        invoiceNumber: invoiceNumber || AfterinvoiceNumber,
        imagesFiles: [
          {
            fileName: fileName || "unknown",
            contentType: fileType || "image/jpeg",
            base64Data: base64Data || "string",
          },
        ],
      };
      let data = await postServiceManage(obj);
      if (data?.success) {
        console.log("data....", data);
        // navigation.navigate("ManageFarmUser");
        navigation.navigate("ManageFarmUser", {
          docDetailMyfarm: docDetailMyfarm,
        });
      } else {
        console.log("error");
        // navigation.navigate("ManageFarmUser", {
        //   docDetailMyfarm: docDetailMyfarm,
        // });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };
  const goProduct = async () => {
    try {
      setLoadIng(true);
      const req = {
        farmUserPlotId: farmUserPlotId,
        addressBookId: idAddress,
        details: itemSelectProduct,
      };
      const response = await postHarvest(req);
      const resData = response.model || "";
      const invoiceNumber = resData.invoiceNumber || "";
      // console.log(JSON.stringify(response, null, 2));

      //Go save Product pay
      const base64Data = imageUri ? await convertImageToBase64(imageUri) : "";
      const obj = {
        invoiceNumber: invoiceNumber || AfterinvoiceNumber,
        masterPaymentMethodId: payMentId,
        imagesFiles: [
          {
            fileName: fileName || "unknown",
            contentType: fileType || "image/jpeg",
            base64Data: base64Data || "string",
          },
        ],
      };

      let data = await postRequestPaid(obj);
      console.log(JSON.stringify(data, null, 2));
      if (data?.success) {
        console.log("data....", data);
        // navigation.navigate("ManageFarmUser");
        // goSusseed();
        navigation.navigate("ManageFarmUser", {
          docDetailMyfarm: docDetailMyfarm,
        });
      } else {
        console.log("error");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
    }
  };

  //Ui
  const content = () => {
    return (
      <ScrollView>
        <View style={{ alignItems: "center", marginTop: moderateScale(10) }}>
          <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
            {t("Please_attach_proof_of_payment_or_transfer_slip")}
          </Text>

          {imageUri ? (
            <>
              <Image
                style={img.img_bankUpload}
                source={{ uri: imageUri }}
                resizeMode="contain"
              />
              {/* {Delete Botton} */}
              <TouchableOpacity
                onPress={() => onDelete()}
                style={ctn.ctn_imgBankDelete}
              >
                <View style={btn.btn_imgBankDelete}>{iconDeletepay()}</View>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity
              onPress={() => selectImageFromLibrary()}
              style={btn.btn_uploadImgBank}
            >
              {iconUploadPay()}
              <Text style={[fonstStyle.f14_bold, txt.txt_qrCode]}>
                {t("Tap_to_attach_evidence")}
              </Text>
            </TouchableOpacity>
          )}
        </View>
         <View style={{ margin: "20%" }} />
      </ScrollView>
    );
  };
  const bottonNext = () => {
    const isSingleFreeItem =
      itemDocManagePay.length === 1 && itemDocManagePay[0].isFree;
    return (
      <>
        {imageUri ? (
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              style={[btn.btn_detailPay]}
              onPress={() =>
                typeRenew === "typeRenew"
                  ? goRenewal()
                  : itemDocManagePay != "" || itemDocStutesPay != ""
                  ? goManage()
                  : itemSelectProduct != "" || itemDocStutesPay != ""
                  ? goProduct()
                  : goSave()
              }
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_editSave]}>
                {t("Confirm_payment")}
              </Text>
            </TouchableOpacity>
          </View>
        ) : null}
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        {isLoadIng ? <LoadingSave /> : null}

        <View style={{ flex: 1 }}>{content()}</View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        {isLoadIng ? <LoadingSave /> : null}
        <View style={{ flex: 1 }}>{content()}</View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      <Default_Bar onBack={() => navigation.goBack()} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
      {bottonNext()}
    </>
  );
}
