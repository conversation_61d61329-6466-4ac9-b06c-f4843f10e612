import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import Default_Bar from "../../components/appBar/default_Bar";
import { postAdminChatList } from "../../action/Mefarm_Admin_API";
import moment from "moment";
import { moderateScale, verticalScale } from "react-native-size-matters";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Translation
import { useTranslation } from "../i18n";

export default function ChatHistoryAdmin({ navigation }: any) {
  const { t } = useTranslation();
  const [docList, setDocList] = useState<any>([]);

  useEffect(() => {
    callAdminChatList();
  }, []);

  const callAdminChatList = async () => {
    try {
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const res = await postAdminChatList(req);
      const data = res.model || "";
      console.log(JSON.stringify(data, null, 2));
      setDocList(data);
    } catch (error) {
      console.log(error);
    }
  };

  const goChat = (type: string, item: any) => {
    const plotList = item.plotList.map((plot: any) => plot.id).join(",");
    // const plotList = item.plotList.map((plot: any) => plot);
    console.log(plotList, type);
    navigation.navigate("ChatSystem", { type: type, plotList: plotList });
  };

  const FlatListChat = () => (
    <FlatList
      data={docList}
      removeClippedSubviews={false}
      maxToRenderPerBatch={10}
      windowSize={8}
      initialNumToRender={10}
      updateCellsBatchingPeriod={50}
      keyExtractor={(item, index) => index.toString()}
      renderItem={renderChat}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={() => (
        <View style={{ margin: moderateScale(120) }} />
      )}
    />
  );

  const renderChat = ({ item, index }: any) => {
    return (
      <TouchableOpacity
        onPress={() => goChat("AdminFram", item)}
        style={{
          marginTop: moderateScale(10),
          paddingHorizontal: moderateScale(20),
        }}
      >
        <View
          style={{
            backgroundColor: BgColor.Bg_F2FFF5,
            padding: 10,
            borderRadius: 8,
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {item.farmName}
          </Text>
          <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
            {item.firstname}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const goBack = () => {
    navigation.goBack();
  };
  return (
    <>
      <Default_Bar onBack={goBack} title={t("list/plots")} />
      {FlatListChat()}
    </>
  );
}

const styles = StyleSheet.create({});
