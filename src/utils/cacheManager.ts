import { clearImageMemory, clearImageDiskCache, getImageCacheSize } from '../components/cacheFiles/cache';
import { clearVideoCache, getVideoCacheSize } from '../components/cacheFiles/cacheVideo';

class CacheManager {
  private static instance: CacheManager;
  private lastCleanup = Date.now();
  private readonly CLEANUP_INTERVAL = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_TOTAL_CACHE_SIZE = 700 * 1024 * 1024; // 700MB total
  private cleanupInterval: NodeJS.Timeout | null = null;

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  // ตรวจสอบและทำความสะอาด cache
  async performRoutineCleanup() {
    const now = Date.now();
    if (now - this.lastCleanup < this.CLEANUP_INTERVAL) {
      return;
    }

    console.log('🧹 CacheManager: Performing routine cleanup');
    
    try {
      const totalSize = await this.getTotalCacheSize();
      console.log(`📊 Total cache size: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);

      if (totalSize > this.MAX_TOTAL_CACHE_SIZE) {
        console.log('⚠️ Cache size exceeded limit, performing cleanup');
        await this.performAggressiveCleanup();
      } else {
        // Routine memory cleanup
        clearImageMemory();
      }

      this.lastCleanup = now;
    } catch (error) {
      console.error('Error during routine cleanup:', error);
    }
  }

  // ทำความสะอาดแบบเข้มข้น
  async performAggressiveCleanup() {
    console.log('🚨 CacheManager: Performing aggressive cleanup');
    
    try {
      // Clear memory caches first
      clearImageMemory();
      
      // Clear disk caches
      await clearImageDiskCache();
      await clearVideoCache();
      
      // Force garbage collection
      if (global.gc) {
        global.gc();
      }

      console.log('✅ Aggressive cleanup completed');
    } catch (error) {
      console.error('Error during aggressive cleanup:', error);
    }
  }

  // ได้ขนาด cache ทั้งหมด
  async getTotalCacheSize(): Promise<number> {
    try {
      const [imageSize, videoSize] = await Promise.all([
        getImageCacheSize(),
        getVideoCacheSize(),
      ]);
      return imageSize + videoSize;
    } catch (error) {
      console.error('Error getting total cache size:', error);
      return 0;
    }
  }

  // ได้สถิติ cache
  async getCacheStats() {
    try {
      const [imageSize, videoSize] = await Promise.all([
        getImageCacheSize(),
        getVideoCacheSize(),
      ]);
      
      const totalSize = imageSize + videoSize;
      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
      const imageSizeMB = (imageSize / 1024 / 1024).toFixed(2);
      const videoSizeMB = (videoSize / 1024 / 1024).toFixed(2);
      
      return {
        totalSize,
        totalSizeMB: `${totalSizeMB}MB`,
        imageSize,
        imageSizeMB: `${imageSizeMB}MB`,
        videoSize,
        videoSizeMB: `${videoSizeMB}MB`,
        isOverLimit: totalSize > this.MAX_TOTAL_CACHE_SIZE,
        limitMB: `${(this.MAX_TOTAL_CACHE_SIZE / 1024 / 1024).toFixed(0)}MB`,
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return null;
    }
  }

  // เริ่มต้น auto cleanup
  startAutoCleanup() {
    // Clear existing interval if any
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(() => {
      this.performRoutineCleanup();
    }, this.CLEANUP_INTERVAL);

    console.log('🔄 CacheManager: Auto cleanup started');
  }

  // หยุด auto cleanup
  stopAutoCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('🛑 CacheManager: Auto cleanup stopped');
    }
  }

  // ตรวจสอบ memory warning
  onMemoryWarning() {
    console.warn('⚠️ CacheManager: Memory warning received');
    this.performAggressiveCleanup();
  }

  // Clear cache ทั้งหมดทันที
  async clearAllCaches() {
    console.log('🗑️ CacheManager: Clearing all caches');
    await this.performAggressiveCleanup();
  }
}

export default CacheManager;
