import AsyncStorage from "@react-native-async-storage/async-storage";
import { MMKV } from "react-native-mmkv";
import { jwtDecode } from "jwt-decode";
import { getRefreshToken } from "../action/Mefarm_Identity_API";

// สร้าง MMKV instance
const storage = new MMKV();

//  token และ userId
export const getAuthTokens = async () => {
  try {
    const tokenLogin = (await AsyncStorage.getItem("tokenLogin")) || "";
    const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
    console.log(tokenLogin);
    
    return { tokenLogin, userIdLogin };
  } catch (error) {
    console.error("Error getting auth tokens:", error);
    return { tokenLogin: "", userIdLogin: "" };
  }
};

// ตรวจสอบและเฟรช token
export const refreshAuthTokenIfNeeded = async (navigation?: any) => {
  try {
    const tokenLogin = (await AsyncStorage.getItem("tokenLogin")) || "";
    if (!tokenLogin) {
      return { success: false, message: "No token found" };
    }

    // ตรวจสอบว่า token หมด
    const decodedToken = jwtDecode<{ exp: number }>(tokenLogin);
    const currentTime = Date.now() / 1000;

    // ถ้า token ไม่หมด ให้ getRefreshToken
    if (decodedToken.exp > currentTime) {
      // ทำการเฟรช token
      const response = await getRefreshToken();
      if (!response || !response.model) {
        return { success: false, message: "Failed to refresh token" };
      }

      const newToken = response?.model?.tokenInfo?.access_token || "";
      if (newToken) {
        await AsyncStorage.setItem("tokenLogin", newToken);
        return { success: true, token: newToken };
      }

      return { success: false, message: "No new token received" };
    } else {
      // ถ้า token หมด ให้ logout
      console.log("Token expired, logging out");
      await logout(navigation);
      return { success: false, message: "Token expired, logged out" };
    }
  } catch (error) {
    console.error("Error checking token:", error);
    return { success: false, message: String(error) };
  }
};

// ล้าง token และออกจากระบบ
export const logout = async (navigation: any) => {
  try {
    await storage.delete("successLogin");
    await AsyncStorage.removeItem("userIdLogin");
    await AsyncStorage.removeItem("tokenLogin");
    await AsyncStorage.removeItem("fcmToken");
    await AsyncStorage.removeItem("editAddress");
    await AsyncStorage.removeItem("userRoles");
    await AsyncStorage.removeItem("bookMark");
    await AsyncStorage.removeItem("bookMarkState");

    if (navigation) {
      navigation.navigate("Select_login");
    }

    return { success: true };
  } catch (error) {
    console.error("Error during logout:", error);
    return { success: false, message: String(error) };
  }
};

//  hook ใช้ใน component
export const useAuth = () => {
  return {
    getTokens: getAuthTokens,
    refreshToken: refreshAuthTokenIfNeeded,
    logout,
  };
};
