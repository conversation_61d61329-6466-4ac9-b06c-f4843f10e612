import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
//Api
import { API_ROOT } from "../constants/api";
import { refreshToken } from "../action/Mefarm_Identity_API";

axios.interceptors.request.use(async (config) => {
  const accessToken = await AsyncStorage.getItem("tokenLogin");
  // console.log("accessToken 2 => ", accessToken);

  // if (accessToken != null && !config.url.includes('api/identity/auth')) {
  if (accessToken != null) {
    config.headers = { Authorization: `Bearer ${accessToken}` };
  }
  return config;
});

export const axiosInterceptors = axios;

// import axios from "axios";
// import AsyncStorage from "@react-native-async-storage/async-storage";
// // ✅ Import API ใช้ต่ออายุ Token
// import { API_ROOT } from "../constants/api";
// import { getRefreshToken } from "../action/Mefarm_Identity_API";

// // ✅ สร้าง Axios Instance
// const api = axios.create({ baseURL: API_ROOT });

// // ✅ Interceptor ก่อนส่ง Request (แนบ Token)
// api.interceptors.request.use(async (config) => {
//   const accessToken = await AsyncStorage.getItem("tokenLogin");
//   if (accessToken) {
//     config.headers["Authorization"] = `Bearer ${accessToken}`;
//   }
//   return config;
// });

// // ✅ Interceptor หลังรับ Response (Handle 401)
// api.interceptors.response.use(
//   (response) => response, // ✅ ถ้าสำเร็จ return ปกติ
//   async (error) => {
//     if (error.response?.status === 401) {
//       console.warn("⚠️ Token expired, trying to refresh...");

//       const oldRefreshToken = await AsyncStorage.getItem("refreshToken");
//       if (!oldRefreshToken) {
//         console.error("❌ No refresh token found, forcing logout.");
//         await AsyncStorage.multiRemove(["tokenLogin", "refreshToken"]);
//         return Promise.reject(error);
//       }

//       try {
//         // 🔄 ขอ Access Token ใหม่จาก API
//         const response = await getRefreshToken(oldRefreshToken);
//         const newAccessToken = response?.data?.accessToken;
//         const newRefreshToken = response?.data?.refreshToken; // ✅ อาจมี Refresh Token ใหม่

//         if (newAccessToken) {
//           await AsyncStorage.setItem("tokenLogin", newAccessToken);
//           if (newRefreshToken) {
//             await AsyncStorage.setItem("refreshToken", newRefreshToken);
//           }

//           // 🔁 Retry คำขอเดิมอีกครั้ง
//           error.config.headers["Authorization"] = `Bearer ${newAccessToken}`;
//           return api.request(error.config);
//         } else {
//           throw new Error("Failed to get new access token");
//         }
//       } catch (refreshError) {
//         console.error("❌ Failed to refresh token:", refreshError);
//         await AsyncStorage.multiRemove(["tokenLogin", "refreshToken"]);
//         return Promise.reject(refreshError);
//       }
//     }

//     return Promise.reject(error);
//   }
// );

// export const axiosInterceptors = api;


