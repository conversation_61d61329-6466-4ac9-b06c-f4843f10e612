import {
  <PERSON>g<PERSON>evel,
  Hu<PERSON><PERSON>onnection,
  HubConnectionBuilder,
  HubConnectionState,
} from "@microsoft/signalr";
import { Dispatch } from "redux";
import {
  addNewPost,
  setDocProfile,
  setDocListPost,
  updatePostLikes,
  setFollowConter,
  setDocListFollow,
  setDocListProfile,
  setDocListFriend,
  updateMemberFollowStatus,
} from "../Redux_Store/action";
import { WEBSOCKET_ROOT } from "../constants/api";

// Singleton connection & cleanup
let globalConnection: HubConnection | null = null;
let globalCleanup: (() => void) | null = null;

// Memory management constants
const MAX_POSTS_PER_PAGE = 100;
const MAX_MEMBERS_PER_PAGE = 200;
const MAX_MESSAGE_QUEUE = 50;

// Message queue for memory management
let messageQueue: any[] = [];
let messageCount = 0;

// Memory monitoring utility
const performMemoryCleanup = () => {
  messageCount++;
  if (messageCount > 100) {
    console.log("SignalR: Performing routine memory cleanup");
    messageCount = 0;

    // Clear old messages from queue
    if (messageQueue.length > MAX_MESSAGE_QUEUE) {
      messageQueue = messageQueue.slice(-MAX_MESSAGE_QUEUE);
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }
};

export const setupSignalRConnection = async (
  dispatch: Dispatch<any>,
  onConnectionEstablished: (connection: HubConnection) => void,
  options: {
    page?: "home" | "profile" | "friend" | "all";
    onFollowCounterReceived?: (data: any) => void;
    forceReconnect?: boolean;
  } = {}
): Promise<{ connection: HubConnection; cleanup: () => void }> => {
  const { page = "all", onFollowCounterReceived, forceReconnect = false } = options;

  // Reuse connection if exists and not forceReconnect
  if (
    !forceReconnect &&
    globalConnection &&
    globalConnection.state === HubConnectionState.Connected
  ) {
    onConnectionEstablished(globalConnection);
    return { connection: globalConnection, cleanup: globalCleanup! };
  }

  // If forceReconnect or no connection, cleanup old one
  if (globalCleanup) {
    globalCleanup();
  }

  // Create new connection
  const newConnection = new HubConnectionBuilder()
    .withUrl(WEBSOCKET_ROOT)
    .withAutomaticReconnect()
    .configureLogging(LogLevel.Information)
    .build();

  // Event Listeners with memory management
  newConnection.on("ReceiveLoadMoreMemberMessage", (data) => {
    if (data) {
      // จำกัดจำนวน members เพื่อประหยัด RAM
      const limitedData = Array.isArray(data) ? data.slice(0, MAX_MEMBERS_PER_PAGE) : data;
      dispatch(setDocListFollow(limitedData));

      // Memory cleanup check
      performMemoryCleanup();
    }
  });

  newConnection.on("ReceiveUpdateMemberMessage", (data) => {
    dispatch(updateMemberFollowStatus(data.userId, data.isFollowing));
  });

  newConnection.on("ReceiveLoadMorePostMessage", (data) => {
    if (!data) return;

    // จำกัดจำนวน posts เพื่อประหยัด RAM
    const limitedData = Array.isArray(data) ? data.slice(0, MAX_POSTS_PER_PAGE) : data;

    if (page === "home") dispatch(setDocListPost(limitedData));
    if (page === "profile") dispatch(setDocListProfile(limitedData));
    if (page === "friend") dispatch(setDocListFriend(limitedData));

    // Memory management
    performMemoryCleanup();
  });

  newConnection.on("ReceiveUpdatePostMessage", (data) => {
    dispatch(updatePostLikes(data.id, data.likes));
  });

  newConnection.on("ReceiveNewPostMessage", (data) => {
    if (!data) return;

    if (page === "home") {
      dispatch(addNewPost(data));
    }
    if (page === "profile") {
      dispatch(setDocListProfile((prevPosts: any) => {
        const newPosts = [data, ...prevPosts];
        // จำกัดจำนวน posts ใน memory
        return newPosts.slice(0, MAX_POSTS_PER_PAGE);
      }));
    }
    if (page === "friend") {
      dispatch(setDocListFriend((prevPosts: any) => {
        const newPosts = [data, ...prevPosts];
        // จำกัดจำนวน posts ใน memory
        return newPosts.slice(0, MAX_POSTS_PER_PAGE);
      }));
    }

    // Track message queue for memory management
    messageQueue.push(data);
    if (messageQueue.length > MAX_MESSAGE_QUEUE) {
      messageQueue = messageQueue.slice(-MAX_MESSAGE_QUEUE);
    }
  });

  newConnection.on("ReceiveFollowCounterMessage", (data: any) => {
    if (data) dispatch(setFollowConter(data));
    if (onFollowCounterReceived) onFollowCounterReceived(data);
  });

  newConnection.on("ReceiveProfileImageMessage", (data: any) => {
    if (data) dispatch(setDocProfile(data));
  });

  // Handle connection error/reconnect
  newConnection.onclose((error) => {
    console.warn("SignalR connection closed", error);
    // Optionally: แจ้งเตือน user หรือพยายาม reconnect เพิ่มเติม
  });

  newConnection.onreconnecting((error) => {
    console.warn("SignalR reconnecting...", error);
  });

  newConnection.onreconnected((connectionId) => {
    console.log("SignalR reconnected", connectionId);
  });

  try {
    await newConnection.start();
    console.log("SignalR connection established.");

    onConnectionEstablished(newConnection);

    const cleanup = () => {
      // ลบ event listeners ทั้งหมด
      newConnection.off("ReceiveLoadMoreMemberMessage");
      newConnection.off("ReceiveUpdateMemberMessage");
      newConnection.off("ReceiveLoadMorePostMessage");
      newConnection.off("ReceiveUpdatePostMessage");
      newConnection.off("ReceiveNewPostMessage");
      newConnection.off("ReceiveFollowCounterMessage");
      newConnection.off("ReceiveProfileImageMessage");

      // ปิด connection และ cleanup memory
      newConnection.stop().catch(() => {});

      // Clear global references และ message queue
      globalConnection = null;
      globalCleanup = null;
      messageQueue = [];
      messageCount = 0;

      // Force garbage collection hint
      if (global.gc) {
        global.gc();
      }

      console.log("SignalR: Connection cleaned up and memory freed");
    };

    globalConnection = newConnection;
    globalCleanup = cleanup;

    return { connection: newConnection, cleanup };
  } catch (err) {
    // cleanup ถ้า error
    if (globalCleanup) globalCleanup();
    throw err;
  }
};

// ฟังก์ชันสำหรับเข้าร่วมห้อง
export const joinSignalRRoom = async (
  connection: HubConnection,
  roomData: {
    roomId: string;
    followerUserId: string;
    accessToken: string;
    followingUserId: string | null;
    isDescending: boolean;
    firstPageSize: number;
    sendType: string;
  },
  onLoadingStart?: () => void,
  onLoadingEnd?: () => void
) => {
  if (!connection || connection.state !== "Connected") {
    return false;
  }

  try {
    if (onLoadingStart) onLoadingStart();
    await connection.invoke("JoinRoom", roomData);
    return true;
  } catch (err) {
    return false;
  } finally {
    if (onLoadingEnd) onLoadingEnd();
  }
};

// Export utility functions for manual memory management
export const forceSignalRCleanup = () => {
  if (globalCleanup) {
    globalCleanup();
  }
  messageQueue = [];
  messageCount = 0;
  if (global.gc) {
    global.gc();
  }
  console.log("SignalR: Manual cleanup performed");
};

export const getSignalRMemoryStats = () => {
  return {
    messageQueueSize: messageQueue.length,
    messageCount,
    connectionState: globalConnection?.state || "Disconnected",
    hasActiveConnection: !!globalConnection,
  };
};
