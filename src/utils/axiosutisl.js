import { API_TIMEOUT, TIMEOUT } from "../constants/api";
import { axiosInterceptors } from "../utils/httpClient";

export const doGet = (url) => {
  return axiosInterceptors
    .get(url, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      // return onError(error, "get", url);
      console.log("error>>>", error);
      //throw error
    });
};export const doGetAllImg = (url) => {
  return axiosInterceptors
    .get(url, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      // return onError(error, "get", url);
      console.log("error>>>", error);
      //throw error
    });
};
export const doGetPackage = (url) => {
  return axiosInterceptors
    .get(url, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "get", url);
      //throw error
    });
};

export const getDasBoard = (url) => {
  return axiosInterceptors
    .get(url, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "get", url);
      //throw error
    });
};

export const doPost = (url, request) => {
  return axiosInterceptors
    .post(url, request, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "post", url, request);
      // throw error
    });
};

export const doPostFarm = (url) => {
  const dateTime = new Date();
  return axiosInterceptors
    .post(url, dateTime, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "post", url, request);
      // throw error
    });
};

export const doPostFiles = (url, formData) => {
  return axiosInterceptors({
    method: "post",
    url: url,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: formData,
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return onError(error, "postFiles", url, request);
      // throw error
    });
};

export const doPostJson = (url, request) => {
  return axiosInterceptors({
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/json",
    },
    data: request,
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "postJson", url, request);
      // throw error
    });
};

export const doPut = (url, request) => {
  return axiosInterceptors
    .put(url, request, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "put", url, request);
      // throw error
    });
};

export const doPutFiles = (url, request) => {
  return axiosInterceptors({
    method: "put",
    url: url,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: request,
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "putFiles", url, request);
      // throw error
    });
};

export const doDelete = (url) => {
  return axiosInterceptors
    .delete(url, API_TIMEOUT)
    .then((response) => {
      return response.status;
    })
    .catch((error) => {
      return onError(error, "delete", url);
      // throw error
    });
};

export const doDeleteData = (url) => {
  return axiosInterceptors
    .delete(url, API_TIMEOUT)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "delete", url);
      // throw error
    });
};

export const doDeleteBody = (url, request) => {
  return axiosInterceptors({
    method: "delete",
    url: url,
    headers: {
      "Content-Type": "application/json",
    },
    data: request,
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "deleteBody", url, request);
      // throw error
    });
};

export const doDownload = (url) => {
  return axiosInterceptors({
    url: url,
    method: "get",
    responseType: "blob",
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "download", url);
      // throw error
    });
};

export const doPostDownload = (url, request) => {
  return axiosInterceptors({
    url: url,
    method: "post",
    responseType: "blob",
    data: request,
    timeout: TIMEOUT,
  })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      return onError(error, "postDownload", url, request);
      // throw error
    });
};

const onError = async (error, method, url, request) => {
  // const params_code = localStorage.getItem("params_code");
  // const params_state = localStorage.getItem("params_state");
  // console.log('onError')
  // console.log('url', url)
  // console.log('method', method)
  // console.log('request', request)
  if (
    params_code &&
    params_state &&
    error.response &&
    error.response.status === 401
  ) {
    const response = await authen(params_code, params_state);

    // console.log('authen response', response)

    if (response.success && response?.model?.tokenInfo?.access_token) {
      // clearLocalStorage()
      // setSession(response.model)
      // setLocalStorage(response.model?.userInfo, params_code, params_state)

      await new Promise((resolve) => setTimeout(resolve, 2000));

      switch (method) {
        case "get":
          return doGet(url);
        // break
        case "post":
          return doPost(url, request);
        // break
        case "postFiles":
          return doPostFiles(url, request);
        // break
        case "postJson":
          return doPostJson(url, request);
        // break
        case "put":
          return doPut(url, request);
        // break
        case "putFiles":
          return doPutFiles(url, request);
        // break
        case "delete":
          return doDelete(url);
        // break
        case "deleteBody":
          return doDeleteBody(url, request);
        // break
        case "download":
          return doDownload(url);
        // break
        case "postDownload":
          return doPostDownload(url, request);
        // break
        default:
          break;
      }
    } else {
      throw error;
    }
  } else {
    throw error;
  }
};
