import NetInfo from "@react-native-community/netinfo";
import { HubConnection } from "@microsoft/signalr";
import { refreshAuthTokenIfNeeded } from "./authManager";

export const setupNetworkListener = (
  isFocused: boolean,
  connection: HubConnection | null,
  joinRoomFunction: () => Promise<void>,
  navigation: any
) => {
  const handleNetworkChange = async (state: any) => {
    if (state.isConnected && isFocused) {
      try {
        // ตรวจสอบและเฟรช token ก่อนเชื่อมต่อ SignalR
        const refreshResult = await refreshAuthTokenIfNeeded(navigation);
        
        if (refreshResult.success) {
          // ถ้า token ใช้งานได้ ให้เชื่อมต่อ SignalR
          if (connection?.state !== "Connected") {
            try {
              await connection?.start();
              // console.log("SignalR connection established after network change");
            } catch (err) {
              console.error("Failed to reconnect to SignalR:", err);
            }
          }
          
          // เข้าร่วมห้องเชื่อมต่อสำเร็จ
          await joinRoomFunction();
        } else {
          console.log("Token refresh failed, cannot connect to SignalR");
        }
      } catch (error) {
        console.error("Error during network reconnection:", error);
      }
    }
  };
  const unsubscribe = NetInfo.addEventListener(handleNetworkChange);
  return () => {
    unsubscribe();
  };
};
