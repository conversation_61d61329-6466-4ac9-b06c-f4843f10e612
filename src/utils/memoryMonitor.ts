import { AppState, AppStateStatus } from 'react-native';
import Cache<PERSON>anager from './cacheManager';
import { forceSignalRCleanup, getSignalRMemoryStats } from './signalRManager';

class MemoryMonitor {
  private static instance: MemoryMonitor;
  private cacheManager: CacheManager;
  private memoryWarningCount = 0;
  private lastMemoryCheck = Date.now();
  private readonly MEMORY_CHECK_INTERVAL = 30 * 1000; // 30 seconds
  private appStateSubscription: any = null;
  private memoryCheckInterval: NodeJS.Timeout | null = null;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  // เริ่มต้น memory monitoring
  startMonitoring() {
    console.log('🔍 MemoryMonitor: Starting memory monitoring');
    
    // Monitor app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
    
    // Start periodic memory checks
    this.memoryCheckInterval = setInterval(() => {
      this.performMemoryCheck();
    }, this.MEMORY_CHECK_INTERVAL);

    // Start cache manager auto cleanup
    this.cacheManager.startAutoCleanup();
  }

  // หยุด memory monitoring
  stopMonitoring() {
    console.log('🛑 MemoryMonitor: Stopping memory monitoring');
    
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
  }

  // Handle app state changes
  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    console.log(`📱 App state changed to: ${nextAppState}`);
    
    switch (nextAppState) {
      case 'background':
        this.onAppBackground();
        break;
      case 'active':
        this.onAppForeground();
        break;
      case 'inactive':
        // App is transitioning between foreground & background
        break;
    }
  };

  // เมื่อ app เข้า background
  private onAppBackground() {
    console.log('📱 App entering background - performing cleanup');
    this.performAggressiveCleanup();
  }

  // เมื่อ app กลับมา foreground
  private onAppForeground() {
    console.log('📱 App returning to foreground');
    this.performMemoryCheck();
  }

  // ตรวจสอบ memory usage
  private async performMemoryCheck() {
    const now = Date.now();
    if (now - this.lastMemoryCheck < this.MEMORY_CHECK_INTERVAL) {
      return;
    }

    console.log('🔍 MemoryMonitor: Performing memory check');
    
    try {
      // Get cache stats
      const cacheStats = await this.cacheManager.getCacheStats();
      const signalRStats = getSignalRMemoryStats();
      
      console.log('📊 Memory Stats:', {
        cache: cacheStats,
        signalR: signalRStats,
        memoryWarnings: this.memoryWarningCount,
      });

      // Check if cleanup is needed
      if (cacheStats && cacheStats.isOverLimit) {
        console.log('⚠️ Cache over limit, performing cleanup');
        await this.cacheManager.performAggressiveCleanup();
      }

      // Check SignalR message queue
      if (signalRStats.messageQueueSize > 30) {
        console.log('⚠️ SignalR message queue large, performing cleanup');
        forceSignalRCleanup();
      }

      this.lastMemoryCheck = now;
    } catch (error) {
      console.error('Error during memory check:', error);
    }
  }

  // ทำความสะอาดแบบเข้มข้น
  private async performAggressiveCleanup() {
    console.log('🚨 MemoryMonitor: Performing aggressive cleanup');
    
    try {
      // Clear all caches
      await this.cacheManager.clearAllCaches();
      
      // Clear SignalR
      forceSignalRCleanup();
      
      // Force garbage collection
      if (global.gc) {
        global.gc();
      }

      console.log('✅ Aggressive cleanup completed');
    } catch (error) {
      console.error('Error during aggressive cleanup:', error);
    }
  }

  // Handle memory warnings
  onMemoryWarning() {
    this.memoryWarningCount++;
    console.warn(`⚠️ Memory warning #${this.memoryWarningCount}`);
    
    // Perform immediate cleanup
    this.performAggressiveCleanup();
    
    // If too many warnings, be more aggressive
    if (this.memoryWarningCount > 3) {
      console.error('🚨 Multiple memory warnings - performing emergency cleanup');
      this.performEmergencyCleanup();
    }
  }

  // Emergency cleanup
  private async performEmergencyCleanup() {
    console.log('🆘 MemoryMonitor: Emergency cleanup');
    
    try {
      // Clear everything aggressively
      await this.cacheManager.clearAllCaches();
      forceSignalRCleanup();
      
      // Multiple GC calls
      if (global.gc) {
        global.gc();
        setTimeout(() => global.gc && global.gc(), 1000);
        setTimeout(() => global.gc && global.gc(), 2000);
      }

      // Reset warning count after emergency cleanup
      this.memoryWarningCount = 0;
      
      console.log('✅ Emergency cleanup completed');
    } catch (error) {
      console.error('Error during emergency cleanup:', error);
    }
  }

  // Get current memory stats
  async getMemoryStats() {
    try {
      const cacheStats = await this.cacheManager.getCacheStats();
      const signalRStats = getSignalRMemoryStats();
      
      return {
        cache: cacheStats,
        signalR: signalRStats,
        memoryWarnings: this.memoryWarningCount,
        lastCheck: new Date(this.lastMemoryCheck).toISOString(),
        isMonitoring: !!this.memoryCheckInterval,
      };
    } catch (error) {
      console.error('Error getting memory stats:', error);
      return null;
    }
  }

  // Manual cleanup trigger
  async performManualCleanup() {
    console.log('🧹 MemoryMonitor: Manual cleanup requested');
    await this.performAggressiveCleanup();
  }
}

export default MemoryMonitor;
