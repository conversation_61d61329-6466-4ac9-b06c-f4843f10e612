export const setAccessToken = (data) => ({
  type: "SET_ACCESS_TOKEN",
  payload: data,
});

export const setUserId = (data) => ({
  type: "SET_USER_ID",
  payload: data,
});

export const setDocListFollow = (data) => ({
  type: "SET_DOC_LIST_FOLLOW",
  payload: Array.isArray(data) ? data : [],
});

export const setDocListPost = (data) => ({
  type: "SET_DOC_LIST_POST",
  payload: Array.isArray(data) ? data : [],
});

export const setDocListProfile = (data) => ({
  type: "SET_DOC__LIST_PROFILE",
  payload: Array.isArray(data) ? data : [],
});

export const setDocListFriend = (data) => ({
  type: "SET_DOC__LIST_FRIEND",
  payload: Array.isArray(data) ? data : [],
});

export const setFollowConter = (data) => ({
  type: "SET_FOLLOW_COUNTER",
  payload: data,
}); 

export const setDocProfile = (data) => ({
  type: "SET_DOC_PROFILE",
  payload: data,
});

export const setDocAllImage = (data) => ({
  type: "SET_DOC_ALL_IMAGE",
  payload: Array.isArray(data) ? data : [],
});
export const setDocAllVideo = (data) => ({
  type: "SET_DOC_ALL_VIDEO",
  payload: Array.isArray(data) ? data : [],
});

// likes
export const updatePostLikes = (postId, likes) => ({
  type: "UPDATE_POST_LIKES",
  payload: { id: postId, likes },
});

export const updateMemberFollowStatus = (userId, isFollowing) => ({
  type: "UPDATE_MEMBER_FOLLOW_STATUS",
  payload: { id: userId, isFollowing },
});

export const addNewPost = (data) => ({
  type: "ADD_NEW_POST",
  payload: data,
});

export const setNotificationCount = (count) => ({
  type: "SET_NOTIFICATION_COUNT",
  payload: Number(count),
});

export const setNotificationCountFram = (count) => ({
  type: "SET_NOTIFICATION_COUNT_FRAM",
  payload: count,
});

export const setRegionData = (region) => ({
  type: "SET_REGION_DATA",
  payload: region,
});

export const setPolygonData = (polygon) => ({
  type: "SET_POLYGON_DATA",
  payload: polygon,
});

export const setPolygonsPlanting = (polygons) => ({
  type: "SET_POLYGON_PLANTING",
  payload: polygons,
});

export const setRegionPlot = (regionPlot) => ({
  type: "SET_REGION_PLOT",
  payload: regionPlot,
});

export const setPolygonPlot = (polygonPlot) => ({
  type: "SET_POLYGON_PLOT",
  payload: polygonPlot,
});

export const setRequest = (data) => ({
  type: "SET_REQUEST_APPROVE",
  payload: data,
});

export const setDocPayment = (data) => ({
  type: "SET_PAYMENT_APPROVE",
  payload: data,
});

export const setDocHistoryPayment = (data) => ({
  type: "SET_PAYMENT_HISTORY",
  payload: data,
});

export const setDocHistoryRequest = (data) => ({
  type: "SET_REQUEST_HISTORY",
  payload: data,
});

export const setDocHistoryProcess = (data) => ({
  type: "SET_PROCESS_HISTORY",
  payload: data,
});

export const setDocProcessList = (data) => ({
  type: "SET_PROCESS_LIST",
  payload: data,
});

export const setDocProcessDetail = (data) => ({
  type: "SET_PROCESS_DETAIL",
  payload: data,
});

export const setLanguageKey = (data) => ({
  type: "SET_LANGUAFE_KEY",
  payload: data,
});

export const setVisitList = (data) => ({
  type: "SET_VISIT_LIST",
  payload: data,
});

//ManageFarmUser
export const setDocMyFarm = (data) => ({
  type: "SET_DOC_MY_FARM",
  payload: data,
});
export const setDocMyFarmList = (data) => ({
  type: "SET_DOC_MY_FARM_LIST",
  payload: data,
});
export const setDocReserVation = (data) => ({
  type: "SET_DOC_RESERVATION",
  payload: data, 
});
export const setRenewalList = (data) => ({
  type: "SET_RENEWAL_LIST",
  payload: data,
});
export const setDocPolt = (data) => ({
  type: "SET_DOC_POLT",
  payload: data,
});
export const setPaymentMethods = (data) => ({
  type: "SET_PAYMENT_METHODS",
  payload: data,
});

export const setListFram = (data) => ({
  type: "SET_LIST_FRAM",
  payload: data,
});

export const setFarmUserPlotId = (data) => ({
  type: "SET_FARM_USER_PLOT_ID",
  payload: data,
});
export const setImagesShow = (data) => ({
  type: "SET_IMAGES_SHOW",
  payload: data,
});

export const setSelectedFarm = (farm) => {
  return {
    type: "SET_SELECTED_FARM",
    payload: farm,
  };
};
export const setFarmDetail = (farm) => {
  return {
    type: "SET_FARM_DETAIL",
    payload: farm,
  };
};
export const setJoinRoomAdmin = (data) => {
  return {
    type: "SET_JOINROOM_ADMIN",
    payload: data,
  };
};
export const setDocSeedList = (data) => {
  return {
    type: "SET_DOCSEED_LIST",
    payload: data,
  };
};
