// reducer.js
const initialState = {
  docListPost: [],
  docListFriend: [],
  docListFollow: [],
  docListProfile: [],
  notificationCount: 0,
  notificationCountFram: 0,
  region: {
    latitude: 0,
    longitude: 0,
    latitudeDelta: 0.0005,
    longitudeDelta: 0.0005,
  },
  polygon: [],
  polygons: [],
  regionPlot: {
    latitude: 0,
    longitude: 0,
    latitudeDelta: 0.0005,
    longitudeDelta: 0.0005,
  },
  polygonPlot: [],
  docRequest: [],
  docPayment: [],
  docProcessList: [],
  docProcessDetail: [],
  docPaymentHistory: [],
  docRequestHistory: [],
  docProcessHistory: [],
  docVisitList: [],
  docMyFarm: [],
  docListFram: [],
  docMyFarmList: [],
  docReserVation: [],
  docRenewalList: [],
  setDocPolt: [],
  docAllImage: [],
  docAllVideo: [],
  imagesShow: [],
  paymentMethods: [],
  farmDetail: [],
  farmUserPlotId: "",
  docLanguageKey: "",
  accessToken: "",
  userId: "",
  followCounter: {
    post: 0,
    followers: 0,
    following: 0,
  },
  selectedFarm: null,
  joinRoomAdmin: {},
  docSeedlist: [],
};

const rootReducer = (state = initialState, action) => {
  switch (action.type) {
    case "SET_ACCESS_TOKEN":
      return { ...state, accessToken: action.payload };
    case "SET_USER_ID":
      return { ...state, userId: action.payload };

    case "SET_DOC_LIST_FOLLOW":
      return { ...state, docListFollow: action.payload };

    case "SET_DOC_LIST_POST":
      return { ...state, docListPost: action.payload };

    case "SET_DOC__LIST_PROFILE":
      return { ...state, docListProfile: action.payload };

    case "SET_DOC__LIST_FRIEND":
      return { ...state, docListFriend: action.payload };

    case "SET_FOLLOW_COUNTER":
      return { ...state, followCounter: action.payload };

    case "SET_DOC_PROFILE":
      return { ...state, docProfile: action.payload };

    case "SET_DOC_ALL_IMAGE":
      return { ...state, docAllImage: action.payload };
    case "SET_DOC_ALL_VIDEO":
      return { ...state, docAllVideo: action.payload };

    case "UPDATE_LIKE_COUNT":
      return {
        ...state,
        docListPost: state.docListPost.map((post) =>
          post.id === action.payload.id
            ? {
                ...post,
                numberOfLike: action.payload.numberOfLike,
                isLike: action.payload.isLike,
              }
            : post
        ),
      };

    case "UPDATE_MEMBER_FOLLOW_STATUS":
      return {
        ...state,
        docListFollow: state.docListFollow.map((member) =>
          member.userId === action.payload.userId
            ? {
                ...member,
                numberOfFollower: action.payload.numberOfFollower,
                isFollower: action.payload.isFollowing,
              }
            : member
        ),
      };

    case "ADD_NEW_POST":
      // ตรวจสอบว่ามีโพสต์ที่มี id เดียวกันอยู่แล้วหรือไม่
      if (state.docListPost.some((post) => post.id === action.payload.id)) {
        return state;
      }
      return { ...state, docListPost: [action.payload, ...state.docListPost] };

    case "SET_NOTIFICATION_COUNT":
      return { ...state, notificationCount: action.payload };

    case "SET_NOTIFICATION_COUNT_FRAM":
      return {
        ...state,
        notificationCountFram: action.payload,
      };

    case "SET_REGION_DATA":
      return { ...state, region: action.payload };

    case "SET_POLYGON_DATA":
      return { ...state, polygon: action.payload };

    case "SET_POLYGON_PLANTING":
      return { ...state, polygons: action.payload };

    case "SET_REGION_PLOT":
      return { ...state, regionPlot: action.payload };

    case "SET_POLYGON_PLOT":
      return { ...state, polygonPlot: action.payload };

    case "SET_REQUEST_APPROVE":
      return { ...state, docRequest: action.payload };

    case "SET_PAYMENT_APPROVE":
      return { ...state, docPayment: action.payload };

    case "SET_PAYMENT_HISTORY":
      return { ...state, docPaymentHistory: action.payload };

    case "SET_REQUEST_HISTORY":
      return { ...state, docRequestHistory: action.payload };

    case "SET_PROCESS_HISTORY":
      return { ...state, docProcessHistory: action.payload };

    case "SET_PROCESS_LIST":
      return { ...state, docProcessList: action.payload };

    case "SET_PROCESS_DETAIL":
      return { ...state, docProcessDetail: action.payload };

    case "SET_LANGUAFE_KEY":
      return { ...state, docLanguageKey: action.payload };

    case "SET_VISIT_LIST":
      return { ...state, docVisitList: action.payload };

    //ManageFarmUser
    case "SET_DOC_MY_FARM":
      return { ...state, docMyFarm: action.payload };
    case "SET_DOC_MY_FARM_LIST":
      return { ...state, docMyFarmList: action.payload };
    case "SET_DOC_RESERVATION":
      return { ...state, docReserVation: action.payload };
    case "SET_RENEWAL_LIST":
      return { ...state, docRenewalList: action.payload };
    case "SET_DOC_POLT":
      return { ...state, docPolt: action.payload };
    case "SET_PAYMENT_METHODS":
      return { ...state, paymentMethods: action.payload };

    case "SET_LIST_FRAM":
      return { ...state, docListFram: action.payload };
    case "SET_FARM_USER_PLOT_ID":
      return { ...state, farmUserPlotId: action.payload };
    case "SET_IMAGES_SHOW":
      return { ...state, imagesShow: action.payload };

    case "SET_SELECTED_FARM":
      return {
        ...state,
        selectedFarm: action.payload,
      };
    case "SET_FARM_DETAIL":
      return {
        ...state,
        farmDetail: action.payload,
      };
    case "SET_DOCSEED_LIST":
      return {
        ...state,
        docSeedlist: action.payload,
      };

    //Chat
    case "SET_JOINROOM_ADMIN":
      return { ...state, joinRoomAdmin: action.payload };

    default:
      return state;
  }
};

export default rootReducer;
