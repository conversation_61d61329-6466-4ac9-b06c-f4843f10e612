import RNFS from "react-native-fs";
import React, { useEffect, useState } from "react";
import FastImage, { FastImageProps } from "react-native-fast-image";

// เพิ่ม function สำหรับ clear memory
export const clearImageMemory = () => {
  FastImage.clearMemoryCache();
  FastImage.clearDiskCache();
};

// เพิ่ม function สำหรับ clear disk cache
export const clearImageDiskCache = async () => {
  try {
    const exists = await RNFS.exists(CACHE_DIR);
    if (exists) {
      await RNFS.unlink(CACHE_DIR);
      console.log('Image disk cache cleared');
    }
  } catch (error) {
    console.error('Error clearing image disk cache:', error);
  }
};

// เพิ่ม function สำหรับ get cache size
export const getImageCacheSize = async (): Promise<number> => {
  try {
    return await getTotalCacheSize();
  } catch (error) {
    console.error('Error getting cache size:', error);
    return 0;
  }
};

const CACHE_DIR = `${RNFS.CachesDirectoryPath}/image-cache`;
const MAX_CACHE_SIZE = 200 * 1024 * 1024; // ลดจาก 2GB เป็น 200MB เพื่อประหยัด RAM

async function ensureCacheDirExists() {
  const exists = await RNFS.exists(CACHE_DIR);
  if (!exists) {
    await RNFS.mkdir(CACHE_DIR);
  }
}

function getCacheKeyFromUrl(url: string | null | undefined): string {
  if (!url) return "noimage";
  const path = url.split("?")[0];
  return path.replace(/[^a-zA-Z0-9]/g, "");
}

async function getTotalCacheSize(): Promise<number> {
  const files = await RNFS.readDir(CACHE_DIR);
  return files.reduce((total, file) => total + file.size, 0);
}

async function deleteOldestFilesUntilUnderLimit() {
  const files = await RNFS.readDir(CACHE_DIR);
  const sorted = files.sort((a, b) => {
    const timeA = a.mtime?.getTime() ?? 0;
    const timeB = b.mtime?.getTime() ?? 0;
    return timeA - timeB;
  });

  let total = await getTotalCacheSize();
  for (const file of sorted) {
    if (total <= MAX_CACHE_SIZE) break;
    // console.log(`🗑️ Remove from cache: ${file.name} (${file.size} bytes)`);
    await RNFS.unlink(file.path);
    total -= file.size;
  }
}

export async function downloadAndCacheImage(
  url: string | null | undefined
): Promise<string> {
  if (!url) throw new Error("Image URL is required");
  await ensureCacheDirExists();

  const cacheKey = getCacheKeyFromUrl(url);
  const cachePath = `${CACHE_DIR}/${cacheKey}.jpg`;

  const exists = await RNFS.exists(cachePath);
  // console.log(`Downloaded and cached: ${exists} | url: ${url} | cachePath: ${cachePath}`);
  // console.log(`cacheKey: ${cacheKey} | url: ${url}`);
  if (exists) {
    return `file://${cachePath}`;
  }

  try {
    await RNFS.downloadFile({
      fromUrl: url,
      toFile: cachePath,
    }).promise;

    await deleteOldestFilesUntilUnderLimit();
  } catch (err) {
    throw err;
  }

  return `file://${cachePath}`;
}

// เพิ่มฟังก์ชันสำหรับวิดีโอ
export async function downloadAndCacheVideo(
  url: string | null | undefined
): Promise<string> {
  if (!url) throw new Error("Video URL is required");
  await ensureCacheDirExists();

  const cacheKey = getCacheKeyFromUrl(url);
  // เปลี่ยนนามสกุลไฟล์เป็น .mp4 หรือ .mov ตามประเภทวิดีโอจริง
  const cachePath = `${CACHE_DIR}/${cacheKey}.mp4`;

  const exists = await RNFS.exists(cachePath);
  // console.log(`Downloaded and cached video: ${exists} | url: ${url} | cachePath: ${cachePath}`);
  if (exists) {
    return `file://${cachePath}`;
  }

  try {
    await RNFS.downloadFile({
      fromUrl: url,
      toFile: cachePath,
    }).promise;

    await deleteOldestFilesUntilUnderLimit();
  } catch (err) {
    throw err;
  }

  return `file://${cachePath}`;
}

// Component สำหรับรูป
export const MyImageComponent = ({
  imageUrl,
  style,
  resizeMode = FastImage.resizeMode.cover,
}: {
  imageUrl: string | null | undefined;
  style?: FastImageProps["style"];
  resizeMode?: FastImageProps["resizeMode"];
}) => {
  const [localPath, setLocalPath] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    if (!imageUrl) return;

    const loadImage = async () => {
      try {
        const path = await downloadAndCacheImage(imageUrl);
        if (mounted) setLocalPath(path);
      } catch (error) {
        console.error("Image load failed", error);
        // Fallback to original URL if cache fails
        if (mounted) setLocalPath(imageUrl);
      }
    };

    loadImage();

    return () => {
      mounted = false;
      // Clear local path to free memory
      setLocalPath(null);
    };
  }, [imageUrl]);

  if (!imageUrl) return null;

  return localPath ? (
    <FastImage
      source={{
        uri: localPath,
        priority: FastImage.priority.normal,
      }}
      style={style}
      resizeMode={resizeMode}
    />
  ) : null;
};


