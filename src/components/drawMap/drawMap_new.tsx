import {
  View,
  Text,
  Alert,
  Image,
  Modal,
  FlatList,
  StatusBar,
  StyleSheet,
  Dimensions,
  PanResponder,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
} from "react-native";
import moment from "moment";
import React, { useState, useRef, useMemo, useEffect } from "react";
import FastImage from "react-native-fast-image";
import Svg, { Path, Circle } from "react-native-svg";
import { goBack_Bg } from "../../assets/svg/svg_naviagte";
import { moderateScale, verticalScale } from "react-native-size-matters";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
//Style
import btn from "../../styleSheet/btn";
import oth from "../../styleSheet/oth";
import txt from "../../styleSheet/txt";
import mod from "../../styleSheet/mod";
import fonstStyle, {
  BgColor,
  BgOpacity,
  LgColor,
  linearDis,
  linearSky,
  linearPink,
  lineOrange,
  linearGreen,
  linearPurple,
  linearDanger,
} from "../../styleSheet/style_Custom";
import {
  iconNoImg,
  iconPlant,
  iconCheckArea,
  iconPlusPlant,
  iconDeleteFarm,
  iconScreenClose,
} from "../../assets/svg/svg_other";
import img from "../../styleSheet/img";
import ctn from "../../styleSheet/ctn";
import Images from "../../utils/imageManager";
import LinearGradient from "react-native-linear-gradient";
import { useDispatch, useSelector } from "react-redux";
//Api
import { seedApi, plantingApi } from "../../action/Mefarm_Farm_API";
//Translation
import { useTranslation } from "../../screen/i18n";

type Point = { x: number; y: number };

const DrawingApp = ({ navigation, route, farmUserPlotId }: any) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const reduxFarmUserPlotId = useSelector((state: any) => state.farmUserPlotId);
  //Route
  const params = route.params || "";
  const docList = params.docList || {};
  const blocks = docList.blocks || [];
  const imageUrl = docList.imageUrl || "";
  
  const [idSelectPlans, setSelectPlans] = useState<string>("");
  const [selectPlansName, setSelectPlansName] = useState<string>("");
  //Array
  const [paths, setPaths] = useState<Point[][]>([]);
  const [positions, setPositions] = useState<Point[]>([]);
  const [docSeedlist, setDocSeedList] = useState<any>([]);
  const [currentPath, setCurrentPath] = useState<Point[]>([]);
  const [docSelectPlans, setDocSelectPlans] = useState<any>([]);
  const [lastPositions, setLastPositions] = useState<Point[]>([]);
  //Null
  const [imageUrlPlans, setImageUrlPlans] = useState<any>(null);
  //True & false
  const [openSheet, setOpenSheet] = useState<boolean>(false);
  const [modalSaveArea, setModalSaveArea] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const pathsRef = useRef(paths);
  const positionsRef = useRef(positions);
  const { width: screenWidth, height: screenHeight } = Dimensions.get("window");
  const newSize = docList.size || { x: 1000, y: 1000 };
  const imageWidth = (screenWidth * newSize.x) / 1000;
  const imageHeight = (screenHeight * newSize.y) / 1000;

  const bottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ["50%", "80%"], []);

  // ฟังก์ชันแปลง x, y จากระบบ 1000x1000 ไปเป็นตำแหน่งบนหน้าจอ
  const toScreenX = (x: number) => (x / newSize.x) * screenWidth;
  const toScreenY = (y: number) => (y / newSize.y) * screenHeight;

  // ฟังก์ชันแปลง x, y จาก screen coordinates กลับเป็นระบบ 1000x1000
  const toNormalizedX = (x: number) => (x / screenWidth) * newSize.x;
  const toNormalizedY = (y: number) => (y / screenHeight) * newSize.y;

  useEffect(() => {
    pathsRef.current = paths;
  }, [paths]);
  useEffect(() => {
    positionsRef.current = positions;
  }, [positions]);
  useEffect(() => {
    callSeed();
  }, []);
  useEffect(() => {
    if (farmUserPlotId) {
      dispatch({ type: "SET_FARM_USER_PLOT_ID", payload: farmUserPlotId });
    }
  }, [farmUserPlotId, dispatch]);

  const callSeed = async () => {
    try {
      setIsLoading(true);
      const req = {
        searchText: "",
        pageSize: 10,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await seedApi(req);
      const seedData = response.model || "";
      setDocSeedList(seedData);
    } catch (error) {
      console.error("Error loading seed data:", error);
      Alert.alert("Error", "Failed to load plant data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const FlasListAreaPlant = () => (
    <FlatList
      data={docSeedlist}
      renderItem={renderSelectPlans}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      numColumns={3}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={8}
      initialNumToRender={10}
      updateCellsBatchingPeriod={50}
      ListFooterComponent={() => (
        <View style={{ margin: moderateScale(300) }} />
      )}
    />
  );

  const selectPlans = (item: any) => {
    setDocSelectPlans(item);
    setSelectPlans(item.id);
    setSelectPlansName(item.plantName);
    setImageUrlPlans(item.imageUrl);
  };

  const cleanDocSelect = () => {
    setDocSelectPlans([]);
    setSelectPlans("");
    setSelectPlansName("");
    setImageUrlPlans(null);
  };

  const handleCleanArea = () => {
    setPaths([]);
    setCurrentPath([]);
    setPositions([]);
    setDocSelectPlans([]);
    setSelectPlans("");
    setSelectPlansName("");
    setImageUrlPlans(null);
  };

  const handleFinishDraw = () => {
    setOpenSheet(true);
  };

  const handleSavePolygon = () => {
    setModalSaveArea(true);
  };

  const handleSaveData = async () => {
    try {
      // Validation
      if (!reduxFarmUserPlotId) {
        Alert.alert("Error", "Farm User Plot ID is required");
        return;
      }

      if (!idSelectPlans || !selectPlansName) {
        Alert.alert("Error", "Please select a plant first");
        return;
      }

      if (!lastPositions || lastPositions.length === 0) {
        Alert.alert("Error", "Please draw an area first");
        return;
      }

      // ตรวจสอบว่า polygon มีจุดอย่างน้อย 3 จุด
      if (lastPositions.length < 3) {
        Alert.alert("Error", "Polygon must have at least 3 points");
        return;
      }

      setIsLoading(true);
      
      // แปลง lastPositions จาก screen coordinates เป็น normalized coordinates (1000x1000)
      let normalizedBlocks = lastPositions.map(point => ({
        x: Math.round(toNormalizedX(point.x) * 100) / 100, // ปัดเศษให้เป็น 2 ตำแหน่ง
        y: Math.round(toNormalizedY(point.y) * 100) / 100
      }));

      // ลบ duplicate points ใน normalized coordinates
      normalizedBlocks = normalizedBlocks.filter((point, index, arr) => {
        if (index === 0) return true;
        const prevPoint = arr[index - 1];
        return !(Math.abs(point.x - prevPoint.x) < 0.01 && Math.abs(point.y - prevPoint.y) < 0.01);
      });

      // ตรวจสอบว่ายังมีจุดเพียงพอหลังจากลบ duplicate
      if (normalizedBlocks.length < 3) {
        Alert.alert("Error", "Invalid polygon: too few unique points");
        return;
      }

      const req = {
        farmUserPlotId: reduxFarmUserPlotId,
        numberOfPlanting: 1,
        plantingList: [
          {
            farmSeedId: idSelectPlans || "",
            seedName: selectPlansName || "",
            pointLatitude: 0,
            pointLongitude: 0,
            blocks: normalizedBlocks,
          },
        ],
      };

      const response = await plantingApi(req);
      console.log("Save Data Request:", JSON.stringify(req, null, 2));
      console.log("Save Data Response:", response);

      // Clear states after successful save
      setPaths([]);
      setCurrentPath([]);
      setPositions([]);
      setDocSelectPlans([]);
      setSelectPlans("");
      setSelectPlansName("");
      setImageUrlPlans(null);
      setOpenSheet(false);

      Alert.alert("Success", "Data saved successfully");
    } catch (error: any) {
      console.error("Error saving data:", error);

      // Handle different types of errors
      let errorMessage = "Failed to save data. Please try again.";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      Alert.alert("Error", errorMessage);
    } finally {
      setModalSaveArea(false);
      setIsLoading(false);
    }
  };

  const goBack = () => {
    navigation.navigate("ManageFarmUser", {
      docDetailMyfarm: docList,
    });
  };

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => paths.length === 0,
    // เริ่มลากนิ้ว
    onPanResponderGrant: (e) => {
      if (paths.length === 0) {
        // Destructure nativeEvent immediately to avoid synthetic event pooling issues
        const nativeEvent = e.nativeEvent;
        const { locationX, locationY } = nativeEvent;
        const newPath = [{ x: locationX, y: locationY }];
        setCurrentPath(newPath);
        setPositions([{ x: locationX, y: locationY }]);
      }
    },
    // ขณะลากนิ้ว
    onPanResponderMove: (e) => {
      if (paths.length === 0) {
        // Destructure nativeEvent immediately to avoid synthetic event pooling issues
        const nativeEvent = e.nativeEvent;
        const { locationX, locationY } = nativeEvent;
        setCurrentPath((prev) => [...prev, { x: locationX, y: locationY }]);
        setPositions((prev) => [...prev, { x: locationX, y: locationY }]);
      }
    },
    // ปล่อยนิ้ว - ปิด polygon โดยเชื่อมจุดสุดท้ายกับจุดแรก
    onPanResponderRelease: () => {
      if (paths.length > 0) {
        setCurrentPath([]);
        setPositions([]);
      } else if (currentPath.length <= 2) {
        Alert.alert("แจ้งเตือน", "กรุณาวาดอย่างน้อย 3 จุด");
        setCurrentPath([]);
        setPositions([]);
      } else {
        // ปิด polygon โดยเชื่อมจุดสุดท้ายกับจุดแรก
        let closedPath = [...currentPath];

        if (closedPath.length > 2) {
          // ลบ duplicate points ที่อาจเกิดขึ้น
          closedPath = closedPath.filter((point, index, arr) => {
            if (index === 0) return true;
            const prevPoint = arr[index - 1];
            return !(Math.abs(point.x - prevPoint.x) < 1 && Math.abs(point.y - prevPoint.y) < 1);
          });

          // ตรวจสอบว่าจุดสุดท้ายไม่ใช่จุดแรกแล้ว (ใช้ tolerance เล็กน้อย)
          const firstPoint = closedPath[0];
          const lastPoint = closedPath[closedPath.length - 1];
          const tolerance = 5; // pixel tolerance

          if (Math.abs(firstPoint.x - lastPoint.x) > tolerance ||
              Math.abs(firstPoint.y - lastPoint.y) > tolerance) {
            closedPath.push(firstPoint); // เพิ่มจุดแรกเป็นจุดสุดท้าย
          }
        }

        setPaths([closedPath]);
        setLastPositions(closedPath); // เก็บตำแหน่งล่าสุดไว้
        setCurrentPath([]);
        setPositions([]);
      }
    },
  });

  const buttomContal = () => {
    return (
      <View style={ctn.ctn_bottomSavePlant}>
        <View
          style={{
            flexDirection: "row",
            backgroundColor: BgOpacity.Op_ffffff5,
            padding: 10,
            borderRadius: 26,
          }}
        >
          {/* Save Button */}
          <View style={{ flexDirection: "column" }}>
            <TouchableOpacity
              onPress={() =>
                docSelectPlans != "" ? handleSavePolygon() : null
              }
            >
              <LinearGradient
                colors={linearGreen}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={
                  docSelectPlans != ""
                    ? btn.btn_saveButton
                    : btn.btn_saveButtonNon
                }
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />
                <Image
                  style={{
                    width: moderateScale(18),
                    height: moderateScale(18),
                  }}
                  source={Images.save}
                  resizeMode="cover"
                />
              </LinearGradient>
            </TouchableOpacity>
            <Text
              style={[
                fonstStyle.f10_bold,
                txt.txt_606060,
                { textAlign: "center" },
              ]}
            >
              {t("save")}
            </Text>
          </View>
          <View style={{ margin: moderateScale(5) }} />

          {/* Cancel Button */}
          <View style={{ flexDirection: "column" }}>
            <TouchableOpacity onPress={() => setOpenSheet(false)}>
              <LinearGradient
                colors={lineOrange}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={btn.btn_boxBottonMenu}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />
                {iconScreenClose()}
              </LinearGradient>
            </TouchableOpacity>
            <Text
              style={[
                fonstStyle.f10_bold,
                txt.txt_606060,
                { textAlign: "center" },
              ]}
            >
              {t("cancel")}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderSelectPlans = ({ item }: any) => {
    return (
      <View style={[ctn.ctn_clickPlans]}>
        <LinearGradient
          colors={[LgColor.Lg_B8DFE6, LgColor.Lg_B8DFE6]}
          start={{ x: 0.0, y: 0.05 }}
          end={{ x: 0.5, y: 1.0 }}
          style={oth.clickPlans}
        >
          <Image
            style={img.img_iconEllipse}
            source={Images.Ellipse}
            resizeMode="contain"
          />
          <View
            style={{ alignItems: "center", flex: 1, justifyContent: "center" }}
          >
            <Text
              numberOfLines={1}
              style={[fonstStyle.f12_bold, txt.txt_606060]}
            >
              {item.plantName}
            </Text>
            {item.imageUrl != null ? (
              <Image
                style={img.img_Manage}
                source={{ uri: item.imageUrl }}
                resizeMode="contain"
              />
            ) : (
              <View style={img.img_Manage}>{iconNoImg()}</View>
            )}
            <Text
              style={[fonstStyle.f12_bold, txt.txt_606060, txt.text_Harvest]}
            >
              {item.harvestPeriod} {t("day")}
            </Text>
            <TouchableOpacity
              onPress={() =>
                idSelectPlans === item.id ? cleanDocSelect() : selectPlans(item)
              }
              style={{ marginTop: moderateScale(24) }}
            >
              <LinearGradient
                colors={idSelectPlans === item.id ? lineOrange : linearGreen}
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 0.0, y: 1.0 }}
                style={[oth.boxSelect]}
              >
                <View style={{ alignItems: "center" }}>
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {idSelectPlans === item.id ? t("cancel") : t("select")}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };

  return (
    <>
      <StatusBar barStyle={"dark-content"} hidden={false} />
      <TouchableOpacity
        style={[btn.btn_costomGoback, { zIndex: 9999 }]}
        onPress={() => goBack()}
      >
        {goBack_Bg()}
      </TouchableOpacity>
      <View style={styles.container} {...panResponder.panHandlers}>
        <Svg width={imageWidth} height={imageHeight}>
          {/* Main farm boundary */}
          {blocks.length > 1 && (
            <Path
              d={
                "M" +
                blocks
                  .map((p: any) => `${toScreenX(p.x)},${toScreenY(p.y)}`)
                  .join(" L") +
                " Z"
              }
              stroke={BgColor.Bg_64BAA8}
              fill={BgOpacity.Op_8080802}
              strokeWidth={3}
            />
          )}

          {/* Drawn paths (completed polygons) */}
          {paths.map((path, index) => {
            // หาค่ากลาง (centroid) เฉพาะถ้ามีจุดมากกว่า 2 จุด
            let centroid = null;
            if (path.length > 2) {
              const sum = path.reduce(
                (acc, p) => ({
                  x: acc.x + p.x / path.length,
                  y: acc.y + p.y / path.length,
                }),
                { x: 0, y: 0 }
              );
              centroid = sum;
            }

            return (
              <React.Fragment key={index}>
                <Path
                  d={
                    path
                      .map((p, i) =>
                        i === 0 ? `M${p.x},${p.y}` : `L${p.x},${p.y}`
                      )
                      .join(" ") + " Z"
                  }
                  stroke={BgColor.Bg_E74C3C}
                  fill={BgOpacity.Op_E74C3C03}
                  strokeWidth={2}
                />
                {/* แสดง iconPlusPlant ตรงกลาง polygon */}
                {centroid && (
                  <View
                    style={{
                      position: "absolute",
                      left: centroid.x - 16,
                      top: centroid.y - 16,
                      zIndex: 20,
                    }}
                    pointerEvents="none"
                  >
                    {imageUrlPlans === null ? (
                      <View style={styles.centroidIcon}>{iconPlusPlant()}</View>
                    ) : (
                      <FastImage
                        style={styles.imagePlans}
                        source={{ uri: imageUrlPlans }}
                        resizeMode={FastImage.resizeMode.contain}
                      />
                    )}
                  </View>
                )}
              </React.Fragment>
            );
          })}

          {/* Current path being drawn */}
          {currentPath.length > 0 && (
            <Path
              d={
                currentPath
                  .map((p, i) =>
                    i === 0 ? `M${p.x},${p.y}` : `L${p.x},${p.y}`
                  )
                  .join(" ") + (currentPath.length > 2 ? " Z" : "")
              }
              stroke={BgColor.Bg_E74C3C}
              fill={BgOpacity.Op_E74C3C03}
              strokeWidth={1.5}
            />
          )}

          {/* Draw points while drawing */}
          {positions.map((pos, index) => (
            <Circle key={index} cx={pos.x} cy={pos.y} r={2} fill="red" />
          ))}

          {/* Farm boundary points */}
          {blocks.map((p: any, i: number) => (
            <Circle
              key={i}
              cx={toScreenX(p.x)}
              cy={toScreenY(p.y)}
              r={4}
              fill="red"
            />
          ))}

          {/* Existing plantings */}
          {Array.isArray(docList.planting) &&
            docList.planting.map((item: any, idx: number) => {
              const polygon = item.blocks.map((mapData: any) => ({
                x: Number(mapData.x),
                y: Number(mapData.y),
              }));
              if (polygon.length <= 1) return null;
              const isCanEdit = item.isCanEdit || "";
              const itemImages = item.imageUrl || "";

              // คำนวณค่ากลาง (centroid)
              const centroid = polygon.reduce(
                (acc: any, point: any) => ({
                  x: acc.x + point.x / polygon.length,
                  y: acc.y + point.y / polygon.length,
                }),
                { x: 0, y: 0 }
              );

              return (
                <React.Fragment key={idx}>
                  <Path
                    d={
                      "M" +
                      polygon
                        .map((p: any) => `${toScreenX(p.x)},${toScreenY(p.y)}`)
                        .join(" L") +
                      " Z"
                    }
                    stroke={isCanEdit ? BgColor.Bg_FF9900 : BgColor.Bg_64BAA8}
                    fill={
                      isCanEdit ? BgOpacity.Op_eecb2f2 : BgOpacity.Op_b3dbc05
                    }
                    strokeWidth={2}
                  />
                  {/* แสดงรูปภาพที่ตำแหน่ง centroid */}
                  {itemImages && (
                    <FastImage
                      style={[
                        styles.centroidImage,
                        {
                          left: toScreenX(centroid.x) - 25,
                          top: toScreenY(centroid.y) - 25,
                        },
                      ]}
                      source={{ uri: itemImages }}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                  )}
                </React.Fragment>
              );
            })}
        </Svg>

        {/* Control buttons when polygon is completed */}
        {paths.length > 0 && (
          <View style={styles.continueControl}>
            <TouchableOpacity onPress={() => handleCleanArea()}>
              <LinearGradient
                colors={linearDanger}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={btn.btn_cleanButton}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />
                {iconDeleteFarm()}
              </LinearGradient>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />

            <TouchableOpacity onPress={() => handleFinishDraw()}>
              <LinearGradient
                colors={linearGreen}
                start={{ x: 0.0, y: 0.05 }}
                end={{ x: 0.5, y: 1.0 }}
                style={btn.btn_cleanButton}
              >
                <Image
                  style={img.img_iconEllipse}
                  source={Images.Ellipse}
                  resizeMode="contain"
                />
                {iconCheckArea()}
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}

        {/* Bottom Sheet for plant selection */}
        {openSheet === true ? (
          <BottomSheet
            index={0}
            ref={bottomSheetRef}
            snapPoints={snapPoints}
            backgroundStyle={{
              backgroundColor: BgOpacity.Op_C5BBDE,
            }}
          >
            <BottomSheetView>{FlasListAreaPlant()}</BottomSheetView>
          </BottomSheet>
        ) : null}

        {openSheet === true ? buttomContal() : null}

        {/* Save confirmation modal */}
        <Modal animationType="fade" transparent={true} visible={modalSaveArea}>
          <View style={mod.mod_center}>
            <View style={[mod.mod_View]}>
              <View style={oth.opt_Success}>
                <LinearGradient
                  colors={[
                    LgColor.Lg_C0D576,
                    LgColor.Lg_A1BD57,
                    LgColor.Lg_78994A,
                  ]}
                  start={{ x: 0.0, y: 0.05 }}
                  end={{ x: 0.5, y: 1.0 }}
                  style={oth.bg_Success}
                >
                  <Image
                    style={img.img_iconEllipse}
                    source={Images.Ellipse}
                    resizeMode="contain"
                  />
                  {iconPlant()}
                </LinearGradient>
              </View>
              <View style={{ bottom: verticalScale(30) }}>
                <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                  {t("record_area")}
                </Text>
              </View>
              <View style={{ flexDirection: "row" }}>
                <TouchableOpacity onPress={() => setModalSaveArea(false)}>
                  <LinearGradient
                    colors={[
                      LgColor.Lg_FFE2BF,
                      LgColor.Lg_FFB155,
                      LgColor.Lg_FF8A00,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonCancle}
                  >
                    <Image
                      style={img.img_iconEllipse}
                      source={Images.Ellipse}
                      resizeMode="contain"
                    />
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {t("cancel")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
                <View style={{ margin: moderateScale(5) }} />

                <TouchableOpacity
                  onPress={() => handleSaveData()}
                  disabled={isLoading}
                  style={{ opacity: isLoading ? 0.6 : 1 }}
                >
                  <LinearGradient
                    colors={[
                      LgColor.Lg_C0D576,
                      LgColor.Lg_A1BD57,
                      LgColor.Lg_78994A,
                    ]}
                    start={{ x: 0.0, y: 0.05 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={btn.btn_bottonAgree}
                  >
                    {isLoading ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <Image
                        style={img.img_iconEllipse}
                        source={Images.Ellipse}
                        resizeMode="contain"
                      />
                    )}
                    <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                      {isLoading ? "Saving..." : t("confirm")}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "black",
  },
  centroidImage: {
    width: 50,
    height: 50,
    borderRadius: 10,
    position: "absolute",
    zIndex: 10,
  },
  continueControl: {
    position: "absolute",
    bottom: 25,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  centroidIcon: {
    width: 50,
    height: 50,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  imagePlans: {
    width: 50,
    height: 50,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
});

export default DrawingApp;
