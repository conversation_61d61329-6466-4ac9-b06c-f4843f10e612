import moment from "moment";
import React, { useRef, memo } from "react";
import { moderateScale } from "react-native-size-matters";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
import { View, Text, TouchableOpacity, ActivityIndicator } from "react-native";
// Style imports
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
// SVG imports
import {
  iconLike,
  iconLikeBg,
  iconDotMenu,
  iconComment,
  iconForwardPost,
  iconForwardPostBg,
} from "../../assets/svg/svg_other";
import { ActionSheetPost } from "../bottomSheet/bottomSheet";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import { CachedVideo } from "../../components/cacheFiles/cacheVideo";
{
  /* <MyVideoComponent videoUrl={item.videoUrl} style={{ width: 300, height: 200 }} /> */
}

interface PostItemProps {
  item: any;
  index: number;
  isUserId: string;
  isShowFullText: boolean[];
  toggleFullText: (index: number) => void;
  openIndex: number | null;
  toggleMenu: (index: number) => void;
  isMuted: boolean;
  setIsMuted: React.Dispatch<React.SetStateAction<boolean>>;
  isLoadLikes: Record<number, boolean>;
  isLoadShare: Record<number, boolean>;
  setLoadIng: React.Dispatch<React.SetStateAction<boolean>>;
  imageDimensions: any;
  setImageDimensions: React.Dispatch<React.SetStateAction<any>>;
  NUM_OF_LINES: number;
  t: any;
  goDetailPost: (item: any, type: string) => void;
  goLike: (item: any, index: number) => void;
  goComment: (item: any) => void;
  onShare: (item: any, index: number) => void;
  goEditPost: (item: any) => void;
  onDelete: (item: any, index: number) => void;
  goFriend: (item: any) => void;
  navigation: any;
}

const PostItem: React.FC<PostItemProps> = ({
  item,
  index,
  isUserId,
  isShowFullText,
  toggleFullText,
  openIndex,
  toggleMenu,
  isMuted,
  setIsMuted,
  isLoadLikes,
  isLoadShare,
  setLoadIng,
  imageDimensions,
  setImageDimensions,
  NUM_OF_LINES,
  t,
  goDetailPost,
  goLike,
  goComment,
  onShare,
  goEditPost,
  onDelete,
  goFriend,
  navigation,
}) => {
  if (!item) return null;
  const actionSheetRef = useRef<ActionSheetRef>(null);
  const isTextLongEnough =
    item.messageContent && item.messageContent.length > 200;

  const openActionSheet = () => {
    actionSheetRef.current?.show();
  };

  return (
    <View style={oth.card_Vartical}>
      <View style={{ flexDirection: "row", padding: moderateScale(10) }}>
        <TouchableOpacity
          style={ctn.ctn_profileVtcal}
          onPress={() =>
            item?.userId === isUserId
              ? navigation.navigate("ProfileUser")
              : goFriend(item)
          }
        >
          <MyImageComponent
            imageUrl={item?.profileImageUrl}
            style={img.img_profile}
          />
        </TouchableOpacity>
        <View style={ctn.ctn_TxtProfile}>
          <TouchableOpacity
            onPress={() =>
              item?.userId === isUserId
                ? navigation.navigate("ProfileUser")
                : goFriend(item)
            }
          >
            <Text
              style={[txt.txt_Vtcal, fonstStyle.f14_bold, txt.txt_606060]}
              numberOfLines={5}
            >
              {item?.firstName}
            </Text>
          </TouchableOpacity>
          <Text style={[txt.txt_time, fonstStyle.f12_light, txt.txt_606060]}>
            {moment(item?.updatedAt).locale(t("locale")).fromNow()}
          </Text>
        </View>
      </View>

      <View style={ctn.ctn_MenuDot}>
        {item?.userId === isUserId ? (
          <TouchableOpacity onPress={() => openActionSheet()}>
            {iconDotMenu()}
          </TouchableOpacity>
        ) : null}

        <ActionSheetPost
          ref={actionSheetRef}
          item={item}
          onEdit={(item: any) => goEditPost(item)}
          onDelete={(item: any) => onDelete(item, index)}
        />
      </View>

      {item?.messageContent ? (
        <Text
          numberOfLines={!isShowFullText[index] ? NUM_OF_LINES : undefined}
          style={[
            txt.txt_cardPost,
            fonstStyle.f12_light,
            txt.txt_606060,
            { paddingHorizontal: moderateScale(10) },
          ]}
        >
          {item?.messageContent}
        </Text>
      ) : null}

      {isTextLongEnough ? (
        <TouchableOpacity onPress={() => toggleFullText(index)}>
          <Text style={[txt.txt_fullTxt, fonstStyle.f10_bold]}>
            {!isShowFullText[index] ? t("show_more") : t("close_more")}
          </Text>
        </TouchableOpacity>
      ) : null}
      <View style={{ margin: moderateScale(5) }} />

      {item?.imageContents && item?.imageContents.length > 0 && (
        <>
          <View style={[ctn.ctn_imagePost]}>
            {item?.imageContents.length === 1 ? (
              <TouchableOpacity
                key={item?.imageContents[0].id}
                onPress={() => goDetailPost(item, "seePost")}
                style={{ flex: 1, alignItems: "center" }}
              >
                <MyImageComponent
                  imageUrl={item?.imageContents[0].url}
                  style={[
                    img.img_imagePost,
                    imageDimensions && {
                      width: imageDimensions.width,
                      height: imageDimensions.height,
                    },
                  ]}
                />
              </TouchableOpacity>
            ) : item.imageContents.length === 2 ? (
              <View style={{ flexDirection: "row", width: "100%" }}>
                {item.imageContents.map((image: any, idx: number) => (
                  <TouchableOpacity
                    key={`image-${image.id || idx}`}
                    onPress={() => goDetailPost(item, "seePost")}
                    style={{
                      width: "50%",
                      paddingRight: idx === 0 ? moderateScale(4) : 0,
                    }}
                  >
                    <MyImageComponent
                      imageUrl={image.url}
                      style={[img.img_imagePost]}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <>
                <View style={{ width: "50%", paddingRight: moderateScale(4) }}>
                  <TouchableOpacity
                    key={item.imageContents[0].id}
                    onPress={() => goDetailPost(item, "seePost")}
                  >
                    <MyImageComponent
                      imageUrl={item.imageContents[0].url}
                      style={[img.img_imagePost]}
                    />
                  </TouchableOpacity>
                </View>

                <View style={{ width: "50%", flexDirection: "column" }}>
                  {item.imageContents
                    .slice(1, 3)
                    .map((image: any, idx: number) => (
                      <TouchableOpacity
                        key={`image-more-${image.id || idx}`}
                        onPress={() => goDetailPost(item, "seePost")}
                        style={{
                          width: "100%",
                          paddingBottom: idx === 0 ? moderateScale(4) : 0,
                        }}
                      >
                        <MyImageComponent
                          imageUrl={image.url}
                          style={[
                            img.img_imagePost,
                            {
                              height: 145,
                            },
                          ]}
                        />
                        {idx === 1 && item.imageContents.length > 3 && (
                          <View style={ctn.ctn_imgaePlus}>
                            <Text
                              style={[
                                fonstStyle.f32_light,
                                txt.txt_imageNumber,
                              ]}
                            >
                              +{item.imageContents.length - 3}
                            </Text>
                          </View>
                        )}
                      </TouchableOpacity>
                    ))}
                </View>
              </>
            )}
          </View>
          <View style={{ margin: moderateScale(2) }} />
        </>
      )}

      {item?.videoContents && item?.videoContents.length > 0 && (
        <>
          <View style={ctn.ctn_imagePost}>
            <View style={{ width: "100%", alignItems: "center" }}>
              <CachedVideo
                videoUrl={item.videoContents[0]?.url}
                style={ctn.ctn_video}
              />
            </View>
          </View>
        </>
      )}

      <View style={{ margin: moderateScale(5) }} />

      <View style={ctn.ctn_likeComment}>
        <TouchableOpacity
          onPress={() => goLike(item, index)}
          style={{ flexDirection: "row" }}
        >
          {isLoadLikes[index] ? (
            <ActivityIndicator size="small" color={BgColor.Bg_D6D6D6} />
          ) : item?.isLike ? (
            iconLikeBg()
          ) : (
            iconLike()
          )}

          <View style={{ margin: moderateScale(2) }} />

          <View style={{ flexDirection: "row" }}>
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {item?.numberOfLike || "0"}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {t("like_post")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={{ margin: moderateScale(2) }} />

        <TouchableOpacity
          onPress={() => goComment(item)}
          style={{ flexDirection: "row" }}
        >
          <View style={{ margin: moderateScale(5) }} />
          {iconComment()}
          <View style={{ margin: moderateScale(2) }} />

          <View style={{ flexDirection: "row" }}>
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {item?.comments?.length || "0"}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {t("comment_post")}
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => onShare(item, index)}
          style={{ flexDirection: "row" }}
        >
          <View style={{ margin: moderateScale(5) }} />

          {isLoadShare[index] ? (
            <ActivityIndicator size="small" color={BgColor.Bg_D6D6D6} />
          ) : item?.isShare ? (
            iconForwardPostBg()
          ) : (
            iconForwardPost()
          )}

          <View style={{ margin: moderateScale(2) }} />

          <View style={{ flexDirection: "row" }}>
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {item?.numberOfShare || "0"}
            </Text>
            <View style={{ margin: moderateScale(2) }} />
            <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
              {t("share")}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
      <View style={{ margin: moderateScale(5) }} />
    </View>
  );
};

export default memo(PostItem);
