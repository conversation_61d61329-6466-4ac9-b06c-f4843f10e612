import React from "react";
import { View, Text, TextInput, TouchableOpacity, Image } from "react-native";
import Images from "../../utils/imageManager";
import { iconSavePoltName } from "../../assets/svg/svg_other";

export default function EditPoltname({
  farmName,
  orientation,
  isEditFramName,
  framPlotName,
  setFramPlotName,
  plotName,
  onCancleEditName,
  onOpenNewName,
  iconCancleFarmName,
  iconEditFarmName,
  iconSaveFarmName,
  iconSavePoltName,
  iconCanclePoltName,
  iconEditPoltName,
  fonstStyle,
  txt,
  oth,
  ctn,
  moderateScale,
  t,
  isTablet,
  isPages,
}: any) {
  return (
    <>
      {farmName || orientation === "landscape" ? (
        <View
          style={
            orientation === "portrait" ? ctn.ctn_nameFarm : ctn.ctn_nameFarmLan
          }
        >
          {isEditFramName ? (
            <>
              <View
                style={[
                  orientation === "portrait"
                    ? oth.card_InPutEditName
                    : oth.card_InPutEditNameLan,
                ]}
              >
                <TouchableOpacity
                  style={oth.editFramName}
                  onPress={onCancleEditName}
                >
                  {iconCanclePoltName()}
                </TouchableOpacity>

                {isEditFramName && framPlotName != "" ? (
                  <TouchableOpacity
                    style={oth.saveEditFramName}
                    onPress={onOpenNewName}
                  >
                    {iconSavePoltName()}
                  </TouchableOpacity>
                ) : null}
                <TextInput
                  style={[
                    fonstStyle.f14_bold,
                    txt.txt_white,
                    { textAlign: "center" },
                  ]}
                  onChangeText={setFramPlotName}
                  placeholder={plotName}
                  value={framPlotName}
                  autoFocus={true}
                  maxLength={15}
                />
              </View>
              <View style={{ margin: moderateScale(2) }} />
              <Text
                style={[
                  fonstStyle.f12_bold,
                  txt.txt_red,
                  {
                    right: isTablet && orientation === "landscape" ? "10%" : 0,
                  },
                ]}
              >
                {t("15characters")}
              </Text>
            </>
          ) : (
            <View
              style={[
                orientation === "portrait"
                  ? oth.card_nameFarm
                  : oth.card_nameFarmLan,
              ]}
            >
              {/* ปุ่ม Edit Name */}
              {isPages != "MaketFram" ? (
                <TouchableOpacity
                  style={oth.editFramName}
                  onPress={onCancleEditName}
                >
                 {iconEditPoltName()}
                </TouchableOpacity>
              ) : null}

              {/* ชื่อแปลง */}
              <Text
                style={[
                  fonstStyle.f14_bold,
                  txt.txt_white,
                  { textAlign: "center", width: "100%" },
                ]}
                numberOfLines={1}
              >
                {plotName}
              </Text>
            </View>
          )}
        </View>
      ) : null}
    </>
  );
}
