import React from "react";
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";

export default function PayMentManage({
  paymentMethods = [],
  selectedIndex,
  selectPayMent,
  btn,
  iconBanking,
  iconBankingChang,
  moderateScale,
  BgColor,
}: any) {
  const renderItem = ({ item, index }: { item: any; index: number }) => (
    <TouchableOpacity
      key={item.id || index}
      disabled={selectedIndex === index}
      onPress={() => selectPayMent(item, index)}
      style={[
        selectedIndex === index
          ? btn.btn_payMentActive
          : btn.btn_payMentNon,
      ]}
    >
      {item.iconImageUrl === null ? (
        selectedIndex === index
          ? iconBankingChang()
          : iconBanking()
      ) : (
        <Image
          source={{ uri: item.iconImageUrl }}
          style={{ width: 40, height: 40, borderRadius: 5 }}
        />
      )}
    </TouchableOpacity>
  );

  if (!paymentMethods || !Array.isArray(paymentMethods)) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="small" color={BgColor?.Bg_84B8A2 || "#84B8A2"} />
      </View>
    );
  }

  return (
    <View
      style={{
        marginTop: 10,
       
        alignItems: "flex-end",

      }}
    >
      <FlatList
        data={paymentMethods}
        horizontal
        keyExtractor={(item, index) => item.id?.toString() || index.toString()}
        renderItem={renderItem}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ flexDirection: "row" }}
        removeClippedSubviews={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: "center", alignItems: "center" },
});