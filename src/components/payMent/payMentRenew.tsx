import React from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
} from "react-native";

export default function PayMentRenew({
  paymentMethods = [],
  selectedIndex,
  selectPayMent,
  btn,
  iconBanking,
  iconBankingChang,
  moderateScale,
}: any) {
  const renderItem = ({ item, index }: { item: any; index: number }) => (
    <View style={{ alignItems: "center" }} key={item.id || index}>
      <TouchableOpacity
        disabled={selectedIndex === index}
        onPress={() => selectPayMent(item, index)}
        style={[
          selectedIndex === index ? btn.btn_payMentActive : btn.btn_payMentNon,
        ]}
      >
        {item.iconImageUrl === null ? (
          selectedIndex === index ? (
            iconBankingChang()
          ) : (
            iconBanking()
          )
        ) : (
          <Image
            source={{ uri: item.iconImageUrl }}
            style={{ width: 40, height: 40, borderRadius: 5 }}
          />
        )}
      </TouchableOpacity>
      <View style={{ margin: moderateScale(10) }} />
    </View>
  );

  return (
    <FlatList
      data={paymentMethods}
      horizontal
      keyExtractor={(item, index) => item.id?.toString() || index.toString()}
      renderItem={renderItem}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ flexDirection: "row" }}
      removeClippedSubviews={false}
    />
  );
}

const styles = StyleSheet.create({});
