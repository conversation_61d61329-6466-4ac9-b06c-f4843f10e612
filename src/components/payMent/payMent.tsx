import React from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, Image } from "react-native";

export default function PayMent({
  paymentMethods = [],
  selectedIndex,
  selectPayMent,
  t,
  btn,
  fonstStyle,
  txt,
  moderateScale,
  iconBanking,
  iconBankingChang,
}: any) {
  const renderPaymentItem = ({ item, index }: { item: any; index: number }) => (
    <View style={{ flexDirection: "row", alignItems: "center" }} key={item.id || index}>
      <TouchableOpacity
        disabled={selectedIndex === index}
        onPress={() => selectPayMent(item, index)}
        style={[
          selectedIndex === index
            ? btn.btn_payMentActive
            : btn.btn_payMentNon,
        ]}
      >
        {item.iconImageUrl === null ? (
          selectedIndex === index
            ? iconBankingChang()
            : iconBanking()
        ) : (
          <Image
            source={{ uri: item.iconImageUrl }}
            style={{ width: 40, height: 40, borderRadius: 5 }}
          />
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={{ paddingHorizontal: moderateScale(20), marginTop: moderateScale(10) }}>
      <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
        {t("Payment_channels")}
      </Text>
      <View style={{ margin: moderateScale(10) }} />
      <FlatList
        data={paymentMethods}
        horizontal
        keyExtractor={(item, index) => item.id?.toString() || index.toString()}
        renderItem={renderPaymentItem}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ flexDirection: "row" }}
        removeClippedSubviews={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({});