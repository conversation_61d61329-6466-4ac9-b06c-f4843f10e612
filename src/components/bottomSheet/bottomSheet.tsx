import { Text, View, TouchableOpacity } from "react-native";
import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { moderateScale } from "react-native-size-matters";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
import { iconEditPost, iconDeletePost } from "../../assets/svg/svg_other";
import fonstStyle from "../../styleSheet/style_Custom";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import { useTranslation } from "../../screen/i18n";

// ใช้ forwardRef เพื่อให้ parent ควบคุม show/hide ได้
export const ActionSheetPost = forwardRef(
  ({ item, onEdit, onDelete }: any, ref) => {
    const { t } = useTranslation();
    const actionSheetRef = useRef<ActionSheetRef>(null);

    // ให้ parent เรียก show/hide ได้
    useImperativeHandle(ref, () => ({
      show: () => actionSheetRef.current?.show(),
      hide: () => actionSheetRef.current?.hide(),
    }));

    return (
      <ActionSheet
        ref={actionSheetRef}
        gestureEnabled
        containerStyle={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      >
        <TouchableOpacity
          style={{ padding: 20 }}
          onPress={() => {
            actionSheetRef.current?.hide();
            onEdit?.(item);
          }}
        >
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconEditPost()}
            <View style={{ margin: moderateScale(5) }} />
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("editPost")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity
          style={{ padding: 20 }}
          onPress={() => {
            actionSheetRef.current?.hide();
            onDelete?.(item);
          }}
        >
          <View style={{ flexDirection: "row", justifyContent: "center" }}>
            {iconDeletePost()}
            <View style={{ margin: moderateScale(5) }} />
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {t("delete_Post")}
            </Text>
          </View>
        </TouchableOpacity>
        <View style={oth.line_profile} />

        <TouchableOpacity
          style={{ padding: 20 }}
          onPress={() => actionSheetRef.current?.hide()}
        >
          <Text
            style={[
              fonstStyle.f14_light,
              txt.txt_red,
              { textAlign: "center" },
            ]}
          >
            {t("cancel")}
          </Text>
        </TouchableOpacity>
      </ActionSheet>
    );
  }
);
