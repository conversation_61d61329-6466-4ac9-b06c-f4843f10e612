import React from "react";
import { StyleSheet, Text, View, FlatList } from "react-native";

export default function Package({
  data = [],
  renderPackAge,
  t,
  fonstStyle,
  txt,
  moderateScale,
}: any) {
  return (
    <View>
      <FlatList
        data={data}
        keyExtractor={(item, index) => item.id?.toString() || index.toString()}
        renderItem={({ item, index }) => renderPackAge({ item, index })}
        ListEmptyComponent={
          <View
            style={{ alignItems: "center", padding: moderateScale?.(20) || 20 }}
          >
            <Text style={[fonstStyle?.f12_light, txt?.txt_gray]}>
              {t ? t("no_package") : "No package"}
            </Text>
          </View>
        }
        contentContainerStyle={{ paddingBottom: moderateScale?.(10) || 10 }}
        removeClippedSubviews={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({});
