import React, { useState, useEffect, useRef } from "react";
import { useFocusEffect } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { useDispatch, useSelector } from "react-redux";
import {
  View,
  Text,
  Animated,
  Platform,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ScaledSheet } from "react-native-size-matters";
import { BgColor, iconColor } from "../../styleSheet/style_Custom";
import txt from "../../styleSheet/txt";
import fonstStyle from "../../styleSheet/style_Custom";

// SVG
import {
  tabHome,
  tabBell,
  tabUser,
  TapCase,
  tabPlans,
  tabStore,
  tabPinGobal,
  tabBgCenter,
  TapCaseChange,
  tabHomeChange,
  tabBellChange,
  tabUserChange,
  tabPlansChange,
  tabStoreChange,
  tabPinGobalChange,
} from "../../assets/svg/svg_bottomTap";
// Page
import Noti from "../../screen/noti/noti";
import Home from "../../screen/homeScreen/home";
import MaketFram from "../../screen/maket/maketFram";
import Plusarea from "../../screen/areaScreen/plusArea";
import ProfileUser from "../../screen/profileScreen/profileUser";
import profile from "../../screen/profileScreen/profile";
// Redux
import { setNotificationCount } from "./../../Redux_Store/action";

// Api
import { pusCount } from "../../action/Mefarm_Realtime_API";
import { bottomTabEventEmitter } from "../../utils/eventEmitter";
const { height: screenHeight } = Dimensions.get("window");

export default function Bottom_Tab({ route, navigation }: any) {
  // BottomNavigator
  const Tab = createBottomTabNavigator();
  const [selectedTab, setSelectedTab] = useState<string>("");
  const [orientation, setOrientation] = useState("portrait");
  const [tabBarVisible, setTabBarVisible] = useState(true);
  const translateY = useRef(new Animated.Value(0)).current;
  const bottomPosition = useRef(new Animated.Value(0)).current;

  // Redux
  const dispatch = useDispatch();
  const notificationCount = useSelector(
    (state: any) => state.notificationCount
  );

  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(getOrientation());
    };

    updateOrientation();

    const subscription = Dimensions.addEventListener(
      "change",
      updateOrientation
    );

    return () => {
      subscription.remove();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const pushNotiCount = async () => {
        try {
          const userIdLogin = (await AsyncStorage.getItem("userIdLogin")) || "";
          const res = await pusCount(userIdLogin);
          const totalNotifications = res.model || 0;
          dispatch(setNotificationCount(totalNotifications));
        } catch (error) {
          console.error("Error fetching notifications:", error);
        }
      };

      pushNotiCount();
    }, [dispatch])
  );

  const getOrientation = () => {
    const { height, width } = Dimensions.get("window");
    return height >= width ? "portrait" : "landscape";
  };

  const onBotton = (focused: any, route: any, navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setSelectedTab("");
          navigation.navigate("PlusArea");
        }}
        style={[
          styles.bottonCenter,
          {
            backgroundColor: focused ? BgColor.Bg_6A938D : BgColor.Bg_FFFFFF,
          },
        ]}
      >
        <>{focused ? tabPlans() : tabPlansChange()}</>
      </TouchableOpacity>
    );
  };

  const onStore = (focused: any, route: any) => {
    return (
      <View
        style={[
          styles.bottonCenter,
          {
            backgroundColor: focused ? BgColor.Bg_6A938D : BgColor.Bg_FFFFFF,
          },
        ]}
      >
        <>{!focused ? TapCase() : TapCaseChange()}</>
      </View>
    );
  };
  useEffect(() => {
    const listener = (data: { visible: boolean }) => {
      setTabBarVisible(data.visible);
    };
    bottomTabEventEmitter.on("bottomTabVisibility", listener);
    return () => {
      bottomTabEventEmitter.off("bottomTabVisibility", listener);
    };
  }, []);

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: [
          styles.barStyle,
          { display: tabBarVisible ? "flex" : "none" },
        ],
        tabBarInactiveTintColor: iconColor.Ic_FFFFFF,
        tabBarActiveTintColor: iconColor.Ic_32711D,
        tabBarIconStyle: {
          bottom: -10,
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={Home}
        options={({ route, navigation }) => ({
          tabBarIcon: ({ focused }) => (!focused ? tabHome() : tabHomeChange()),
          tabBarButton: (props: any) => (
            <TouchableOpacity
              {...props}
              onPress={(e) => {
                props.onPress?.(e);
                setSelectedTab("");
              }}
            />
          ),
        })}
      />
      <Tab.Screen
        name="MaketFram"
        component={MaketFram}
        options={({ route, navigation }) => ({
          tabBarIcon: ({ focused }) =>
            !focused ? tabPinGobal() : tabPinGobalChange(),
          tabBarButton: (props: any) => (
            <TouchableOpacity
              {...props}
              onPress={(e) => {
                props.onPress?.(e);
                setSelectedTab("");
              }}
            />
          ),
          orientation: "portrait",
          gestureEnabled: false,
        })}
      />
      {/* {!selectedTab ? ( */}
      <Tab.Screen
        name="PlusArea"
        component={Plusarea}
        options={({ route, navigation }) => ({
          tabBarIcon: ({ focused }) => onBotton(focused, route, navigation),
          tabBarButton: (props: any) => (
            <View
              style={
                orientation === "portrait"
                  ? styles.continueBotton
                  : styles.continuelandscape
              }
            >
              {tabBgCenter()}
              <TouchableOpacity
                {...props}
                onPress={(e) => {
                  // navigation.navigate("PlusArea");
                  props.onPress?.(e);
                  setSelectedTab("");
                }}
              />
            </View>
          ),
        })}
      />
      <Tab.Screen
        name="Noti"
        component={Noti}
        options={({ route, navigation }) => ({
          tabBarBadge: notificationCount > 0 ? notificationCount : undefined,
          tabBarBadgeStyle: {
            backgroundColor: "#FF3B30",
            color: "white",
            fontSize: 10,
          },
          tabBarIcon: ({ focused }) => {
            return <>{!focused ? tabBell() : tabBellChange()}</>;
          },
          tabBarButton: (props: any) => (
            <TouchableOpacity
              {...props}
              onPress={() => {
                setSelectedTab("");
                navigation.navigate("Noti");
              }}
            />
          ),
        })}
      />
      <Tab.Screen
        name="profile"
        component={profile}
        options={({ route, navigation }) => ({
          tabBarIcon: ({ focused }) => {
            return <>{!focused ? tabUser() : tabUserChange()}</>;
          },
          tabBarButton: (props: any) => (
            <TouchableOpacity
              {...props}
              onPress={(e) => {
                props.onPress?.(e);
                setSelectedTab("");
              }}
            />
          ),
        })}
      />
    </Tab.Navigator>
  );
}

const styles = ScaledSheet.create({
  barStyle: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: BgColor.Bg_84B8A2,
    borderTopWidth: 0,
  },
  continueBotton: {
    bottom: Platform.select({
      ios: screenHeight * 0.031,
      android: screenHeight * 0.032,
    }),
    alignItems: "center",
    justifyContent: "center",
  },
  continuelandscape: {
    marginTop: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  bottonCenter: {
    width: "35@ms",
    height: "35@ms",
    bottom: "55@ms",
    borderRadius: 180,
    overflow: "hidden",
    alignItems: "center",
    justifyContent: "center",
  },
});
