import React from "react";
import { Head<PERSON> as <PERSON>er<PERSON><PERSON> } from "@rneui/themed";
import { BgColor } from "../../styleSheet/style_Custom";
import { StyleSheet, TouchableOpacity, StatusBar, Text } from "react-native";
//Style Sheet
import txt from "../../styleSheet/txt";
import fonstStyle, {
  FonstColor,
  BgOpacity,
} from "../../styleSheet/style_Custom";
//Sev
import { goBack_gay } from "../../assets/svg/svg_naviagte";

export default function Setting_Bar({ onBack, pageName }: any) {
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={centerHeader()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={onBack}>{goBack_gay()}</TouchableOpacity>
  );
  const centerHeader = () => (
    <Text style={[fonstStyle.f16_bold, txt.txt_616161, {marginTop: 10}]} numberOfLines={1}>{pageName}</Text>
  );
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
    </>
  );
}

const styles = StyleSheet.create({
  bgBack: {
    paddingVertical: 8,
    paddingHorizontal: 9,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_ffffff5,
  },
  text666666: {
    color: FonstColor.Tc_666666,
  },
});
