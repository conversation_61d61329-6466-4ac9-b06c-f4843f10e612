
import React from "react";
import { Header as HeaderRNE } from "@rneui/themed";
import { BgColor } from "../../styleSheet/style_Custom";
import { StyleSheet, TouchableOpacity, Text, View, StatusBar } from "react-native";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";
//Style Sheet
import fonstStyle from "../../styleSheet/style_Custom";
import txt from "../../styleSheet/txt";
//Svg
import { goBack_gay } from "../../assets/svg/svg_naviagte";

export default function Manage_Bar({ onBack, pageName }: any) {
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={centerHeader()}
    />
  );
  
  const lefHeader = () => (
    <TouchableOpacity onPress={onBack}>{goBack_gay()}</TouchableOpacity>
  );
  
  const centerHeader = () => (
    <Text style={[fonstStyle.f16_bold, txt.txt_616161, {marginTop: 10}]} numberOfLines={1}>
      {pageName}
    </Text>
  );
  
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
    </>
  );
}

const styles = StyleSheet.create({
  continue: {
    paddingHorizontal: moderateScale(10),
  },
});

