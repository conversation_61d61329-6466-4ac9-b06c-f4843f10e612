import React from "react";
import { Header as <PERSON>erRNE } from "@rneui/themed";
import { BgColor } from "../../styleSheet/style_Custom";
import { StyleSheet, StatusBar, Image } from "react-native";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";
import Images from "../../utils/imageManager";

export default function Null_Bar({ onBack }: any) {
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      backgroundImage={Images.bgApp}
    />
  );
  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
    </>
  );
}

const styles = StyleSheet.create({
  continue: {
    paddingHorizontal: moderateScale(10),
  },
});
