import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from "react-native";
import moment from "moment";
import { moderateScale } from "react-native-size-matters";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "../../screen/i18n";
import { VLCPlayer } from "react-native-vlc-media-player";
import BottomSheet from "@gorhom/bottom-sheet";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
import txt from "../../styleSheet/txt";
import { useOrientation } from "../../hooks/useOrientation";
import {
  iconUpCCTV,
  iconFullscreen,
  iconCapterCCTV,
  iconLeftCCTV,
  iconRightCCTV,
  iconDowntCCTV,
  iconResetCCTV,
  iconZoomInCCTV,
  iconZoomOutCCTV,
  iconCloseContorl,
} from "../../assets/svg/svg_other";
import { postUploadStream, getImageFram } from "../../action/Mefarm_Farm_API";
import { setImagesShow } from "../../Redux_Store/action";
import { putActionCctv } from "../../action/Mefarm_IoT_API";
import Orientation from "react-native-orientation-locker";
const windowHeight = Dimensions.get("window").height;

interface CCTVPlayerProps {
  streamUrl: string;
  onError?: (error: any) => void;
  onLoaded?: () => void;
  paused?: boolean;
  onClose?: () => void;
  farmUserPlotId?: string;
  type?: string;
  isPages?: string;
  onChangeType?: (type: string) => void;
  onCaptureSuccess?: (timestamp: number) => void;
}

const CCTVPlayer = ({
  streamUrl,
  onError,
  onLoaded,
  paused = false,
  onClose,
  farmUserPlotId,
  type,
  isPages,
  onChangeType,
  onCaptureSuccess,
}: CCTVPlayerProps) => {
  const vlcRef = useRef<any>(null);
  const orientation = useOrientation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showControl, setShowControl] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const reduxFarmUserPlotId = useSelector((state: any) => state.farmUserPlotId);
  const imagesShow = useSelector((state: any) => state.imagesShow);
  const [pageSize, setPageSize] = useState<number>(10);

  useEffect(() => {
    if (farmUserPlotId) {
      dispatch({ type: "SET_FARM_USER_PLOT_ID", payload: farmUserPlotId });
    }
  }, [farmUserPlotId, dispatch]);
  const callGallery = async () => {
    try {
      const minDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      const pageSizes = pageSize;
      const res = await getImageFram(farmUserPlotId, minDate, pageSizes);
      const dataImags = res.model || "";
      const imageUrls = dataImags.map((item: any) => ({
        id: item.id,
        thumbnailUrl: item.thumbnailUrl,
      }));
      dispatch(setImagesShow(imageUrls));
    } catch (error) {
      console.log(error);
    } finally {
    }
  };
  const actionCctv = async (type: string) => {
    try {
      setIsLoading(true);
      const res = await putActionCctv(reduxFarmUserPlotId, type);
      console.log(res);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };
  const capterCctv = async () => {
    try {
      setIsLoading(true);
      const req = {
        farmUserPlotId: reduxFarmUserPlotId,
        streamUrl: streamUrl,
      };
      await postUploadStream(req);
      if (onChangeType) {
        onChangeType("gallery");
        await callGallery();
        if (onCaptureSuccess) {
          onCaptureSuccess(Date.now());
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };
  const renderDirectionControl = () => (
    <View style={styles.directionControlContainer}>
      <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
        {t("camera_control")}
      </Text>
      <TouchableOpacity
        style={{ position: "absolute", top: 10, right: 10 }}
        onPress={() => setShowControl(!showControl)}
      >
        {iconCloseContorl()}
      </TouchableOpacity>
      <View style={styles.directionPad}>
        <TouchableOpacity
          style={[styles.directionButton, styles.upButton]}
          onPress={() => actionCctv("up")}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          {iconUpCCTV()}
        </TouchableOpacity>
        <View style={styles.middleRow}>
          <TouchableOpacity
            style={[styles.directionButton, styles.leftButton]}
            onPress={() => actionCctv("left")}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          >
            {iconLeftCCTV()}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.centerButton}
            onPress={() => actionCctv("reset")}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          >
            {iconResetCCTV()}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => actionCctv("right")}
            style={[styles.directionButton, styles.rightButton]}
          >
            {iconRightCCTV()}
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={[styles.directionButton, styles.downButton]}
          onPress={() => actionCctv("down")}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          {iconDowntCCTV()}
        </TouchableOpacity>
      </View>

      {/* {Bottom} */}
      <View
        style={
          isPages === "MaketFram"
            ? styles.controlButtonsPage
            : styles.controlButtonsContainer
        }
      >
        <View style={styles.controlButtonsRow}>
          <TouchableOpacity
            onPress={() => actionCctv("zoomin")}
            style={styles.spacDivider}
          >
            {iconZoomInCCTV()}
          </TouchableOpacity>
          <View style={isPages === "MaketFram" ? null : styles.divider} />
          <TouchableOpacity
            onPress={() => capterCctv()}
            style={isPages === "MaketFram" ? null : styles.spacDivider}
          >
            {isPages === "MaketFram" ? null : iconCapterCCTV()}
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity
            onPress={() => actionCctv("zoomout")}
            style={styles.spacDivider}
          >
            {iconZoomOutCCTV()}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (isLoading) {
      timeout = setTimeout(() => {
        if (isLoading) {
          setIsLoading(false);
        }
      }, 1000);
    }
    return () => clearTimeout(timeout);
  }, [isLoading]);

  return (
    <>
      <View style={styles.container}>
        <View style={styles.videoContainer}>
          {/* แสดงวิดีโอปกติ ถ้าไม่ fullscreen */}
          {!isFullscreen && (
            <>
              <VLCPlayer
                ref={vlcRef}
                style={styles.videoPlayer}
                source={{ uri: streamUrl }}
                autoplay={true}
                onBuffering={() => {
                  setIsLoading(true);
                  setHasError(false);
                }}
                onPlaying={() => {
                  setIsLoading(false);
                  setHasError(false);
                  if (onLoaded) onLoaded();
                  console.log("VLCPlayer is playing");
                }}
                onError={(e) => {
                  setHasError(true);
                  setIsLoading(false);
                  setErrorMessage(t("unable_to_play"));
                  if (onError) onError(e);
                  console.log("VLCPlayer error", e);
                }}
                onStopped={() => {
                  setIsLoading(false);
                  setHasError(false);
                  console.log("VLCPlayer stopped");
                }}
                onEnd={() => {
                  setIsLoading(false);
                  setHasError(false);
                  console.log("VLCPlayer ended");
                }}
                paused={paused}
              />
              <TouchableOpacity
                style={styles.cardFullScreen}
                onPress={() => setIsFullscreen(true)}
              >
                {iconFullscreen()}
              </TouchableOpacity>
            </>
          )}
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#ffffff" />
              <Text style={styles.loadingText}>{t("loading_cctv")}</Text>
            </View>
          )}
          {hasError && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{t("cctv_error")}</Text>
              <Text style={styles.errorMessage}>{errorMessage}</Text>
            </View>
          )}
        </View>
        {showControl && renderDirectionControl()}
      </View>
      {/* Modal สำหรับ Fullscreen */}
      <Modal
        visible={isFullscreen}
        animationType="fade"
        supportedOrientations={["landscape", "portrait"]}
        onRequestClose={() => setIsFullscreen(false)}
        transparent={false}
      >
        <View style={{ flex: 1, backgroundColor: "#000" }}>
          <VLCPlayer
            ref={vlcRef}
            style={{ flex: 1, width: "100%", height: "100%" }}
            source={{ uri: streamUrl }}
            autoplay={true}
            onBuffering={() => {
              setIsLoading(true);
              setHasError(false);
            }}
            onPlaying={() => {
              setIsLoading(false);
              setHasError(false);
              if (onLoaded) onLoaded();
              console.log("VLCPlayer is playing");
            }}
            onError={(e) => {
              setHasError(true);
              setIsLoading(false);
              setErrorMessage(t("unable_to_play"));
              if (onError) onError(e);
              console.log("VLCPlayer error", e);
            }}
            onStopped={() => {
              setIsLoading(false);
              setHasError(false);
              console.log("VLCPlayer stopped");
            }}
            onEnd={() => {
              setIsLoading(false);
              setHasError(false);
              console.log("VLCPlayer ended");
            }}
            paused={paused}
          />
          <TouchableOpacity
            style={
              orientation === "portrait"
                ? styles.cardFullScreenModal
                : styles.cardFullScreenModalLan
            }
            onPress={() => setIsFullscreen(false)}
          >
            {iconFullscreen()}
          </TouchableOpacity>
        </View>
      </Modal>
      {!showControl && orientation === "portrait" &&(
        <View style={styles.containerBotton}>
          <TouchableOpacity
            style={styles.cardBotton}
            onPress={() => setShowControl(!showControl)}
          >
            <Text style={[fonstStyle.f14_bold, { color: "white" }]}>
              {t("camera_control")}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  headerTitle: {
    ...fonstStyle.f16_bold,
    color: "#fff",
  },
  menuButton: {
    padding: 4,
  },
  containerBotton: {
    width: "100%",
    alignItems: "center",
    backgroundColor: "black",
  },
  cardBotton: {
    borderWidth: 1,
    borderColor: "white",
    alignItems: "center",
    marginBottom: 25,
    paddingHorizontal: 40,
    paddingVertical: 10,
    borderRadius: 180,
  },
  qualityInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#000",
  },
  qualityText: {
    ...fonstStyle.f14_light,
    color: "#fff",
  },
  signalText: {
    ...fonstStyle.f14_light,
    color: "#fff",
  },
  videoContainer: {
    flex: 1,
    backgroundColor: "#000",
  },
  videoPlayer: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  loadingText: {
    ...fonstStyle.f14_light,
    color: "#fff",
    marginTop: moderateScale(10),
  },
  errorContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.7)",
  },
  errorText: {
    ...fonstStyle.f16_bold,
    color: "#fff",
    marginBottom: moderateScale(10),
  },
  errorMessage: {
    ...fonstStyle.f14_light,
    color: "#fff",
    textAlign: "center",
    paddingHorizontal: moderateScale(20),
  },
  controlsContainer: {
    padding: 16,
    backgroundColor: "#f5f5f5",
  },
  controlsRow: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  controlButton: {
    padding: 10,
  },
  directionControlContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
  directionTitle: {
    ...fonstStyle.f16_bold,
    color: "#555",
  },
  directionPad: {
    width: 200,
    height: 200,
    marginTop: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderRadius: 180,
    padding: 20,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  directionButton: {
    width: null,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  upButton: {
    marginBottom: 20,
  },
  middleRow: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    justifyContent: "space-between",
  },
  leftButton: {
    marginRight: 20,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  rightButton: {
    marginLeft: 20,
  },
  downButton: {
    marginTop: 20,
  },
  doneButton: {
    marginTop: 24,
    backgroundColor: "#5d6b7d",
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 8,
  },
  doneButtonText: {
    ...fonstStyle.f16_bold,
    color: "#fff",
  },
  controlButtonsContainer: {
    backgroundColor: BgColor.Bg_FFFFFF,
    alignItems: "center",
    width: "50%",
    marginTop: 25,
    borderRadius: 180,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  controlButtonsPage: {
    backgroundColor: BgColor.Bg_FFFFFF,
    alignItems: "center",
    width: "35%",
    marginTop: 25,
    borderRadius: 180,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  controlButtonsRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  divider: {
    margin: 10,
    backgroundColor: "#d6d6d6",
    width: 2,
    height: 30,
  },
  spacDivider: {
    marginLeft: 10,
    marginRight: 10,
  },
  cardFullScreen: {
    position: "absolute",
    marginTop: windowHeight * 0.01,
    right: 0,
    borderRadius: 180,
    padding: 8,
    zIndex: 10,
  },
  cardFullScreenModal: {
    position: "absolute",
    marginTop: windowHeight * 0.075,
    right: 10,
    borderRadius: 20,
    padding: 8,
    zIndex: 10,
  },
  cardFullScreenModalLan: {
    position: "absolute",
    marginTop: windowHeight * 0.01,
    right: 40,
    borderRadius: 20,
    padding: 8,
    zIndex: 10,
  },
});

export default CCTVPlayer;
