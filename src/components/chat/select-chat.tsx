import { StyleSheet, Text, View, Image, TouchableOpacity } from "react-native";
import React, { useRef, useMemo } from "react";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import Images from "../../utils/imageManager";
import txt from "../../styleSheet/txt";
import fonstStyle, { BgColor, BgOpacity } from "../../styleSheet/style_Custom";
//Translation
import { useTranslation } from "../../screen/i18n";

export default function SelectChat({ onCloseChat, navigation }: any) {
  const { t } = useTranslation();
  const snapChatFram = useMemo(() => ["40%"], []);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const goChat = (type: string) => {
    onCloseChat();
    navigation.navigate("ChatSystem", { type: type });
  };
  return (
    <BottomSheet
      index={0}
      ref={bottomSheetRef}
      snapPoints={snapChatFram}
      enablePanDownToClose={true}
      onClose={onCloseChat}
      backdropComponent={() => (
        <View style={{ backgroundColor: BgOpacity.Op_0000005, flex: 1 }}></View>
      )}
    >
      <BottomSheetView style={styles.continue}>
        <TouchableOpacity style={styles.botton} onPress={() => goChat("Admin")}>
          <Image
            style={styles.img}
            source={Images.chatService}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
          {t("adminChat")}
        </Text>

        <View style={{ margin: 20 }} />

        <TouchableOpacity
          style={[styles.botton]}
          onPress={() => goChat("Ai")}
        >
          <Image
            style={styles.img}
            source={Images.chatAi}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>{t("aiChat")}</Text>
      </BottomSheetView>
    </BottomSheet>
  );
}

const styles = StyleSheet.create({
  continue: { flex: 1, alignItems: "center", justifyContent: "center" },
  botton: {
    padding: 10,
    borderRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
    shadowColor: "#000",
    marginBottom: 10,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  img: {
    width: 50,
    height: 50,
  },
});
