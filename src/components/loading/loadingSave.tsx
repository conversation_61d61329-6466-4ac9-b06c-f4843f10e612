import React from "react";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import txt from "../../styleSheet/txt";
import { moderateScale } from "react-native-size-matters";
//Translation
import { useTranslation } from "../../screen/i18n";

export default function LoadingSave() {
  const { t } = useTranslation();

  return (
    <View style={[styles.container]}>
      <ActivityIndicator size="large" color={BgColor.Bg_B3DBC0} />
      <View style={{ margin: moderateScale(10) }} />
      <Text
        style={[fonstStyle.f16_medium, txt.txt_white, { textAlign: "center" }]}
      >
        {t("Please_exit")}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "(rgba(0,0,0,0.5))",
  },
});
