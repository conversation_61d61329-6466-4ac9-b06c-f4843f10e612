import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import React from "react";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Translation
import { useTranslation } from "../../screen/i18n";

export default function LoadingDetail() {
  const { t } = useTranslation();

  return (
    <View style={[styles.container]}>
      <ActivityIndicator size="large" color={BgColor.Bg_B3DBC0} />
      <Text style={[fonstStyle.f12_light, { marginTop: 10 }]}>{t('loading')}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
});
