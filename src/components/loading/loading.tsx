import React from "react";
import { BgColor } from "../../styleSheet/style_Custom";
import {
  View,
  StyleSheet,
  ActivityIndicator,
} from "react-native";

export default function Loading() {
  return (
    <View style={[styles.container]}>
      <ActivityIndicator size="large" color={BgColor.Bg_B3DBC0} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "(rgba(0,0,0,0.5))",
  },
});
