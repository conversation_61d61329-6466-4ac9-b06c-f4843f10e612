import axios from "axios";
import { API_ROOT } from "../constants/api";

export const registerApi = (data) =>{
    return axios({
      method: 'post',
      url: `${API_ROOT}/identity/register`,
      data: data
    }).then((res) => {
      return res;
    }).catch((error) => {
      console.log(error)
      return error;
    })
  }

  export const logInApi = (data) =>{
    return axios({
      method: 'post',
      url: `${API_ROOT}/identity/login`,
      data: data
    }).then((res) => {
      return res.data;
    }).catch((error) => {
      console.log(error)
      return error;
    })
  }