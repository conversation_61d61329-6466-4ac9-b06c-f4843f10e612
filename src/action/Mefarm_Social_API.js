import axios from "axios";
import { API_ROOT } from "../constants/api";
import { getDasBoard } from "../utils/axiosutisl";

//Dashboard Screen
//สร้าง post ใหม่ เมื่อสร้างเสร็จจะถูกส่งเข้า social hub
export const newPostApi = async (data) => {
  try {
    const response = await axios({
      method: "post",
      url: `${API_ROOT}/social/post/create`,
      data,
    });
    console.log("API Response:", response.data); // Log the response
    return response.data;
  } catch (error) {
    console.error("API Error:", error); // Enhanced logging
    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.error("Response error:", error.response.data); // Log response data
      }
      if (error.request) {
        console.error("Request error:", error.request); // Log the request if no response
      }
      return { message: error.message || "Request failed" };
    } else {
      console.error("Unexpected Error:", error);
      return { message: "An unexpected error occurred." };
    }
  }
};

//แก้ไข post เมื่อแก้ไขเสร็จจะถูกส่งเข้า social hub
export const upDatePostApi = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/social/post/update`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//ลบ post เมื่อลบเสร็จะถูกส่งข้อมูลเข้า social hub
export const deletePostApi = (socialPostId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/social/post/delete/${socialPostId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//สร้าง comment ใหม่ เมื่อสร้างเสร็จจะถูกส่งเข้า social hub
export const commentApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/social/post/comment/create`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//แก้ไข comment เมื่อแก้ไขเสร็จจะถูกส่งเข้า social hub
export const upDateCommentApi = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/social/post/comment/update`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//ลบ comment เมื่อลบเสร็จะถูกส่งข้อมูลเข้า social hub
export const deleteCommentApi = (commentId) => {
  console.log("commentId...", commentId);
  
  return axios({
    method: "delete",
    url: `${API_ROOT}/social/post/comment/delete/${commentId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//สร้าง replie comment ใหม่ เมื่อสร้างเสร็จจะถูกส่งเข้า social hub
export const reCommentApi = (formData) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/social/post/replie/create`,
    data: formData,
    headers: { "Content-Type": "multipart/form-data" },
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//แก้ไข replie comment เมื่อแก้ไขเสร็จจะถูกส่งเข้า social hub
export const upDateReplyApi = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/social/post/replie/update`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//ลบ replie comment เมื่อลบเสร็จะถูกส่งข้อมูลเข้า social hub (ยังไม่ได้ใช้)
export const deleteReplieComment = (commentId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/social/post/replie/delete/${commentId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

// บันทึกรายการ shared เมื่อสร้างเสร็จจะถูกส่งเข้า social hub
export const shareSocial = (socialPostId) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/social/post/share/create/${socialPostId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};



//Test
export const getDasBoardApi = () => {
  const url = `${API_ROOT}/realtime/test/post-data`;
  return getDasBoard(url);
};
