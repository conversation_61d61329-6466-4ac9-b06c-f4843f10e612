import * as CryptoJS from "crypto-js";

const keyGen = {
  key: "X9dVuRRNMfAi+4nTXjJPQirVCuyvtBFyVG05vMo7kpY=",
  iv: "jLsXRQFeo0wq9dK5NDfvgQ==",
};

export function encrypt(plaintext) {
  if (plaintext == null) {
    return null;
  }
  var key = CryptoJS.enc.Base64.parse(keyGen.key);
  let iv = CryptoJS.enc.Base64.parse(keyGen.iv);

  var cipherText = CryptoJS.AES.encrypt(plaintext, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return cipherText.toString();
}

export function decrypt(cipherText) {
  if (cipherText == null) {
    return null;
  }
  var key = CryptoJS.enc.Base64.parse(keyGen.key);
  let iv = CryptoJS.enc.Base64.parse(keyGen.iv);

  var cipherBytes = CryptoJS.enc.Base64.parse(cipherText);

  var decrypted = CryptoJS.AES.decrypt({ ciphertext: cipherBytes }, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}
