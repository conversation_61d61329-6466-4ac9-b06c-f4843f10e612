import axios from "axios";
import { API_ROOT } from "../constants/api";
import { doGetPackage } from "../utils/axiosutisl";

//Farm User Plot Images Screen
//อัพโหลดรูป หรือ วีดีโอ สำหรับแปลงนั้นๆ
export const postUploadImages = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/images/upload`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//สั่ง snapshot จาก stream url สำหรับแปลงนั้นๆ
export const postUploadStream = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/images/upload-from-stream`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//สำหรับแสดงรายการภาพที่อัพโหลดเข้ามาในระบบ ของแปลงเพาะปลูกนั้นๆ
export const getImageFram = (farmUserPlotId, minDate, pageSize) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/user/plot/images/upload/${farmUserPlotId}/${minDate}/${pageSize}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรูป หรือ วีดีโอ สำหรับแปลงนั้นๆ
export const deleteFarmImgVideo = (data) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/farm/user/plot/images-videos/delete`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Selection Screen
//เรียกดูรายการ farm ระบุจำนวนข้อมูลที่ pageSize และ minUpdatedAt กรณีครั้งแรกให้ใช้วันเวลาปัจจุบัน หากค้นหาให้ใส่ที่ searchText
export const getFarmApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/list`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกดูรายละเอียด farm โดยใช้ FarmId โดยได้จาก list ดูรายละเอียดของ farm
export const getFarmDetailApi = (data) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/list/detail/${data}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Plot Selection Screen
//เรียกดูรายการ farm plot list แปลงย่อยในฟาร์ม โดยใช้ FarmId โดยได้จาก list
export const getPoltFarmApi = (isFarmId) => {
  // console.log(isFarmId);
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/plot/list/${isFarmId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกดูรายละเอียด farm plot โดยใช้ FarmPlotId โดยได้จาก plot/list
export const getPoltDetailApi = (isPoltId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/plot/detail/${isPoltId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Package Selection Screen
//เรียกดูรายการ farm package ที่สามารถสมัครได้
export const getFarmPackAge = (poltId) => {
  // console.log("poltId>>>>", poltId);
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/package/list/${poltId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Package Confirm Screen
//เรียกดูรายการ ช่องทางการชำระเงิน
export const getPayMentApi = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/payment-method/list`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ส่งจองแพ็คเกจและแปลงปลูก
export const getFarmConfirmApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/package/reservation`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm User Screen
//ลบรายการจอง โดยส่ง Reservation Id เข้ามาในระบบ จะลบได้เฉพาะรายการที่รอชำระเงินเท่านั้น
export const deleteRestFarm = (reservationId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/farm/package/reservation/${reservationId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Package Payment Screen
//แจ้งการ ชำระเงิน สร้างข้อมูลแพ็คเกจ
export const registerFarmApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/package/register`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//แจ้งการ ชำระเงิน การต่อแพ็คเกจ
export const renewalApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/package/renewal`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm User Dashboard Screen
//แสดงรายละเอียดหน้า dashboard การจัดการฟาร์มของ user นั้นๆ
export const getFarmDasdoard = (data) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/dashboard`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm User Plot Screen
//แสดงรายละเอียดของแต่ละแปลง โดยนำ id ที่ได้จาก /api/farm/dashboard ใน myFarm ส่งเข้ามา
export const getMyFarmDetail = (farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/user/plot/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//อัพเดตชื่อแปลงย่อยของผู้ใช้เอง โดยผู้ใช้ต้องบันทึก ทั้งภาษาไทย และ อังกฤษ
export const updatePlotName = (data) => {
  // console.log("data...", data);
  return axios({
    method: "put",
    url: `${API_ROOT}/farm/user/plot/update-name`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ตั้งแปลงให้เป็นสาธารณะ โดยที่จะแสดงที่หน้าแปลงสาธิต ให้ผู้ใช้ทั่วไปเข้าดูได้ แต่จะไม่สามารถแก้ไขข้อมูลหรือสั่งการในแปลงได้
export const putPublic = (farmUserPlotId, isPublic) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/farm/user/plot/set-public/${farmUserPlotId}/${isPublic}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกดูรายการเมล็ดพันธุ์ ระบุจำนวนข้อมูลที่ pageSize และ minUpdatedAt กรณีครั้งแรกให้ใช้วันเวลาปัจจุบัน หากค้นหาให้ใส่ที่ searchText
export const seedApi = (data) => {
  // console.log("data...", data);
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/seed/list`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
// เรียกดูรายการละเอียดพืชจาก Id
export const getPlantDetail = (farmPlantId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/plant/${farmPlantId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ส่งข้อมูลเพื่อตรวจสอบพื้นที่การวาด Polygon ต้องอยู่ภายในแปลงย่อย และต้องไม่ซ้อนทับกัน
export const plantOverlap = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/planting-overlap-check`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เพิ่มรายการเพาะปลูก โดยกำหนด seed limit จาก package detail ในส่วนรายการเมล็ดพันธุ์ให้ใส่มาใน key PlantingList ข้อมูลที่ได้จาก seed/list เปลี่ยน key Id เป็น FarmSeedId
export const plantingApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/planting`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรายการเพาะปลูก ลบได้เฉพาะรายการที่ยังไม่ดำเนินการเตรียมดินเท่านั้น
export const deleteAreaApi = (idDelete) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/farm/user/plot/planting/${idDelete}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ใช้ยืนยันการปลูก โดยที่หลังจากยืนยันแล้ว จะทำการลบรายการที่ flag delete ออกไปทั้งหมด และเพิ่มข้อมูล request process
export const confirmPlanting = (farmUserPlotId) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/farm/user/plot/planting/confirm/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Management Request Screen
//เรียกดูรายการจัดการฟาร์ม ที่ยังไม่ได้ชำระเงิน
export const getWaitList = (farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/farm/user/plot/service/request/wait-list/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกดูรายการจัดการฟาร์ม ตามหมายเลข InvoiceNumber
//สร้างรายการขอจัดการฟาร์ม เพื่อเปิด Invoice Api นี้ ไม่รวมการแจ้งชำระเงิน
export const postRequestservice = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/service/request/create`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรายการ Invoice การจัดการฟาร์ม กรณีที่ สร้างแล้ว แต่ยังไม่แจ้งโอนเงิน และ Admin ยังไม่ Confirm
export const deleteInvoice = (idInvoice) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/farm/user/plot/service/request/delete/${idInvoice}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//แจ้งการ ชำระเงิน ค่าบริการจัดการฟาร์ม
export const postServiceManage = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/service/request/paid`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Farm Harvest Request Screen
//เรียกดูรายการเก็บเกี่ยว ที่ยังไม่ได้ชำระเงิน
//เรียกดูรายละเอียดการเก็บเกี่ยว ตามหมายเลข InvoiceNumber
//สร้างรายการแจ้งเก็บเกี่ยว เพื่อเปิด Invoice และคำนวนข้อมูล Api นี้ ไม่รวมการแจ้งชำระเงิน
export const postHarvest = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/harvest/request/create`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรายการ Invoice แจ้งเก็บเกี่ยว กรณีที่ สร้างแล้ว แต่ยังไม่แจ้งโอนเงิน และ Admin ยังไม่ Confirm
//แจ้งการ ชำระเงิน ค่าบริการและค่าขนส่งในการเก็บเกี่ยว
export const postRequestPaid = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/user/plot/harvest/request/paid`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Fram Public Dashboard Screen
//แสดงรายละเอียดรายการฟาร์มหน้าที่ 2 แบบสาธารณะ ค้นหาได้ กดเข้าไป แล้วก็เข้าไปหน้าฟาร์ม แต่เข้าไปแล้วจะส่งคำสั่งใดๆ ไม่ได้ดูได้อย่างเดียวใช้ key isViewOnly
export const postFramPublic = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/farm/public/plot/list`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//What??
export const getPackageApi = () => {
  const url = `${API_ROOT}/farm/package/list`;
  return doGetPackage(url);
};
