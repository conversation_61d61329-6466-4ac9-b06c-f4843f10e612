import axios from "axios";
import moment from "moment";
import { API_ROOT } from "../constants/api";

//สั่งการรดน้ำของอุปกรณ์ IoT ทันที
export const wateringApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/iot/watering/${data}`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ตรวจสอบสถานะสั่งรดน้ำ หากมีค่าแสดงว่า กำลังรดน้ำอยู่ แต่หาก = null ให้สามารถสั่งรดน้ำทันทีได้
export const waterState = (farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/iot/watering-state/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

///api/iot/config/watering/schedule/{farmUserPlotId}(ยังไม่ใช้)

//เรียกดูบันทึกการสั่งการรดน้ำ ระบุจำนวน pageSize และ updatedAt ล่าสุดส่งมาด้วย
export const wateringStatus = (farmUserPlotId, pageSize) => {
  const updatedAt = moment().local().format("YYYY-MM-DD HH:mm:ss");  
  return axios({
    method: "get",
    url: `${API_ROOT}/iot/config/watering/schedules/${farmUserPlotId}/${pageSize}/${updatedAt}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//เพิ่มการตั้งค่าการรดน้ำล่วงหน้าของอุปกรณ์ IoT
export const wateringAddApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/iot/config/watering/schedule/add`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//อัพเดตการตั้งค่าการรดน้ำล่วงหน้าของอุปกรณ์ IoT
export const updateWaterApi = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/iot/config/watering/schedule/update`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรายการการตั้งค่าการทำงานล่วงหน้าของอุปกรณ์ IoT
export const deleteConfig = (configId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/iot/config/schedule/delete/${configId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

export const putActionCctv = (reduxFarmUserPlotId, type) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/iot/camera-control/${reduxFarmUserPlotId}/${type}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

