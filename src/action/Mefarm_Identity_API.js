import { API_ROOT } from "../constants/api";
import axios from "axios";
import { doGet, doPut, doGetAllImg } from "../utils/axiosutisl";

//Register Screen
//ลงทะเบียนผู้ใช้ ข้อมูลที่ต้องเข้ารหัสคือ userName,password,confirmPassword,firstName,address,mobileNo,telephonNo,lineId,email,dateOfBirth,dateOfJoin ชื่อผู้ใช้ใส่มาใน firstName ถ้าไม่มีส่ง null หรือตัด key นั้นออกไปเลย
export const registerApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/register`,
    data: data,
  })
    .then((res) => {
      return res;
    })
    .catch((error) => {
      console.log(error);
      return error;
    });
};
//สมัครด้วย third party OAuth ให้ระบุ Provider เช่น Google Apple หรือ Facebook และส่ง token มาที่ api นี้
export const registerThirdparty = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/third-party-register`,
    data: data,
  })
    .then((res) => {
      // ล็อกข้อมูลที่ได้รับจาก API
      // console.log("API Response:", res.data); // แสดงผลลัพธ์จาก response data
      return res.data;
    })
    .catch((error) => {
      // ล็อกข้อผิดพลาดที่เกิดขึ้น
      // console.error("Error in API call:", error); // แสดงข้อความข้อผิดพลาดใน console
      return error;
    });
};

//Login Screen
//ลงชื่อเข้าใช้ ข้อมูลที่ต้องเข้ารหัสคือ userName และ password
export const logInApi = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/login`,
    data: data,
  })
    .then((res) => {
      return res.data;
    })
    .catch((error) => {
      console.log(error);
      return error;
    });
};
// ลงชื่อเข้าใช้ด้วย third party OAuth ให้ระบุ Provider เช่น Google Apple หรือ Facebook และส่ง token มาที่ api นี้
export const logInThirdParty = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/third-party-login`,
    data: data,
  })
    .then((res) => {
      return res.data;
    })
    .catch((error) => {
      console.log(error);
      return error;
    });
};

//Link Third Party Signin Screen
export const checkThird = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/api/identity/third-party-link`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};


//Landing Screen
//สำหรับการต่ออายุ token กรณี token ใกล้หมดอายุ
export const getRefreshToken = (refreshToken) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/identity/refresh-token`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};


//Profile Screen
//ข้อมูลโปรไฟล์ของผู้ใช้ มีการเข้ารหัสข้อมูลส่วนบุคคล
export const getProfileApi = () => {
  const url = `${API_ROOT}/identity/profile`;
  return doGet(url);
};
//ปรับปรุงโปรไฟล์ผู้ใช้ ไม่รวม userName, password ข้อมูลที่ต้องเข้ารหัสก่อนคือ firstName, address, mobileNo, email
export const saveProfileApi = (request) => {
  const url = `${API_ROOT}/identity/profile/update`;
  return doPut(url, request);
};
//แก้ไขรูปโปรไฟล์ผู้ใช้ ให้ส่งเฉพาะไฟล์ภาพเข้ามา เมื่อปรับปรุงแล้วจะ return ข้อมูล profile ล่าสุดกลับไป
export const upDateProfile = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/identity/profile/image/update`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบภาพโปรไฟล์ของผู้ใช้ เมื่อลบแล้วจะ return ข้อมูล profile ล่าสุดกลับไป
export const deleteImgProfile = () => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/identity/profile/image/delete`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//แก้ไขรูปหน้าปก ให้ส่งเฉพาะไฟล์ภาพเข้ามา เมื่อปรับปรุงแล้วจะ return ข้อมูล profile ล่าสุดกลับไป
export const upDateCover = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/identity/profile/cover-image/update`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบภาพหน้าปกของผู้ใช้ เมื่อลบแล้วจะ return ข้อมูล profile ล่าสุดกลับไป
export const deleteImgCover = () => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/identity/profile/cover-image/delete`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//CheckUserPassword Screen
//ปรับปรุงโปรไฟล์ผู้ใช้ เฉพาะ userName และ password ข้อมูลที่ต้องเข้ารหัสก่อนคือ userName, currentPassword, password, confirmPassword สำหรับการแก้ userName ส่งเฉพาะ userName มาก็พอ
export const ChangePass = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/identity/profile/user-password/update`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//PDPA Screen
//ปรับปรุงนโยบายคุ้มครองข้อมูลส่วนบุคคล ให้นำข้อมูลที่ส่งไปกับ profile แก้ไขแล้วส่งมาที่ api นี้ (ยังไม่ได้ใช้)

//DeleteAccount Screen
//ลบบัญชีผู้ใช้ เมื่อลบแล้ว สามารถสมัครโดยใช้ชื่อผู้ใช้เดิมได้
export const deleteUser = () => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/social/post/delete`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//PublicProfile Screen
//ข้อมูลผู้ใช้ โดยระบุ userId แบบเข้ารหัส เข้ามา โดยหาก userId ไม่ตรงกับผู้ขอข้อมูล จะแสดงข้อมูลตามที่ pdpa อนุญาตเท่านั้น (ยังไม่ได้ใช้)

//AddressBook Screen
//ค้นหารายการจังหวัด
export const postProvince = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/location/province`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ค้นหารายการอำเภอ ต้องระบุ provinceId
export const postDistrict = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/location/district`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ค้นหารายการตำบล ต้องระบุ districtId
export const postSubDistrict = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/location/sub-district`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกข้อมูล รายการอำเภอ ตำบล รหัสไปรษณีย์ โดยใช้ provinceId จะได้ข้อมูลของทั้งจังหวัดนั้น
export const getProvince = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/identity/location/data/{provinceId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//รายการที่อยู่สำหรับจัดส่งสินค้า
export const getAddressList = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/identity/address-book/list`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เพิ่มรายการที่อยู่สำหรับจัดส่งสินค้า
export const postAddressBook = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/identity/address-book/create`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//แก้ไขรายการที่อยู่สำหรับจัดส่งสินค้า
export const putAddressBook = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/identity/address-book/update`,
    data
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบรายการที่อยู่สำหรับจัดส่งสินค้า
export const deleteAddress = (addressId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/identity/address-book/delete/${addressId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Images Screen
//สำหรับแสดงรายการภาพที่อัพโหลดเข้ามาในระบบ
export const getAllImgApi = (newDate, pageSize) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/identity/upload/images/${newDate}/${pageSize}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Videos Screen
//สำหรับแสดงรายการวีดีโอที่อัพโหลดเข้ามาในระบบ
export const getAllVideoApi = (newDate, pageSize) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/identity/upload/videos/${newDate}/${pageSize}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Images Videos Screen
//สำหรับลบไฟล์อัพโหลดเข้ามาในระบบ (ยังไม่ได้ใช้)
