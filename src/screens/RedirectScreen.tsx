import React, { useEffect } from "react";
import { View, ActivityIndicator } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import LoadingFarm from "../components/loading/loadingFarm";

const RedirectScreen = ({ navigation }: any) => {
  useEffect(() => {
    const checkBookmark = async () => {
      try {
        const bookmark = await AsyncStorage.getItem("bookMark");
        // console.log(
        //   "Bookmark check in RedirectScreen:",
        //   bookmark ? "Found bookmark" : "No bookmark"
        // );

        if (bookmark) {
          // console.log("Navigating to PlusArea with screenType: Bookmarker");
          // Navigate to Bottom_Tab with PlusArea as the initial tab
          navigation.replace("Bottom_Tab", {
            screen: "PlusArea",
            params: { checkBookmark: true, screenType: "Bookmarker" },
          });
        } else {
          // console.log("Navigating to Bottom_Tab without bookmark");
          navigation.replace("Bottom_Tab");
        }
      } catch (error) {
        // console.error("Error checking bookmark:", error);
        navigation.replace("Bottom_Tab");
      }
    };

    checkBookmark();
  }, [navigation]);

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <LoadingFarm />
    </View>
  );
};

export default RedirectScreen;
