declare module 'react-native-nodemediaclient' {
  import { Component } from 'react';
  import { ViewProps } from 'react-native';

  export interface NodeCameraViewProps extends ViewProps {
    outputUrl: string;
    camera?: {
      cameraId?: number;
      cameraFrontMirror?: boolean;
    };
    audio?: {
      bitrate?: number;
      profile?: number;
      samplerate?: number;
    };
    video?: {
      preset?: number;
      bitrate?: number;
      profile?: number;
      fps?: number;
      videoFrontMirror?: boolean;
    };
    autopreview?: boolean;
    denoise?: boolean;
    dynamicRateEnable?: boolean;
    onStatus?: (code: number, msg: string) => void;
  }

  export interface NodePlayerViewProps extends ViewProps {
    inputUrl: string;
    bufferTime?: number;
    maxBufferTime?: number;
    autoplay?: boolean;
    scaleMode?: 'ScaleToFill' | 'ScaleAspectFit' | 'ScaleAspectFill';
    renderType?: 'SURFACEVIEW' | 'TEXTUREVIEW';
    cryptoKey?: string;
    onStatus?: (code: number, msg: string) => void;
  }

  export class NodeCameraView extends Component<NodeCameraViewProps> {
    start(): void;
    stop(): void;
    switchCamera(): void;
    flashEnable(enable: boolean): void;
    startPreview(): void;
    stopPreview(): void;
  }

  export class NodePlayerView extends Component<NodePlayerViewProps> {
    start(): void;
    stop(): void;
    pause(): void;
    resume(): void;
    seekTo(time: number): void;
  }
}