import { useState, useCallback } from "react";
import { decrypt } from "../action/encryption";
import { getProfileApi } from "../action/Mefarm_Identity_API";

// กำหนด interface ข้อมูลโปรไฟล์
export interface ProfileData {
  userId: string;
  firstName: string;
  dateOfBirth: string;
  mobileNo: string;
  email: string;
  address: string;
  profileImageUrl: string;
  coverImageUrl: string;
  userRoles: any[]; 
}

// กำหนด interface ผลลัพธ์การส่งข้อมูล
export interface ProfileResult {
  success: boolean;
  data?: ProfileData;
  error?: any;
  rawResponse?: any;
}

export const useProfileData = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  
  const fetchProfileData = useCallback(async (): Promise<ProfileResult> => {
    try {
      setIsLoading(true);
      const response = await getProfileApi();

      if (response?.success) {
        const userId = response.model.userId || "";
        const firstName = decrypt(response.model.firstName) || "";
        const dateOfBirth = decrypt(response.model.dateOfBirth) || "";
        const mobileNo = decrypt(response.model.mobileNo) || "";
        const email = decrypt(response.model.email) || "";
        const address = decrypt(response.model.address) || "";
        const profileImageUrl = response.model.profileImageUrl || "";
        const coverImageUrl = response.model.coverImageUrl || "";
        let userRoles = response.model.userRoles || [];
        if (typeof userRoles === "string") {
          try {
            userRoles = decrypt(userRoles);
            userRoles = JSON.parse(userRoles);
          } catch (decryptError) {
            console.error("Error decrypting userRoles:", decryptError);
            userRoles = [];
          }
        }

        const data: ProfileData = {
          userId,
          firstName,
          dateOfBirth,
          mobileNo,
          email,
          address,
          profileImageUrl,
          coverImageUrl,
          userRoles,
        };

        setProfileData(data);

        return { success: true, data, rawResponse: response };
      } else {
        return { success: false, error: "Failed to fetch profile data" };
      }
    } catch (error) {
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { isLoading, profileData, fetchProfileData };
};
