import { useState, useEffect } from "react";
import { getAuthTokens } from "../utils/authManager";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { setAccessToken, setUserId } from "../Redux_Store/action";

export const useAuthTokens = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  //Dispatch
  const dispatch = useDispatch();
  const accessToken = useSelector((state: any) => state.accessToken);
  const userId = useSelector((state: any) => state.userId);

  useEffect(() => {
    const fetchTokens = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const { tokenLogin, userIdLogin } = await getAuthTokens();
        dispatch(setAccessToken(tokenLogin));
        dispatch(setUserId(userIdLogin));
        // console.log(">>>>", tokenLogin );
      } catch (err) {
        console.error("Error in useAuthTokens:", err);
        setError(String(err));
      } finally {
        setIsLoading(false);
      }
    };

    fetchTokens();
  }, []);

  return { accessToken, userId, isLoading, error };
};
