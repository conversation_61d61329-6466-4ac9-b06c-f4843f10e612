import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';

export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  const getOrientation = () => {
    const { height, width } = Dimensions.get('window');
    return height >= width ? 'portrait' : 'landscape';
  };

  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(getOrientation());
    };

    updateOrientation();

    const subscription = Dimensions.addEventListener(
      'change',
      updateOrientation
    );

    return () => {
      subscription.remove();
    };
  }, []);

  return orientation;
};