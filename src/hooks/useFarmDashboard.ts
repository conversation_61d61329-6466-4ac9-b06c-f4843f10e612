import { useState } from "react";
import { useDispatch } from "react-redux";
import { getFarmDasdoard } from "../action/Mefarm_Farm_API";
import { 
  setDocMyFarm, 
  setDocMyFarmList, 
  setDocReserVation, 
  setRenewalList 
} from "../Redux_Store/action";

export const useFarmDashboard = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchFarmDashboard = async () => {
    try {
      setIsLoading(true);
      const response = await getFarmDasdoard();
      const docFarm = response.model || "";
      const listMyFarm = docFarm.myFarmList || [];
      const docReservationList = docFarm.myReservationList || [];
      const myRenewalList = docFarm.myRenewalList || [];
      
      dispatch(setDocMyFarm(docFarm));
      dispatch(setDocMyFarmList(listMyFarm));
      dispatch(setDocReserVation(docReservationList));
      dispatch(setRenewalList(myRenewalList));
      
      return {
        success: true,
        data: {
          docFarm,
          listMyFarm,
          docReservationList,
          myRenewalList
        }
      };
    } catch (error) {
      console.log(error);
      dispatch(setDocMyFarmList([]));
      dispatch(setDocReserVation([]));
      dispatch(setRenewalList([]));
      
      return {
        success: false,
        error
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    fetchFarmDashboard
  };
};