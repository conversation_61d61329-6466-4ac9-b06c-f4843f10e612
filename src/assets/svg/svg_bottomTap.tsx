import Svg, {
  G,
  Use,
  Line,
  Rect,
  Text,
  Path,
  Defs,
  Mask,
  Stop,
  TSpan,
  Image,
  Symbol,
  Circle,
  Pattern,
  Ellipse,
  Polygon,
  TextPath,
  Polyline,
  <PERSON>lipPath,
  LinearGradient,
  RadialGradient,
} from "react-native-svg";
import React from "react";
import { View } from "react-native";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";

export const tabHome = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 27"
        fill="none"
      >
        <Path
          d="M14.8525 0.567213C14.0494 -0.189071 12.7931 -0.189071 11.99 0.567213L1.89935 10.0694C1.27501 10.6574 0.921265 11.4754 0.921265 12.3312V23.8875C0.921265 25.6064 2.32037 27 4.04626 27H7.17126C8.89716 27 10.2963 25.6064 10.2963 23.8875V18.6999C10.2963 18.127 10.7626 17.6625 11.3379 17.6625H15.5046C16.0799 17.6625 16.5463 18.127 16.5463 18.6999V23.8875C16.5463 25.6064 17.9454 27 19.6713 27H22.7963C24.5221 27 25.9213 25.6064 25.9213 23.8875V12.3312C25.9213 11.4754 25.5675 10.6574 24.9431 10.0694L14.8525 0.567213Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const tabBell = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 27"
        fill="none"
      >
        <Path
          d="M24.4157 17.8009L22.0039 15.3757V9.39979C22.036 7.18068 21.2641 5.02491 19.8308 3.33051C18.3975 1.63612 16.3995 0.517538 14.2057 0.181312C12.9326 0.013647 11.6383 0.119429 10.4093 0.491598C9.18027 0.863768 8.04474 1.49377 7.07848 2.33956C6.11222 3.18536 5.33745 4.22749 4.80586 5.39644C4.27428 6.56539 3.9981 7.83427 3.99575 9.11841V15.3757L1.58394 17.8009C1.281 18.1089 1.07556 18.4994 0.993316 18.9235C0.911069 19.3476 0.95566 19.7865 1.12151 20.1855C1.28737 20.5844 1.56713 20.9255 1.92582 21.1663C2.28451 21.4071 2.70621 21.5368 3.13822 21.5392H7.64027V21.9948C7.70285 23.3551 8.30211 24.635 9.30673 25.5543C10.3113 26.4735 11.6394 26.957 12.9998 26.8988C14.3603 26.957 15.6883 26.4735 16.693 25.5543C17.6976 24.635 18.2968 23.3551 18.3594 21.9948V21.5392H22.8615C23.2935 21.5368 23.7152 21.4071 24.0739 21.1663C24.4326 20.9255 24.7123 20.5844 24.8782 20.1855C25.044 19.7865 25.0886 19.3476 25.0064 18.9235C24.9241 18.4994 24.7187 18.1089 24.4157 17.8009ZM15.6796 21.9948C15.6053 22.6393 15.2852 23.2303 14.786 23.6446C14.2868 24.0589 13.647 24.2647 12.9998 24.219C12.3527 24.2647 11.7129 24.0589 11.2137 23.6446C10.7145 23.2303 10.3944 22.6393 10.3201 21.9948V21.5392H15.6796V21.9948Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const tabUser = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 29"
        fill="none"
      >
        <Path
          d="M12.5786 14.5C16.5239 14.5 19.7215 11.3664 19.7215 7.5C19.7215 3.63359 16.5239 0.5 12.5786 0.5C8.6333 0.5 5.43576 3.63359 5.43576 7.5C5.43576 11.3664 8.6333 14.5 12.5786 14.5ZM17.5786 16.25H16.6467C15.4079 16.8078 14.0295 17.125 12.5786 17.125C11.1277 17.125 9.75495 16.8078 8.51053 16.25H7.57861C3.43799 16.25 0.0786133 19.5422 0.0786133 23.6V25.875C0.0786133 27.3242 1.27839 28.5 2.75718 28.5H22.4C23.8788 28.5 25.0786 27.3242 25.0786 25.875V23.6C25.0786 19.5422 21.7192 16.25 17.5786 16.25Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const TapCase = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 27 27"
        fill="none"
      >
        <Path
          d="M6.75618 7.39704H15.925H16.025V7.29704V4.77831V4.67831H15.925H6.33757L5.93762 2.07136C5.89165 1.73908 5.72411 1.43563 5.46737 1.2197C5.21719 1.00794 4.89854 0.894391 4.57083 0.900213H2.07187C1.71135 0.900213 1.36558 1.04343 1.11065 1.29836C0.85572 1.55329 0.7125 1.89905 0.7125 2.25958C0.7125 2.6201 0.85572 2.96586 1.11065 3.22079C1.36558 3.47572 1.71135 3.61894 2.07187 3.61894H3.42304L5.75198 18.7413L5.76226 18.8162C5.80681 19.1405 5.96699 19.4378 6.21331 19.6534C6.4662 19.8763 6.79334 19.9967 7.13042 19.9907H20.9625C21.2549 19.9907 21.5396 19.8964 21.7742 19.7218C22.0088 19.5472 22.1808 19.3016 22.2648 19.0215C22.2648 19.0215 22.2648 19.0215 22.2648 19.0215L24.6399 11.1039L24.6786 10.9751H24.5442H21.9146H21.8402L21.8188 11.0464L19.9511 17.2719H8.27446L6.75618 7.39704ZM10.2205 25.5205C10.7116 25.0294 10.9875 24.3633 10.9875 23.6688C10.9875 22.9742 10.7116 22.3082 10.2205 21.8171C9.72937 21.3259 9.06329 21.05 8.36875 21.05C7.67421 21.05 7.00813 21.3259 6.51701 21.8171C6.0259 22.3082 5.75 22.9742 5.75 23.6688C5.75 24.3633 6.0259 25.0294 6.51701 25.5205C7.00813 26.0116 7.67421 26.2875 8.36875 26.2875C9.06329 26.2875 9.72937 26.0116 10.2205 25.5205ZM21.5549 25.5205C22.046 25.0294 22.3219 24.3633 22.3219 23.6688C22.3219 22.9742 22.046 22.3082 21.5549 21.8171C21.0637 21.3259 20.3977 21.05 19.7031 21.05C19.0086 21.05 18.3425 21.3259 17.8514 21.8171C17.3603 22.3082 17.0844 22.9742 17.0844 23.6688C17.0844 24.3633 17.3603 25.0294 17.8514 25.5205C18.3425 26.0116 19.0086 26.2875 19.7031 26.2875C20.3977 26.2875 21.0637 26.0116 21.5549 25.5205ZM23.1831 1.29836C22.9282 1.04343 22.5824 0.900213 22.2219 0.900213C21.8613 0.900213 21.5156 1.04343 21.2607 1.29836C21.0057 1.55329 20.8625 1.89905 20.8625 2.25958V3.41894H19.7031C19.3426 3.41894 18.9968 3.56216 18.7419 3.81709C18.487 4.07202 18.3438 4.41778 18.3438 4.77831C18.3438 5.13883 18.487 5.48459 18.7419 5.73952C18.9968 5.99445 19.3426 6.13767 19.7031 6.13767H20.8625V7.29704C20.8625 7.65756 21.0057 8.00332 21.2607 8.25825C21.5156 8.51318 21.8613 8.6564 22.2219 8.6564C22.5824 8.6564 22.9282 8.51318 23.1831 8.25825C23.438 8.00332 23.5812 7.65756 23.5812 7.29704V6.13767H24.7406C25.1012 6.13767 25.4469 5.99445 25.7018 5.73952C25.9568 5.48459 26.1 5.13883 26.1 4.77831C26.1 4.41778 25.9568 4.07202 25.7018 3.81709C25.4469 3.56216 25.1012 3.41894 24.7406 3.41894H23.5812V2.25958C23.5812 1.89905 23.438 1.55329 23.1831 1.29836Z"
          fill="#6A938D"
          stroke="#6A938D"
          strokeWidth="0.2"
        />
        <Mask
          id="mask0_718_2311"
          maskUnits="userSpaceOnUse"
          x="-1"
          y="2"
          width="28"
          height="23"
        >
          <Rect
            x="-0.1"
            y="2.525"
            width="26.2"
            height="22.1375"
            fill="url(#pattern0_718_2311)"
            stroke="black"
            strokeWidth="0.2"
          />
        </Mask>
        <G mask="url(#mask0_718_2311)"></G>
        <Defs>
          <Pattern
            id="pattern0_718_2311"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <Use transform="matrix(0.00195312 0 0 0.00231481 0 -0.0266204)" />
          </Pattern>
          <Image id="image0_718_2311" width="512" height="455" />
        </Defs>
      </Svg>
    </View>
  );
};
export const tabPlans = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 31 25"
        fill="none"
      >
        <Path
          d="M4.67139 3.57143H0.921387C0.921387 10.4743 6.79834 16.0714 14.0464 16.0714V24.1071C14.0464 24.5982 14.4683 25 14.9839 25H16.8589C17.3745 25 17.7964 24.5982 17.7964 24.1071V16.0714C17.7964 9.16853 11.9194 3.57143 4.67139 3.57143ZM27.1714 0C22.2378 0 17.9487 2.59487 15.7046 6.42857C17.3276 8.11384 18.5288 10.1618 19.1616 12.433C25.7651 11.7801 30.9214 6.46763 30.9214 0H27.1714Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const tabStore = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 31 25"
        fill="none"
      >
        <Path
          d="M30.2402 6.05937L27.0791 1.20312C26.7966 0.767187 26.2949 0.5 25.7591 0.5H6.08148C5.5457 0.5 5.04401 0.767187 4.76151 1.20312L1.60042 6.05937C-0.0312656 8.56719 1.41534 12.0547 4.4644 12.4531C4.68358 12.4812 4.90764 12.4953 5.13169 12.4953C6.57342 12.4953 7.84954 11.8859 8.72627 10.9438C9.603 11.8859 10.884 12.4953 12.3209 12.4953C13.7626 12.4953 15.0387 11.8859 15.9154 10.9438C16.7922 11.8859 18.0732 12.4953 19.51 12.4953C20.9518 12.4953 22.2279 11.8859 23.1046 10.9438C23.9862 11.8859 25.2623 12.4953 26.6992 12.4953C26.9281 12.4953 27.1473 12.4812 27.3665 12.4531C30.4253 12.0594 31.8768 8.57187 30.2402 6.05937ZM26.7089 14C26.2219 14 25.7397 13.9297 25.2721 13.8219V18.5H6.56855V13.8219C6.10096 13.925 5.61876 14 5.13169 14C4.83945 14 4.54233 13.9812 4.25496 13.9437C3.9822 13.9062 3.71431 13.8453 3.45616 13.775V23C3.45616 23.8297 4.15267 24.5 5.01479 24.5H26.8356C27.6977 24.5 28.3942 23.8297 28.3942 23V13.775C28.1312 13.85 27.8682 13.9109 27.5954 13.9437C27.2983 13.9812 27.0061 14 26.7089 14Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const tabBgCenter = () => {
  return (
    <View>
      <Svg
        width={moderateScale(90)}
        height={moderateScale(70)}
        viewBox="0 0 147 77"
        fill="#84B8A2"
      >
        <Path
          id="Ellipse 39"
          d="M146.5 38.5C146.5 59.763 95.763 77 74.5 77C53.237 77 0.5 59.763 0.5 38.5C39 38.5 36 0 74.5 0C113 0 108 38.5 146.5 38.5Z"
          fill="#84B8A2"
        />
      </Svg>
    </View>
  );
};
export const TapCaseChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 27 27"
        fill="none"
      >
        <Path
          d="M6.75618 7.39704H15.925H16.025V7.29704V4.77831V4.67831H15.925H6.33757L5.93762 2.07136C5.89165 1.73908 5.72411 1.43563 5.46737 1.2197C5.21719 1.00794 4.89854 0.894391 4.57083 0.900213H2.07187C1.71135 0.900213 1.36558 1.04343 1.11065 1.29836C0.85572 1.55329 0.7125 1.89905 0.7125 2.25958C0.7125 2.6201 0.85572 2.96586 1.11065 3.22079C1.36558 3.47572 1.71135 3.61894 2.07187 3.61894H3.42304L5.75198 18.7413L5.76226 18.8162C5.80681 19.1405 5.96699 19.4378 6.21331 19.6534C6.4662 19.8763 6.79334 19.9967 7.13042 19.9907H20.9625C21.2549 19.9907 21.5396 19.8964 21.7742 19.7218C22.0088 19.5472 22.1808 19.3016 22.2648 19.0215C22.2648 19.0215 22.2648 19.0215 22.2648 19.0215L24.6399 11.1039L24.6786 10.9751H24.5442H21.9146H21.8402L21.8188 11.0464L19.9511 17.2719H8.27446L6.75618 7.39704ZM10.2205 25.5205C10.7116 25.0294 10.9875 24.3633 10.9875 23.6688C10.9875 22.9742 10.7116 22.3082 10.2205 21.8171C9.72937 21.3259 9.06329 21.05 8.36875 21.05C7.67421 21.05 7.00813 21.3259 6.51701 21.8171C6.0259 22.3082 5.75 22.9742 5.75 23.6688C5.75 24.3633 6.0259 25.0294 6.51701 25.5205C7.00813 26.0116 7.67421 26.2875 8.36875 26.2875C9.06329 26.2875 9.72937 26.0116 10.2205 25.5205ZM21.5549 25.5205C22.046 25.0294 22.3219 24.3633 22.3219 23.6688C22.3219 22.9742 22.046 22.3082 21.5549 21.8171C21.0637 21.3259 20.3977 21.05 19.7031 21.05C19.0086 21.05 18.3425 21.3259 17.8514 21.8171C17.3603 22.3082 17.0844 22.9742 17.0844 23.6688C17.0844 24.3633 17.3603 25.0294 17.8514 25.5205C18.3425 26.0116 19.0086 26.2875 19.7031 26.2875C20.3977 26.2875 21.0637 26.0116 21.5549 25.5205ZM23.1831 1.29836C22.9282 1.04343 22.5824 0.900213 22.2219 0.900213C21.8613 0.900213 21.5156 1.04343 21.2607 1.29836C21.0057 1.55329 20.8625 1.89905 20.8625 2.25958V3.41894H19.7031C19.3426 3.41894 18.9968 3.56216 18.7419 3.81709C18.487 4.07202 18.3438 4.41778 18.3438 4.77831C18.3438 5.13883 18.487 5.48459 18.7419 5.73952C18.9968 5.99445 19.3426 6.13767 19.7031 6.13767H20.8625V7.29704C20.8625 7.65756 21.0057 8.00332 21.2607 8.25825C21.5156 8.51318 21.8613 8.6564 22.2219 8.6564C22.5824 8.6564 22.9282 8.51318 23.1831 8.25825C23.438 8.00332 23.5812 7.65756 23.5812 7.29704V6.13767H24.7406C25.1012 6.13767 25.4469 5.99445 25.7018 5.73952C25.9568 5.48459 26.1 5.13883 26.1 4.77831C26.1 4.41778 25.9568 4.07202 25.7018 3.81709C25.4469 3.56216 25.1012 3.41894 24.7406 3.41894H23.5812V2.25958C23.5812 1.89905 23.438 1.55329 23.1831 1.29836Z"
          fill="#ffffff"
          stroke="#ffffff"
          strokeWidth="0.2"
        />
        <Mask
          id="mask0_718_2311"
          maskUnits="userSpaceOnUse"
          x="-1"
          y="2"
          width="28"
          height="23"
        >
          <Rect
            x="-0.1"
            y="2.525"
            width="26.2"
            height="22.1375"
            fill="url(#pattern0_718_2311)"
            stroke="black"
            strokeWidth="0.2"
          />
        </Mask>
        <G mask="url(#mask0_718_2311)"></G>
        <Defs>
          <Pattern
            id="pattern0_718_2311"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
          >
            <Use transform="matrix(0.00195312 0 0 0.00231481 0 -0.0266204)" />
          </Pattern>
          <Image id="image0_718_2311" width="512" height="455" />
        </Defs>
      </Svg>
    </View>
  );
};
export const tabHomeChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 27"
        fill="#6A938D"
      >
        <Path
          d="M14.8525 0.567213C14.0494 -0.189071 12.7931 -0.189071 11.99 0.567213L1.89935 10.0694C1.27501 10.6574 0.921265 11.4754 0.921265 12.3312V23.8875C0.921265 25.6064 2.32037 27 4.04626 27H7.17126C8.89716 27 10.2963 25.6064 10.2963 23.8875V18.6999C10.2963 18.127 10.7626 17.6625 11.3379 17.6625H15.5046C16.0799 17.6625 16.5463 18.127 16.5463 18.6999V23.8875C16.5463 25.6064 17.9454 27 19.6713 27H22.7963C24.5221 27 25.9213 25.6064 25.9213 23.8875V12.3312C25.9213 11.4754 25.5675 10.6574 24.9431 10.0694L14.8525 0.567213Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const tabBellChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 27"
        fill="#6A938D"
      >
        <Path
          d="M24.4157 17.8009L22.0039 15.3757V9.39979C22.036 7.18068 21.2641 5.02491 19.8308 3.33051C18.3975 1.63612 16.3995 0.517538 14.2057 0.181312C12.9326 0.013647 11.6383 0.119429 10.4093 0.491598C9.18027 0.863768 8.04474 1.49377 7.07848 2.33956C6.11222 3.18536 5.33745 4.22749 4.80586 5.39644C4.27428 6.56539 3.9981 7.83427 3.99575 9.11841V15.3757L1.58394 17.8009C1.281 18.1089 1.07556 18.4994 0.993316 18.9235C0.911069 19.3476 0.95566 19.7865 1.12151 20.1855C1.28737 20.5844 1.56713 20.9255 1.92582 21.1663C2.28451 21.4071 2.70621 21.5368 3.13822 21.5392H7.64027V21.9948C7.70285 23.3551 8.30211 24.635 9.30673 25.5543C10.3113 26.4735 11.6394 26.957 12.9998 26.8988C14.3603 26.957 15.6883 26.4735 16.693 25.5543C17.6976 24.635 18.2968 23.3551 18.3594 21.9948V21.5392H22.8615C23.2935 21.5368 23.7152 21.4071 24.0739 21.1663C24.4326 20.9255 24.7123 20.5844 24.8782 20.1855C25.044 19.7865 25.0886 19.3476 25.0064 18.9235C24.9241 18.4994 24.7187 18.1089 24.4157 17.8009ZM15.6796 21.9948C15.6053 22.6393 15.2852 23.2303 14.786 23.6446C14.2868 24.0589 13.647 24.2647 12.9998 24.219C12.3527 24.2647 11.7129 24.0589 11.2137 23.6446C10.7145 23.2303 10.3944 22.6393 10.3201 21.9948V21.5392H15.6796V21.9948Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const tabUserChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 26 29"
        fill="#6A938D"
      >
        <Path
          d="M12.5786 14.5C16.5239 14.5 19.7215 11.3664 19.7215 7.5C19.7215 3.63359 16.5239 0.5 12.5786 0.5C8.6333 0.5 5.43576 3.63359 5.43576 7.5C5.43576 11.3664 8.6333 14.5 12.5786 14.5ZM17.5786 16.25H16.6467C15.4079 16.8078 14.0295 17.125 12.5786 17.125C11.1277 17.125 9.75495 16.8078 8.51053 16.25H7.57861C3.43799 16.25 0.0786133 19.5422 0.0786133 23.6V25.875C0.0786133 27.3242 1.27839 28.5 2.75718 28.5H22.4C23.8788 28.5 25.0786 27.3242 25.0786 25.875V23.6C25.0786 19.5422 21.7192 16.25 17.5786 16.25Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const tabPlansChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 31 25"
        fill="#6A938D"
      >
        <Path
          d="M4.67126 3.57143H0.921265C0.921265 10.4743 6.79822 16.0714 14.0463 16.0714V24.1071C14.0463 24.5982 14.4681 25 14.9838 25H16.8588C17.3744 25 17.7963 24.5982 17.7963 24.1071V16.0714C17.7963 9.16853 11.9193 3.57143 4.67126 3.57143ZM27.1713 0C22.2377 0 17.9486 2.59487 15.7045 6.42857C17.3275 8.11384 18.5287 10.1618 19.1615 12.433C25.765 11.7801 30.9213 6.46763 30.9213 0H27.1713Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const tabStoreChange = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 31 25"
        fill="#6A938D"
      >
        <Path
          d="M30.2402 6.05937L27.0791 1.20312C26.7966 0.767187 26.2949 0.5 25.7591 0.5H6.08148C5.5457 0.5 5.04401 0.767187 4.76151 1.20312L1.60042 6.05937C-0.0312656 8.56719 1.41534 12.0547 4.4644 12.4531C4.68358 12.4812 4.90764 12.4953 5.13169 12.4953C6.57342 12.4953 7.84954 11.8859 8.72627 10.9438C9.603 11.8859 10.884 12.4953 12.3209 12.4953C13.7626 12.4953 15.0387 11.8859 15.9154 10.9438C16.7922 11.8859 18.0732 12.4953 19.51 12.4953C20.9518 12.4953 22.2279 11.8859 23.1046 10.9438C23.9862 11.8859 25.2623 12.4953 26.6992 12.4953C26.9281 12.4953 27.1473 12.4812 27.3665 12.4531C30.4253 12.0594 31.8768 8.57187 30.2402 6.05937ZM26.7089 14C26.2219 14 25.7397 13.9297 25.2721 13.8219V18.5H6.56855V13.8219C6.10096 13.925 5.61876 14 5.13169 14C4.83945 14 4.54233 13.9812 4.25496 13.9437C3.9822 13.9062 3.71431 13.8453 3.45616 13.775V23C3.45616 23.8297 4.15267 24.5 5.01479 24.5H26.8356C27.6977 24.5 28.3942 23.8297 28.3942 23V13.775C28.1312 13.85 27.8682 13.9109 27.5954 13.9437C27.2983 13.9812 27.0061 14 26.7089 14Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
//
export const tabPinGobal = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M9.14844 7.48828C8.58844 7.48828 8.14844 7.93828 8.14844 8.48828C8.14844 9.03828 8.59844 9.48828 9.14844 9.48828C9.69844 9.48828 10.1484 9.03828 10.1484 8.48828C10.1484 7.93828 9.69844 7.48828 9.14844 7.48828Z"
          fill="#ffffff"
        />
        <Path
          d="M21.46 5.04C20.62 3.09 18.77 2 16.19 2H7.81C4.6 2 2 4.6 2 7.81V16.19C2 18.77 3.09 20.62 5.04 21.46C5.23 21.54 5.45 21.49 5.59 21.35L21.35 5.59C21.5 5.44 21.55 5.22 21.46 5.04ZM10.53 12.24C10.14 12.62 9.63 12.8 9.12 12.8C8.61 12.8 8.1 12.61 7.71 12.24C6.69 11.28 5.57 9.75 6 7.93C6.38 6.28 7.84 5.54 9.12 5.54C10.4 5.54 11.86 6.28 12.24 7.94C12.66 9.75 11.54 11.28 10.53 12.24Z"
          fill="#ffffff"
        />
        <Path
          d="M19.4689 20.5295C19.6889 20.7495 19.6589 21.1095 19.3889 21.2595C18.5089 21.7495 17.4389 21.9995 16.1889 21.9995H7.80892C7.51892 21.9995 7.39892 21.6595 7.59892 21.4595L13.6389 15.4195C13.8389 15.2195 14.1489 15.2195 14.3489 15.4195L19.4689 20.5295Z"
          fill="#ffffff"
        />
        <Path
          d="M22.0017 7.80892V16.1889C22.0017 17.4389 21.7517 18.5189 21.2617 19.3889C21.1117 19.6589 20.7517 19.6789 20.5317 19.4689L15.4117 14.3489C15.2117 14.1489 15.2117 13.8389 15.4117 13.6389L21.4517 7.59892C21.6617 7.39892 22.0017 7.51892 22.0017 7.80892Z"
          fill="#ffffff"
        />
      </Svg>
    </View>
  );
};
export const tabPinGobalChange = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M9.14844 7.48828C8.58844 7.48828 8.14844 7.93828 8.14844 8.48828C8.14844 9.03828 8.59844 9.48828 9.14844 9.48828C9.69844 9.48828 10.1484 9.03828 10.1484 8.48828C10.1484 7.93828 9.69844 7.48828 9.14844 7.48828Z"
          fill="#6A938D"
        />
        <Path
          d="M21.46 5.04C20.62 3.09 18.77 2 16.19 2H7.81C4.6 2 2 4.6 2 7.81V16.19C2 18.77 3.09 20.62 5.04 21.46C5.23 21.54 5.45 21.49 5.59 21.35L21.35 5.59C21.5 5.44 21.55 5.22 21.46 5.04ZM10.53 12.24C10.14 12.62 9.63 12.8 9.12 12.8C8.61 12.8 8.1 12.61 7.71 12.24C6.69 11.28 5.57 9.75 6 7.93C6.38 6.28 7.84 5.54 9.12 5.54C10.4 5.54 11.86 6.28 12.24 7.94C12.66 9.75 11.54 11.28 10.53 12.24Z"
          fill="#6A938D"
        />
        <Path
          d="M19.4689 20.5295C19.6889 20.7495 19.6589 21.1095 19.3889 21.2595C18.5089 21.7495 17.4389 21.9995 16.1889 21.9995H7.80892C7.51892 21.9995 7.39892 21.6595 7.59892 21.4595L13.6389 15.4195C13.8389 15.2195 14.1489 15.2195 14.3489 15.4195L19.4689 20.5295Z"
          fill="#6A938D"
        />
        <Path
          d="M22.0017 7.80892V16.1889C22.0017 17.4389 21.7517 18.5189 21.2617 19.3889C21.1117 19.6589 20.7517 19.6789 20.5317 19.4689L15.4117 14.3489C15.2117 14.1489 15.2117 13.8389 15.4117 13.6389L21.4517 7.59892C21.6617 7.39892 22.0017 7.51892 22.0017 7.80892Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
