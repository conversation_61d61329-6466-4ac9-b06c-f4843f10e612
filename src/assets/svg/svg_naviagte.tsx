import React from "react";
import { View } from "react-native";
import Svg, { G, Path } from "react-native-svg";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";

export const goBack_Default = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        viewBox="0 0 298 511.93"
        fill="#84B8A2"
      >
        <Path
          fill-rule="#84B8A2"
          d="M285.77 441c16.24 16.17 16.32 42.46.15 58.7-16.16 16.24-42.45 16.32-58.69.16l-215-214.47c-16.24-16.16-16.32-42.45-.15-58.69L227.23 12.08c16.24-16.17 42.53-16.09 58.69.15 16.17 16.24 16.09 42.54-.15 58.7l-185.5 185.04L285.77 441z"
        />
      </Svg>
    </View>
  );
};
export const goBack_Black = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        viewBox="0 0 298 511.93"
        fill="#000000"
      >
        <Path
          fill-rule="#000000"
          d="M285.77 441c16.24 16.17 16.32 42.46.15 58.7-16.16 16.24-42.45 16.32-58.69.16l-215-214.47c-16.24-16.16-16.32-42.45-.15-58.69L227.23 12.08c16.24-16.17 42.53-16.09 58.69.15 16.17 16.24 16.09 42.54-.15 58.7l-185.5 185.04L285.77 441z"
        />
      </Svg>
    </View>
  );
};
export const goBack_White = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        viewBox="0 0 298 511.93"
        fill="#ffffff"
      >
        <Path
          fill-rule="#ffffff"
          d="M285.77 441c16.24 16.17 16.32 42.46.15 58.7-16.16 16.24-42.45 16.32-58.69.16l-215-214.47c-16.24-16.16-16.32-42.45-.15-58.69L227.23 12.08c16.24-16.17 42.53-16.09 58.69.15 16.17 16.24 16.09 42.54-.15 58.7l-185.5 185.04L285.77 441z"
        />
      </Svg>
    </View>
  );
};
export const goBack_Bg = () => {
  return (
    <View>
      <Svg
        fill="#fff"
        width={scale(34)}
        height={verticalScale(34)}
        viewBox="0 0 512 512"
      >
        <Path d="M256,48C141.13,48,48,141.13,48,256s93.13,208,208,208,208-93.13,208-208S370.87,48,256,48Zm35.31,292.69a16,16,0,1,1-22.62,22.62l-96-96a16,16,0,0,1,0-22.62l96-96a16,16,0,0,1,22.62,22.62L206.63,256Z" />
      </Svg>
    </View>
  );
};
export const goBack_BgGreen = () => {
  return (
    <View>
      <Svg
        fill="#84B8A2"
        width={scale(34)}
        height={verticalScale(34)}
        viewBox="0 0 512 512"
      >
        <Path d="M256,48C141.13,48,48,141.13,48,256s93.13,208,208,208,208-93.13,208-208S370.87,48,256,48Zm35.31,292.69a16,16,0,1,1-22.62,22.62l-96-96a16,16,0,0,1,0-22.62l96-96a16,16,0,0,1,22.62,22.62L206.63,256Z" />
      </Svg>
    </View>
  );
};
export const goBack_gay = () => {
  return (
    <View>
      <Svg
        fill="#D6D6D6"
        width={scale(34)}
        height={verticalScale(34)}
        viewBox="0 0 512 512"
      >
        <Path d="M256,48C141.13,48,48,141.13,48,256s93.13,208,208,208,208-93.13,208-208S370.87,48,256,48Zm35.31,292.69a16,16,0,1,1-22.62,22.62l-96-96a16,16,0,0,1,0-22.62l96-96a16,16,0,0,1,22.62,22.62L206.63,256Z" />
      </Svg>
    </View>
  );
};
export const goBack_Operation = () => {
  return (
    <View>
      <Svg
        fill="#D9D9D9"
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 512 512"
      >
        <Path d="M256,48C141.13,48,48,141.13,48,256s93.13,208,208,208,208-93.13,208-208S370.87,48,256,48Zm35.31,292.69a16,16,0,1,1-22.62,22.62l-96-96a16,16,0,0,1,0-22.62l96-96a16,16,0,0,1,22.62,22.62L206.63,256Z" />
      </Svg>
    </View>
  );
};
export const goBack_x = () => {
  return (
    <View>
      <Svg
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM15.36 14.3C15.65 14.59 15.65 15.07 15.36 15.36C15.21 15.51 15.02 15.58 14.83 15.58C14.64 15.58 14.45 15.51 14.3 15.36L12 13.06L9.7 15.36C9.55 15.51 9.36 15.58 9.17 15.58C8.98 15.58 8.79 15.51 8.64 15.36C8.35 15.07 8.35 14.59 8.64 14.3L10.94 12L8.64 9.7C8.35 9.41 8.35 8.93 8.64 8.64C8.93 8.35 9.41 8.35 9.7 8.64L12 10.94L14.3 8.64C14.59 8.35 15.07 8.35 15.36 8.64C15.65 8.93 15.65 9.41 15.36 9.7L13.06 12L15.36 14.3Z"
          fill="#D6D6D6"
        />
      </Svg>
    </View>
  );
};
