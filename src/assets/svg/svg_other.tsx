import React from "react";
import { View } from "react-native";
import Svg, {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Rect,
  Mask,
  Defs,
  Pattern,
  Use,
} from "react-native-svg";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";

export const iconNon = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 35 37"
        fill="none"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="#757575"
        />
      </Svg>
    </View>
  );
};
export const iconKey = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M22 8.29344C22 11.7692 19.1708 14.5869 15.6807 14.5869C15.0439 14.5869 13.5939 14.4405 12.8885 13.8551L12.0067 14.7333C11.4883 15.2496 11.6283 15.4016 11.8589 15.652C11.9551 15.7565 12.0672 15.8781 12.1537 16.0505C12.1537 16.0505 12.8885 17.075 12.1537 18.0995C11.7128 18.6849 10.4783 19.5045 9.06754 18.0995L8.77362 18.3922C8.77362 18.3922 9.65538 19.4167 8.92058 20.4412C8.4797 21.0267 7.30403 21.6121 6.27531 20.5876L5.2466 21.6121C4.54119 22.3146 3.67905 21.9048 3.33616 21.6121L2.45441 20.7339C1.63143 19.9143 2.1115 19.0264 2.45441 18.6849L10.0963 11.0743C10.0963 11.0743 9.3615 9.90338 9.3615 8.29344C9.3615 4.81767 12.1907 2 15.6807 2C19.1708 2 22 4.81767 22 8.29344ZM15.681 10.4889C16.8984 10.4889 17.8853 9.50601 17.8853 8.29353C17.8853 7.08105 16.8984 6.09814 15.681 6.09814C14.4635 6.09814 13.4766 7.08105 13.4766 8.29353C13.4766 9.50601 14.4635 10.4889 15.681 10.4889Z"
            fill="#444444"
          />
        </G>
      </Svg>
    </View>
  );
};
export const editSvg = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M19.6952 21H0.787808C0.35714 21 0 20.6429 0 20.2122C0 19.7815 0.35714 19.4244 0.787808 19.4244H19.6952C20.1259 19.4244 20.483 19.7815 20.483 20.2122C20.483 20.6429 20.1259 21 19.6952 21Z"
          fill="#757575"
        />
        <Path
          d="M17.6156 1.5481C15.5778 -0.489691 13.5821 -0.542212 11.4917 1.5481L10.2208 2.8191C10.1157 2.92414 10.0737 3.09221 10.1157 3.23927C10.914 6.02285 13.1409 8.24972 15.9245 9.04801C15.9665 9.05852 16.0085 9.06902 16.0505 9.06902C16.1661 9.06902 16.2711 9.02701 16.3551 8.94297L17.6156 7.672C18.6555 6.64259 19.1597 5.64471 19.1597 4.63631C19.1702 3.5964 18.666 2.58801 17.6156 1.5481Z"
          fill="#757575"
        />
        <Path
          d="M14.0336 10.003C13.729 9.85599 13.4348 9.70893 13.1512 9.54087C12.9201 9.40431 12.6996 9.25726 12.479 9.09969C12.3004 8.98415 12.0903 8.81608 11.8907 8.64802C11.8697 8.63751 11.7962 8.57449 11.7122 8.49046C11.3655 8.19632 10.9769 7.81817 10.6302 7.39801C10.5987 7.377 10.5462 7.30347 10.4727 7.20893C10.3676 7.08289 10.1891 6.8728 10.0315 6.63121C9.90547 6.47365 9.75841 6.24256 9.62186 6.01147C9.45379 5.72786 9.30673 5.44425 9.15967 5.15013C9.13741 5.10245 9.11587 5.05501 9.09507 5.00784C8.94003 4.65772 8.48342 4.55536 8.21273 4.82611L2.19548 10.8434C2.05892 10.9799 1.93287 11.2425 1.90136 11.4211L1.33414 15.4442C1.2291 16.1585 1.42868 16.8307 1.86985 17.2824C2.248 17.65 2.7732 17.8496 3.34042 17.8496C3.46647 17.8496 3.59252 17.8391 3.71857 17.8181L7.75215 17.2509C7.94122 17.2194 8.2038 17.0933 8.32985 16.9568L14.3396 10.947C14.6123 10.6744 14.5094 10.2065 14.1548 10.0551C14.1149 10.0381 14.0744 10.0207 14.0336 10.003Z"
          fill="#757575"
        />
      </Svg>
    </View>
  );
};
export const iconPin = () => {
  return (
    <View>
      <Svg
        width={scale(14)}
        height={verticalScale(14)}
        viewBox="0 0 12 14"
        fill="none"
      >
        <Path
          d="M11.831 4.57561C11.1211 1.42049 8.39663 0 6.00339 0C6.00339 0 6.00339 0 5.99663 0C3.61013 0 0.878859 1.41366 0.168999 4.56878C-0.621987 8.09268 1.51435 11.0771 3.44788 12.9551C4.1645 13.6517 5.08395 14 6.00339 14C6.92283 14 7.84227 13.6517 8.55212 12.9551C10.4856 11.0771 12.622 8.09951 11.831 4.57561ZM6.00339 7.99707C4.82705 7.99707 3.87379 7.03415 3.87379 5.84585C3.87379 4.65756 4.82705 3.69463 6.00339 3.69463C7.17973 3.69463 8.13297 4.65756 8.13297 5.84585C8.13297 7.03415 7.17973 7.99707 6.00339 7.99707Z"
          fill="#B3DBC0"
        />
      </Svg>
    </View>
  );
};
export const iconRightChat = () => {
  return (
    <View>
      <Svg
        width={scale(14)}
        height={verticalScale(14)}
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        viewBox="0 0 298 511.93"
        fill="#346359"
      >
        <Path
          fill-rule="#346359"
          d="M70.77 499.85c-16.24 16.17-42.53 16.09-58.69-.15-16.17-16.25-16.09-42.54.15-58.7l185.5-185.03L12.23 70.93c-16.24-16.16-16.32-42.45-.15-58.7 16.16-16.24 42.45-16.32 58.69-.15l215.15 214.61c16.17 16.25 16.09 42.54-.15 58.7l-215 214.46z"
        />
      </Svg>
    </View>
  );
};
export const iconCamera = () => {
  return (
    <View>
      <Svg width={scale(16)} height={verticalScale(16)} viewBox="0 -2 32 32">
        <G
          id="Page-1"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <G
            id="Icon-Set-Filled"
            transform="translate(-258.000000, -467.000000)"
            fill="#346359"
          >
            <Path
              d="M286,471 L283,471 L282,469 C281.411,467.837 281.104,467 280,467 L268,467 C266.896,467 266.53,467.954 266,469 L265,471 L262,471 C259.791,471 258,472.791 258,475 L258,491 C258,493.209 259.791,495 262,495 L286,495 C288.209,495 290,493.209 290,491 L290,475 C290,472.791 288.209,471 286,471 Z M274,491 C269.582,491 266,487.418 266,483 C266,478.582 269.582,475 274,475 C278.418,475 282,478.582 282,483 C282,487.418 278.418,491 274,491 Z M274,477 C270.687,477 268,479.687 268,483 C268,486.313 270.687,489 274,489 C277.313,489 280,486.313 280,483 C280,479.687 277.313,477 274,477 L274,477 Z"
              id="camera"
            ></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconCameraEdit = () => {
  return (
    <View>
      <Svg width={scale(16)} height={verticalScale(16)} viewBox="0 -2 32 32">
        <G
          id="Page-1"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <G
            id="Icon-Set-Filled"
            transform="translate(-258.000000, -467.000000)"
            fill="#346359"
          >
            <Path
              d="M286,471 L283,471 L282,469 C281.411,467.837 281.104,467 280,467 L268,467 C266.896,467 266.53,467.954 266,469 L265,471 L262,471 C259.791,471 258,472.791 258,475 L258,491 C258,493.209 259.791,495 262,495 L286,495 C288.209,495 290,493.209 290,491 L290,475 C290,472.791 288.209,471 286,471 Z M274,491 C269.582,491 266,487.418 266,483 C266,478.582 269.582,475 274,475 C278.418,475 282,478.582 282,483 C282,487.418 278.418,491 274,491 Z M274,477 C270.687,477 268,479.687 268,483 C268,486.313 270.687,489 274,489 C277.313,489 280,486.313 280,483 C280,479.687 277.313,477 274,477 L274,477 Z"
              id="camera"
            ></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconPinArea = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M12.398 17.804C13.881 17.0348 19 14.0163 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 14.0163 10.119 17.0348 11.602 17.804C11.8548 17.9351 12.1452 17.9351 12.398 17.804ZM12 12C13.6569 12 15 10.6569 15 9C15 7.34315 13.6569 6 12 6C10.3431 6 9 7.34315 9 9C9 10.6569 10.3431 12 12 12Z"
          fill="#ffffff"
        />
        <Path
          d="M18.0622 16.5C18.6766 16.9561 19 17.4734 19 18C19 18.5266 18.6766 19.0439 18.0622 19.5C17.4478 19.9561 16.5641 20.3348 15.5 20.5981C14.4359 20.8614 13.2288 21 12 21C10.7712 21 9.56414 20.8614 8.5 20.5981C7.43587 20.3348 6.5522 19.9561 5.93782 19.5C5.32344 19.0439 5 18.5266 5 18C5 17.4734 5.32344 16.9561 5.93782 16.5"
          stroke="#ffffff"
          stroke-width="2"
          stroke-linecap="round"
        />
      </Svg>
    </View>
  );
};
export const iconPinAreaNon = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M12.398 17.804C13.881 17.0348 19 14.0163 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 14.0163 10.119 17.0348 11.602 17.804C11.8548 17.9351 12.1452 17.9351 12.398 17.804ZM12 12C13.6569 12 15 10.6569 15 9C15 7.34315 13.6569 6 12 6C10.3431 6 9 7.34315 9 9C9 10.6569 10.3431 12 12 12Z"
          fill="#B3DBC0"
        />
        <Path
          d="M18.0622 16.5C18.6766 16.9561 19 17.4734 19 18C19 18.5266 18.6766 19.0439 18.0622 19.5C17.4478 19.9561 16.5641 20.3348 15.5 20.5981C14.4359 20.8614 13.2288 21 12 21C10.7712 21 9.56414 20.8614 8.5 20.5981C7.43587 20.3348 6.5522 19.9561 5.93782 19.5C5.32344 19.0439 5 18.5266 5 18C5 17.4734 5.32344 16.9561 5.93782 16.5"
          stroke="#B3DBC0"
          stroke-width="2"
          stroke-linecap="round"
        />
      </Svg>
    </View>
  );
};
export const iconBell = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.7942 3.29494C13.2296 3.10345 12.6258 3 12 3C9.15347 3 6.76217 5.14032 6.44782 7.96942L6.19602 10.2356L6.18957 10.2933C6.06062 11.417 5.69486 12.5005 5.11643 13.4725L5.08664 13.5222L4.5086 14.4856C3.98405 15.3599 3.72177 15.797 3.77839 16.1559C3.81607 16.3946 3.93896 16.6117 4.12432 16.7668C4.40289 17 4.91267 17 5.93221 17H18.0678C19.0873 17 19.5971 17 19.8756 16.7668C20.061 16.6117 20.1839 16.3946 20.2216 16.1559C20.2782 15.797 20.0159 15.3599 19.4914 14.4856L18.9133 13.5222L18.8835 13.4725C18.4273 12.7059 18.1034 11.8698 17.9236 10.9994C15.1974 10.9586 13 8.73592 13 6C13 5.00331 13.2916 4.07473 13.7942 3.29494ZM16.2741 4.98883C16.0999 5.28551 16 5.63109 16 6C16 6.94979 16.662 7.74494 17.5498 7.94914C17.4204 6.82135 16.9608 5.80382 16.2741 4.98883Z"
          fill="#444444"
        />
        <Path
          d="M9 17C9 17.394 9.0776 17.7841 9.22836 18.1481C9.37913 18.512 9.6001 18.8427 9.87868 19.1213C10.1573 19.3999 10.488 19.6209 10.8519 19.7716C11.2159 19.9224 11.606 20 12 20C12.394 20 12.7841 19.9224 13.1481 19.7716C13.512 19.6209 13.8427 19.3999 14.1213 19.1213C14.3999 18.8427 14.6209 18.512 14.7716 18.1481C14.9224 17.7841 15 17.394 15 17L12 17H9Z"
          fill="#444444"
        />
        <Circle cx="18" cy="6" r="2.5" fill="#444444" stroke="#444444" />
      </Svg>
    </View>
  );
};
export const iconScope = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 32 32"
        enable-background="new 0 0 32 32"
      >
        <G>
          <Path
            fill="#828282"
            d="M17,6.5C17,6.224,16.776,6,16.5,6S16,6.224,16,6.5V15H6.5C6.224,15,6,15.224,6,15.5S6.224,16,6.5,16H16
		v9.469c0,0.276,0.224,0.5,0.5,0.5s0.5-0.224,0.5-0.5V16h9.469c0.276,0,0.5-0.224,0.5-0.5s-0.224-0.5-0.5-0.5H17V6.5z"
          />
          <Path
            fill="#828282"
            d="M31.562,15h-2.613C28.461,8.63,23.37,3.539,17,3.051V0.5C17,0.224,16.776,0,16.5,0S16,0.224,16,0.5V3
		C9.17,3,3.565,8.299,3.051,15H0.562c-0.276,0-0.5,0.224-0.5,0.5s0.224,0.5,0.5,0.5H3c0,7.168,5.832,13,13,13v2.5
		c0,0.276,0.224,0.5,0.5,0.5s0.5-0.224,0.5-0.5v-2.551C23.701,28.435,29,22.83,29,16h2.562c0.276,0,0.5-0.224,0.5-0.5
		S31.839,15,31.562,15z M16,28C9.383,28,4,22.617,4,16S9.383,4,16,4s12,5.383,12,12S22.617,28,16,28z"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconPlus = () => {
  return (
    <View>
      <Svg
        width={moderateScale(14)}
        height={moderateScale(14)}
        viewBox="0 0 14 14"
        fill="none"
      >
        <Path
          d="M7 0C6.69058 0 6.39383 0.122916 6.17504 0.341709C5.95625 0.560501 5.83333 0.857247 5.83333 1.16667V5.83333H1.16667C0.857247 5.83333 0.560501 5.95625 0.341709 6.17504C0.122916 6.39384 0 6.69058 0 7C0 7.30942 0.122916 7.60617 0.341709 7.82496C0.560501 8.04375 0.857247 8.16667 1.16667 8.16667H5.83333V12.8333C5.83333 13.1428 5.95625 13.4395 6.17504 13.6583C6.39383 13.8771 6.69058 14 7 14C7.30942 14 7.60616 13.8771 7.82496 13.6583C8.04375 13.4395 8.16667 13.1428 8.16667 12.8333V8.16667H12.8333C13.1428 8.16667 13.4395 8.04375 13.6583 7.82496C13.8771 7.60617 14 7.30942 14 7C14 6.69058 13.8771 6.39384 13.6583 6.17504C13.4395 5.95625 13.1428 5.83333 12.8333 5.83333H8.16667V1.16667C8.16667 0.857247 8.04375 0.560501 7.82496 0.341709C7.60616 0.122916 7.30942 0 7 0Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const iconFile = () => {
  return (
    <View>
      <Svg
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 392 512.309"
      >
        <Path
          d="M58.883 0h186.242v93.425l.099 1.406c1.618 22.2 9.835 40.061 24.454 52.552 14.088 12.047 33.864 18.745 59.129 19.14l63.193.036v286.86c0 32.331-26.552 58.89-58.883 58.89H58.883C26.545 512.309 0 485.814 0 453.419V58.883C0 26.495 26.495 0 58.883 0zm48.892 412.172c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h141.71c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-141.71zm0-175.532c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h176.45c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-176.45zm0 84.77c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h167.102c5.674 0 10.281 4.606 10.281 10.28 0 5.673-4.607 10.28-10.281 10.28H107.775zM265.685 3.159L392 139.816v6.197l-63.03.035c-20.179-.346-35.532-5.327-45.967-14.251-10.288-8.796-16.109-21.853-17.318-38.414V3.159z"
          fill="#444444"
        />
      </Svg>
    </View>
  );
};
export const iconInfo = () => {
  return (
    <View>
      <Svg
        fill="#444444"
        clip-rule="evenodd"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 1920 1920"
      >
        <Path
          d="M960 0c530.193 0 960 429.807 960 960s-429.807 960-960 960S0 1490.193 0 960 429.807 0 960 0Zm223.797 707.147c-28.531-29.561-67.826-39.944-109.227-39.455-55.225.657-114.197 20.664-156.38 40.315-100.942 47.024-178.395 130.295-242.903 219.312-11.616 16.025-17.678 34.946 2.76 49.697 17.428 12.58 29.978 1.324 40.49-9.897l.69-.74c.801-.862 1.591-1.72 2.37-2.565 11.795-12.772 23.194-25.999 34.593-39.237l2.85-3.31 2.851-3.308c34.231-39.687 69.056-78.805 115.144-105.345 27.4-15.778 47.142 8.591 42.912 35.963-2.535 16.413-11.165 31.874-17.2 47.744-21.44 56.363-43.197 112.607-64.862 168.888-23.74 61.7-47.405 123.425-70.426 185.398l-2 5.38-1.998 5.375c-20.31 54.64-40.319 108.872-53.554 165.896-10.575 45.592-24.811 100.906-4.357 145.697 11.781 25.8 36.77 43.532 64.567 47.566 37.912 5.504 78.906 6.133 116.003-2.308 19.216-4.368 38.12-10.07 56.57-17.005 56.646-21.298 108.226-54.146 154.681-92.755 47.26-39.384 88.919-85.972 126.906-134.292 12.21-15.53 27.004-32.703 31.163-52.596 3.908-18.657-12.746-45.302-34.326-34.473-11.395 5.718-19.929 19.867-28.231 29.27-10.42 11.798-21.044 23.423-31.786 34.92-21.488 22.987-43.513 45.463-65.634 67.831-13.54 13.692-30.37 25.263-47.662 33.763-21.59 10.609-38.785-1.157-36.448-25.064 2.144-21.954 7.515-44.145 15.046-64.926 30.306-83.675 61.19-167.135 91.834-250.686 19.157-52.214 38.217-104.461 56.999-156.816 17.554-48.928 32.514-97.463 38.834-149.3 4.357-35.71-4.9-72.647-30.269-98.937Zm63.72-401.498c-91.342-35.538-200.232 25.112-218.574 121.757-13.25 69.784 13.336 131.23 67.998 157.155 105.765 50.16 232.284-29.954 232.29-147.084.005-64.997-28.612-111.165-81.715-131.828Z"
          fill-rule="evenodd"
        />
      </Svg>
    </View>
  );
};
export const iconUser = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 16 18"
        fill="none"
      >
        <Path
          d="M8 9C10.525 9 12.5714 6.98555 12.5714 4.5C12.5714 2.01445 10.525 0 8 0C5.475 0 3.42857 2.01445 3.42857 4.5C3.42857 6.98555 5.475 9 8 9ZM11.2 10.125H10.6036C9.81071 10.4836 8.92857 10.6875 8 10.6875C7.07143 10.6875 6.19286 10.4836 5.39643 10.125H4.8C2.15 10.125 0 12.2414 0 14.85V16.3125C0 17.2441 0.767857 18 1.71429 18H14.2857C15.2321 18 16 17.2441 16 16.3125V14.85C16 12.2414 13.85 10.125 11.2 10.125Z"
          fill="#2B2B2B"
        />
      </Svg>
    </View>
  );
};
export const iconPlay = () => {
  return (
    <View>
      <Svg
        id="Filled"
        viewBox="0 0 24 24"
        width={scale(25)}
        height={verticalScale(25)}
      >
        <Path
          d="M20.492,7.969,10.954.975A5,5,0,0,0,3,5.005V19a4.994,4.994,0,0,0,7.954,4.03l9.538-6.994a5,5,0,0,0,0-8.062Z"
          fill="#fff"
        />
      </Svg>
    </View>
  );
};
export const iconSave = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(21)}
        height={verticalScale(21)}
        viewBox="0 0 122.877 101.052"
        enable-background="new 0 0 122.877 101.052"
      >
        <G>
          <Path
            d="M4.43,63.63c-2.869-2.755-4.352-6.42-4.427-10.11c-0.074-3.689,1.261-7.412,4.015-10.281 c2.752-2.867,6.417-4.351,10.106-4.425c3.691-0.076,7.412,1.255,10.283,4.012l24.787,23.851L98.543,3.989l1.768,1.349l-1.77-1.355 c0.141-0.183,0.301-0.339,0.479-0.466c2.936-2.543,6.621-3.691,10.223-3.495V0.018l0.176,0.016c3.623,0.24,7.162,1.85,9.775,4.766 c2.658,2.965,3.863,6.731,3.662,10.412h0.004l-0.016,0.176c-0.236,3.558-1.791,7.035-4.609,9.632l-59.224,72.09l0.004,0.004 c-0.111,0.141-0.236,0.262-0.372,0.368c-2.773,2.435-6.275,3.629-9.757,3.569c-3.511-0.061-7.015-1.396-9.741-4.016L4.43,63.63 L4.43,63.63z"
            fill={"#757575"}
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconSaveProcess = () => {
  return (
    <View>
      <Svg
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M3 9C3 6.17157 3 4.75736 3.87868 3.87868C4.75736 3 6.17157 3 9 3H15.3431C16.1606 3 16.5694 3 16.9369 3.15224C17.3045 3.30448 17.5935 3.59351 18.1716 4.17157L19.8284 5.82843C20.4065 6.40649 20.6955 6.69552 20.8478 7.06306C21 7.4306 21 7.83935 21 8.65685V15C21 17.8284 21 19.2426 20.1213 20.1213C19.48 20.7626 18.5534 20.9359 17 20.9827V18L17 17.9384C17.0001 17.2843 17.0001 16.6965 16.9362 16.2208C16.8663 15.7015 16.7042 15.1687 16.2678 14.7322C15.8313 14.2958 15.2985 14.1337 14.7792 14.0638C14.3034 13.9999 13.7157 13.9999 13.0616 14L13 14H10L9.93839 14C9.28427 13.9999 8.69655 13.9999 8.22084 14.0638C7.70149 14.1337 7.16867 14.2958 6.73223 14.7322C6.29579 15.1687 6.13366 15.7015 6.06383 16.2208C5.99988 16.6965 5.99993 17.2843 6 17.9384L6 18V20.9239C5.02491 20.828 4.36857 20.6112 3.87868 20.1213C3 19.2426 3 17.8284 3 15V9ZM15 18V21H9C8.64496 21 8.31221 21 8 20.9983V18C8 17.2646 8.00212 16.8137 8.046 16.4873C8.08457 16.2005 8.13942 16.1526 8.14592 16.1469L8.14645 16.1464L8.14692 16.1459C8.1526 16.1394 8.20049 16.0846 8.48734 16.046C8.81369 16.0021 9.26462 16 10 16H13C13.7354 16 14.1863 16.0021 14.5127 16.046C14.7995 16.0846 14.8474 16.1394 14.8531 16.1459L14.8536 16.1464L14.8541 16.1469C14.8606 16.1526 14.9154 16.2005 14.954 16.4873C14.9979 16.8137 15 17.2646 15 18ZM7 7C6.44772 7 6 7.44772 6 8C6 8.55228 6.44772 9 7 9H12C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7H7Z"
          fill="#ffff"
        />
      </Svg>
    </View>
  );
};
export const iconSend = () => {
  return (
    <View>
      <Svg
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M16.1391 2.95907L7.10914 5.95907C1.03914 7.98907 1.03914 11.2991 7.10914 13.3191L9.78914 14.2091L10.6791 16.8891C12.6991 22.9591 16.0191 22.9591 18.0391 16.8891L21.0491 7.86907C22.3891 3.81907 20.1891 1.60907 16.1391 2.95907ZM16.4591 8.33907L12.6591 12.1591C12.5091 12.3091 12.3191 12.3791 12.1291 12.3791C11.9391 12.3791 11.7491 12.3091 11.5991 12.1591C11.3091 11.8691 11.3091 11.3891 11.5991 11.0991L15.3991 7.27907C15.6891 6.98907 16.1691 6.98907 16.4591 7.27907C16.7491 7.56907 16.7491 8.04907 16.4591 8.33907Z"
          fill="#84B8A2"
        />
      </Svg>
    </View>
  );
};
export const iconNext = () => {
  return (
    <View>
      <Svg
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 43 43"
        fill="none"
      >
        <Path
          d="M21.7271 1.84375C32.4595 1.84375 41.167 10.5512 41.167 21.2836C41.167 32.0161 32.4595 40.7235 21.7271 40.7235C10.9947 40.7235 2.28721 32.0161 2.28721 21.2836C2.28721 10.5512 10.9947 1.84375 21.7271 1.84375Z"
          stroke="#A5A5A5"
          stroke-opacity="0.85"
          stroke-width="2.9288"
          stroke-miterlimit="10"
        />
        <Path
          d="M17.6775 31.0036L27.3975 21.2836L17.6775 11.5637"
          stroke="#A5A5A5"
          stroke-opacity="0.85"
          stroke-width="2.9288"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </Svg>
    </View>
  );
};
export const iconLike = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 25 22"
        fill="none"
      >
        <Path
          d="M13.213 20.8989C12.822 21.0337 12.178 21.0337 11.787 20.8989C8.452 19.7865 1 15.1461 1 7.28089C1 3.80898 3.8635 1 7.394 1C9.487 1 11.3385 1.98876 12.5 3.51685C13.6615 1.98876 15.5245 1 17.606 1C21.1365 1 24 3.80898 24 7.28089C24 15.1461 16.548 19.7865 13.213 20.8989Z"
          stroke="#343434"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </Svg>
    </View>
  );
};
export const iconPlans = () => {
  return (
    <View>
      <Svg
        height={verticalScale(15)}
        width={scale(15)}
        id="_x32_"
        viewBox="0 0 512 512"
      >
        <G>
          <Path
            // class="st0"
            d="M106.003,348.768c12.848-115.627,86.282-211.197,185.781-257.238C268.378,53.37,236.62,22.365,199.921,0
      C62.447,65.536-2.416,227.413,54.819,370.914c14.055,35.265,34.373,66.122,58.994,92.125
      C104.455,425.92,101.726,387.261,106.003,348.768z"
            fill="#B3DBC0"
          />
          <Path
            // class="st0"
            d="M439.482,93.253C287.34,86.361,153.924,198.646,136.859,352.197c-6.209,55.924,3.883,109.888,26.406,157.215
      c2.134,0.289,4.225,0.63,6.35,0.866c152.43,16.933,289.722-92.903,306.654-245.325C483.17,202.836,469.01,143.23,439.482,93.253z
       M290.952,345.279c-48.56,75.218-93.716,132.454-100.854,127.853c-7.119-4.609,26.484-69.305,75.078-144.515
      c48.577-75.218,93.734-132.455,100.862-127.854C373.177,205.373,339.556,270.069,290.952,345.279z"
            fill="#B3DBC0"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconPhone = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 20 20"
        fill="none"
      >
        <Path
          d="M14.9502 19.1667C11.2077 19.1618 7.61987 17.673 4.97353 15.0267C2.32719 12.3803 0.838347 8.79251 0.833496 5.05002C0.833496 3.93169 1.27775 2.85917 2.06853 2.06839C2.85931 1.27761 3.93183 0.833357 5.05016 0.833357C5.28698 0.831553 5.52339 0.853045 5.756 0.897523C5.98086 0.930799 6.20192 0.986064 6.416 1.06252C6.56656 1.11535 6.70072 1.20656 6.80522 1.32714C6.90972 1.44772 6.98094 1.59348 7.01183 1.75002L8.26766 7.25002C8.30151 7.39931 8.29744 7.55471 8.25581 7.70202C8.21417 7.84933 8.13631 7.98387 8.02933 8.09336C7.91016 8.22169 7.901 8.23086 6.7735 8.81752C7.67641 10.7983 9.2606 12.389 11.2377 13.3C11.8335 12.1634 11.8427 12.1542 11.971 12.035C12.0805 11.928 12.215 11.8502 12.3623 11.8085C12.5096 11.7669 12.665 11.7628 12.8143 11.7967L18.3143 13.0525C18.4659 13.0877 18.6059 13.1608 18.7214 13.2651C18.8369 13.3693 18.9239 13.5012 18.9743 13.6484C19.0517 13.8659 19.11 14.0898 19.1485 14.3175C19.1854 14.5479 19.2038 14.7809 19.2035 15.0142C19.1866 16.1277 18.7298 17.1894 17.933 17.9674C17.1361 18.7454 16.0638 19.1765 14.9502 19.1667Z"
          fill="#2B2B2B"
        />
      </Svg>
    </View>
  );
};
export const iconQrPay = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M21 7.505a1.5 1.5 0 0 1-1.5 1.5h-3a1.5 1.5 0 0 1-1.5-1.5V4.5A1.5 1.5 0 0 1 16.5 3h3A1.5 1.5 0 0 1 21 4.5v3.005zM9 7.5V4.504a1.5 1.5 0 0 0-1.498-1.5l-3-.003A1.5 1.5 0 0 0 3 4.5V7.5A1.5 1.5 0 0 0 4.5 9h3A1.5 1.5 0 0 0 9 7.5zM5 7V5.001l2 .002V7H5zm-.502 8.004 3-.003A1.5 1.5 0 0 1 9 16.5V19.5A1.5 1.5 0 0 1 7.5 21h-3A1.5 1.5 0 0 1 3 19.5v-2.996a1.5 1.5 0 0 1 1.498-1.5zM7 19H5v-1.997l2-.002V19zM19 5v2.005h-2V5h2zm0 11a1 1 0 1 1 2 0v3.5a1.5 1.5 0 0 1-1.498 1.5l-3.5.005a1 1 0 1 1-.003-2L19 19V16zM11 4v7H4a1 1 0 1 0 0 2h8a1 1 0 0 0 1-1V4a1 1 0 1 0-2 0zm2 16a1 1 0 1 1-2 0v-4a1 1 0 1 1 2 0v4zm3-3a1 1 0 0 0 1-1v-3h3a1 1 0 1 0 0-2h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1z"
          fill="#000000"
        />
      </Svg>
    </View>
  );
};
export const iconRight = () => {
  return (
    <View>
      <Svg
        width={scale(10)}
        height={verticalScale(10)}
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        viewBox="0 0 298 511.93"
        fill="#949494"
      >
        <Path
          fill-rule="#949494"
          d="M70.77 499.85c-16.24 16.17-42.53 16.09-58.69-.15-16.17-16.25-16.09-42.54.15-58.7l185.5-185.03L12.23 70.93c-16.24-16.16-16.32-42.45-.15-58.7 16.16-16.24 42.45-16.32 58.69-.15l215.15 214.61c16.17 16.25 16.09 42.54-.15 58.7l-215 214.46z"
        />
      </Svg>
    </View>
  );
};
export const iconEmail = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 21 16"
        fill="none"
      >
        <Path
          d="M10.2705 9.07254C10.6605 9.07254 11.0134 8.8961 11.4127 8.48753L19.4915 0.49217C19.1481 0.15785 18.5073 0 17.588 0H2.63728C1.85723 0 1.30935 0.148578 1.00292 0.455006L9.12828 8.48753C9.52758 8.88683 9.88972 9.07254 10.2705 9.07254ZM0.232178 14.5049L6.79747 8.01395L0.213596 1.52291C0.074289 1.78294 0 2.22867 0 2.86942V13.1213C0 13.7806 0.0835603 14.2449 0.232178 14.5049ZM20.2903 14.4956C20.4295 14.2263 20.5039 13.7713 20.5039 13.1213V2.86942C20.5039 2.24725 20.4388 1.80152 20.2994 1.5508L13.7435 8.01395L20.2903 14.4956ZM2.91586 16H17.8664C18.6466 16 19.1852 15.8514 19.4915 15.5543L12.8056 8.92396L12.1834 9.54612C11.552 10.159 10.9483 10.4376 10.2705 10.4376C9.59256 10.4376 8.98898 10.159 8.35754 9.54612L7.73537 8.92396L1.05863 15.545C1.41149 15.8514 2.03366 16 2.91586 16Z"
          fill="#2B2B2B"
        />
      </Svg>
    </View>
  );
};
export const iconClose = () => {
  return (
    <View>
      <Svg
        width={scale(35)}
        height={verticalScale(35)}
        viewBox="0 0 35 37"
        fill="none"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const iconCloseContorl = () => {
  return (
    <View>
      <Svg
        width={scale(35)}
        height={verticalScale(35)}
        viewBox="0 0 35 37"
        fill="#679290"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="#679290"
        />
      </Svg>
    </View>
  );
};
export const iconPause = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        data-name="Layer 1"
        viewBox="0 0 24 24"
        width={scale(24)}
        height={verticalScale(24)}
      >
        <Path
          d="M22,21c-.553,0-1-.448-1-1V4c0-.552,.447-1,1-1s1,.448,1,1V20c0,.552-.447,1-1,1Zm-4,0c-.553,0-1-.448-1-1V4c0-.552,.447-1,1-1s1,.448,1,1V20c0,.552-.447,1-1,1Zm-13.673-.271c-.509,0-1.023-.122-1.509-.367-1.139-.578-1.818-1.683-1.818-2.958V6.597c0-1.275,.679-2.381,1.817-2.958,1.119-.567,2.452-.457,3.46,.285l7.368,5.402c.86,.631,1.354,1.606,1.354,2.674s-.494,2.043-1.355,2.674l-7.368,5.403c-.588,.432-1.265,.651-1.949,.651Z"
          fill="#fff"
        />
      </Svg>
    </View>
  );
};
export const iconVideo = () => {
  return (
    <View>
      <Svg
        width={scale(14)}
        height={verticalScale(14)}
        viewBox="0 0 18 14"
        fill="none"
      >
        <Path
          d="M13.5 0H4.5C1.8 0 0 1.75 0 4.375V9.625C0 12.25 1.8 14 4.5 14H13.5C16.2 14 18 12.25 18 9.625V4.375C18 1.75 16.2 0 13.5 0ZM10.701 7.90125L8.478 9.19625C7.578 9.72125 6.83998 9.31875 6.83998 8.295V5.69625C6.83998 4.67251 7.578 4.27001 8.478 4.79501L10.701 6.09C11.556 6.5975 11.556 7.4025 10.701 7.90125Z"
          fill="#346359"
        />
      </Svg>
    </View>
  );
};
export const iconVideEdit = () => {
  return (
    <View>
      <Svg
        width={scale(19)}
        height={verticalScale(19)}
        viewBox="0 0 18 14"
        fill="none"
      >
        <Path
          d="M13.5 0H4.5C1.8 0 0 1.75 0 4.375V9.625C0 12.25 1.8 14 4.5 14H13.5C16.2 14 18 12.25 18 9.625V4.375C18 1.75 16.2 0 13.5 0ZM10.701 7.90125L8.478 9.19625C7.578 9.72125 6.83998 9.31875 6.83998 8.295V5.69625C6.83998 4.67251 7.578 4.27001 8.478 4.79501L10.701 6.09C11.556 6.5975 11.556 7.4025 10.701 7.90125Z"
          fill="#346359"
        />
      </Svg>
    </View>
  );
};
export const iconError = () => {
  return (
    <View>
      <Svg
        width={scale(50)}
        height={verticalScale(50)}
        viewBox="0 0 35 37"
        fill="none"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const iconCheck = () => {
  return (
    <View>
      <Svg
        fill="#ffff"
        width={scale(43)}
        height={verticalScale(43)}
        viewBox="0 0 24 24"
      >
        <Path d="M17.28 9.28a.75.75 0 00-1.06-1.06l-5.97 5.97-2.47-2.47a.75.75 0 00-1.06 1.06l3 3a.75.75 0 001.06 0l6.5-6.5z" />
        <Path
          fill-rule="evenodd"
          d="M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zM2.5 12a9.5 9.5 0 1119 0 9.5 9.5 0 01-19 0z"
        />
      </Svg>
    </View>
  );
};
export const iconLogout = () => {
  return (
    <Svg
      width={scale(32)}
      height={verticalScale(32)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <G id="SVGRepo_bgCarrier" stroke-width="0" />

      <G
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      />

      <G id="SVGRepo_iconCarrier">
        <Path
          d="M17.2929 14.2929C16.9024 14.6834 16.9024 15.3166 17.2929 15.7071C17.6834 16.0976 18.3166 16.0976 18.7071 15.7071L21.6201 12.7941C21.6351 12.7791 21.6497 12.7637 21.6637 12.748C21.87 12.5648 22 12.2976 22 12C22 11.7024 21.87 11.4352 21.6637 11.252C21.6497 11.2363 21.6351 11.2209 21.6201 11.2059L18.7071 8.29289C18.3166 7.90237 17.6834 7.90237 17.2929 8.29289C16.9024 8.68342 16.9024 9.31658 17.2929 9.70711L18.5858 11H13C12.4477 11 12 11.4477 12 12C12 12.5523 12.4477 13 13 13H18.5858L17.2929 14.2929Z"
          fill="#ffffff"
        />
        <Path
          d="M5 2C3.34315 2 2 3.34315 2 5V19C2 20.6569 3.34315 22 5 22H14.5C15.8807 22 17 20.8807 17 19.5V16.7326C16.8519 16.647 16.7125 16.5409 16.5858 16.4142C15.9314 15.7598 15.8253 14.7649 16.2674 14H13C11.8954 14 11 13.1046 11 12C11 10.8954 11.8954 10 13 10H16.2674C15.8253 9.23514 15.9314 8.24015 16.5858 7.58579C16.7125 7.4591 16.8519 7.35296 17 7.26738V4.5C17 3.11929 15.8807 2 14.5 2H5Z"
          fill="#ffffff"
        />
      </G>
    </Svg>
  );
};
export const iconLikeBg = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 23 20"
        fill="none"
      >
        <Path
          d="M16.606 0C14.5245 0 12.6615 0.988762 11.5 2.50561C10.3385 0.988762 8.4755 0 6.394 0C2.8635 0 0 2.80898 0 6.28088C0 7.61796 0.2185 8.85396 0.598 10C2.415 15.618 8.0155 18.9775 10.787 19.8989C11.178 20.0337 11.822 20.0337 12.213 19.8989C14.9845 18.9775 20.585 15.618 22.402 10C22.7815 8.85396 23 7.61796 23 6.28088C23 2.80898 20.1365 0 16.606 0Z"
          fill="#FF3737"
        />
      </Svg>
    </View>
  );
};
export const iconSilent = () => {
  return (
    <View>
      <Svg
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 493 511.769"
      >
        <Path
          fill-rule="nonzero"
          fill="#fff"
          d="M65.905 115.589h131.411L306.008 6.074c8.056-8.055 21.091-8.12 29.141-.065 4.099 3.969 6.11 9.257 6.11 14.609h.065v67.441L47.837 385.579a28.41 28.41 0 00-6.45 10.175 66.152 66.152 0 01-22.005-14.57C7.423 369.284 0 352.794 0 334.666V181.489c0-18.128 7.423-34.618 19.323-46.513 12.386-12.391 29.043-19.387 46.582-19.387zM450.34 59.237c9.716-9.775 25.525-9.819 35.3-.103 9.774 9.715 9.818 25.524.102 35.299L341.324 240.311v233.01c0 11.392-9.251 20.707-20.713 20.707-5.79 0-11.013-2.39-14.792-6.234l-107.433-87.287h-15.657L79.865 504.409c-9.716 9.775-25.524 9.818-35.299.103-9.775-9.716-9.818-25.525-.103-35.3L327.121 183.7l14.203-14.398v.054L450.34 59.237z"
        />
      </Svg>
    </View>
  );
};
export const iconCancel = () => {
  return (
    <View>
      <Svg
        fill="#ffff"
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 24 24"
      >
        <Path
          id="cancel"
          // class="cls-1"
          d="M936,120a12,12,0,1,1,12-12A12,12,0,0,1,936,120Zm0-22a10,10,0,1,0,10,10A10,10,0,0,0,936,98Zm4.706,14.706a0.951,0.951,0,0,1-1.345,0l-3.376-3.376-3.376,3.376a0.949,0.949,0,1,1-1.341-1.342l3.376-3.376-3.376-3.376a0.949,0.949,0,1,1,1.341-1.342l3.376,3.376,3.376-3.376a0.949,0.949,0,1,1,1.342,1.342l-3.376,3.376,3.376,3.376A0.95,0.95,0,0,1,940.706,112.706Z"
          transform="translate(-924 -96)"
        />
      </Svg>
    </View>
  );
};
export const iconVolume = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        data-name="Layer 1"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 122.88 84.86"
      >
        <Path
          d="M11.32,19.85H33.89L52.56,1a3.55,3.55,0,0,1,5,0,3.48,3.48,0,0,1,1,2.51h0V81.3a3.56,3.56,0,0,1-6.1,2.49l-18.45-15H11.32A11.35,11.35,0,0,1,0,57.49V31.17A11.37,11.37,0,0,1,11.32,19.85ZM74.71,31.62A3.32,3.32,0,0,1,81,29.51c1.14,3.39,1.69,8.66,1.6,13.67s-.81,9.72-2.19,12.57a3.33,3.33,0,0,1-6-2.91c1-2,1.47-5.76,1.55-9.77a38.19,38.19,0,0,0-1.27-11.45Zm17.14-12.4A3.32,3.32,0,0,1,98,16.67c3.08,7.4,4.75,16.71,4.89,26s-1.21,18.25-4.14,25.51a3.31,3.31,0,0,1-6.15-2.47c2.6-6.44,3.79-14.67,3.67-23s-1.63-16.86-4.41-23.5ZM108.42,8.68a3.32,3.32,0,1,1,6-2.88,89.44,89.44,0,0,1,8.48,37.53c.1,12.58-2.44,25.12-8,35.81a3.31,3.31,0,1,1-5.89-3c5-9.71,7.32-21.17,7.23-32.72a82.47,82.47,0,0,0-7.83-34.7Z"
          fill="#fff"
        />
      </Svg>
    </View>
  );
};
export const iconDelete = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 35 37"
        fill="none"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const iconLogOut = () => {
  return (
    <View>
      <Svg
        fill="#757575"
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        id="sign-out"
      >
        <Path d="M20.49,3.84l-6-1.5A2,2,0,0,0,12,4.28V5H10A2,2,0,0,0,8,7V8a1,1,0,0,0,2,0V7h2V17H10V16a1,1,0,0,0-2,0v1a2,2,0,0,0,2,2h2v.72a2,2,0,0,0,.77,1.57,2,2,0,0,0,1.23.43,2.12,2.12,0,0,0,.49-.06l6-1.5A2,2,0,0,0,22,18.22V5.78A2,2,0,0,0,20.49,3.84Z"></Path>
        <Path d="M4.41,13H9a1,1,0,0,0,0-2H4.41l1.3-1.29A1,1,0,0,0,4.29,8.29l-3,3h0a1.15,1.15,0,0,0-.21.33.94.94,0,0,0,0,.76,1.15,1.15,0,0,0,.21.33h0l3,3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42Z"></Path>
      </Svg>
    </View>
  );
};
export const iconMapArea = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 122.88 117.55"
      >
        <G>
          <Path
            d="M78.81,82.78c-4.35,4.77-9.42,9.05-15.12,12.51c-0.7,0.51-1.65,0.58-2.43,0.08 c-8.41-5.35-15.48-11.78-21.03-18.76c-7.66-9.61-12.49-20.27-14.14-30.53c-1.68-10.41-0.11-20.42,5.07-28.56 c2.04-3.22,4.65-6.15,7.83-8.68C46.3,3.01,54.65-0.06,62.96,0c8.01,0.06,15.91,3.05,22.74,9.28c2.4,2.18,4.42,4.68,6.07,7.39 c5.57,9.17,6.77,20.87,4.32,32.73c-2.41,11.71-8.41,23.62-17.28,33.35V82.78L78.81,82.78L78.81,82.78z M25.32,74.54 c1.98,0,3.59,1.61,3.59,3.59c0,1.98-1.61,3.59-3.59,3.59h-6.74l-8.88,28.67h103.22l-9.64-28.67h-5.57c-1.98,0-3.59-1.61-3.59-3.59 c0-1.98,1.61-3.59,3.59-3.59h10.7l14.46,43.01H0l13.32-43.01H25.32L25.32,74.54z M61.38,18.51c9.88,0,17.88,8.01,17.88,17.87 c0,9.88-8.01,17.88-17.88,17.88c-9.88,0-17.87-8-17.87-17.88C43.49,26.51,51.5,18.51,61.38,18.51L61.38,18.51L61.38,18.51z"
            fill="#B3DBC0"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconFileNon = () => {
  return (
    <View>
      <Svg
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 392 512.309"
      >
        <Path
          d="M58.883 0h186.242v93.425l.099 1.406c1.618 22.2 9.835 40.061 24.454 52.552 14.088 12.047 33.864 18.745 59.129 19.14l63.193.036v286.86c0 32.331-26.552 58.89-58.883 58.89H58.883C26.545 512.309 0 485.814 0 453.419V58.883C0 26.495 26.495 0 58.883 0zm48.892 412.172c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h141.71c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-141.71zm0-175.532c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h176.45c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-176.45zm0 84.77c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h167.102c5.674 0 10.281 4.606 10.281 10.28 0 5.673-4.607 10.28-10.281 10.28H107.775zM265.685 3.159L392 139.816v6.197l-63.03.035c-20.179-.346-35.532-5.327-45.967-14.251-10.288-8.796-16.109-21.853-17.318-38.414V3.159z"
          fill="#B3DBC0"
        />
      </Svg>
    </View>
  );
};
export const iconBanking = () => {
  return (
    <View>
      <Svg
        fill="#B3DBC0"
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="-1.5 0 19 19"
      >
        <Path d="M15.442 14.75v.491H.558v-.49a.476.476 0 0 1 .475-.476h.478a.487.487 0 0 1-.003-.048v-.443a.476.476 0 0 1 .475-.475h.713V7.164H1.508a.554.554 0 0 1-.22-1.063L7.78 3.288a.554.554 0 0 1 .44 0L14.712 6.1a.554.554 0 0 1-.22 1.063h-1.188v6.145h.713a.476.476 0 0 1 .475.475v.443a.443.443 0 0 1-.003.048h.478a.476.476 0 0 1 .475.475zM3.804 13.31h2.058V8.264H3.804zm.377-7.254h7.639L8 4.4zm2.79 2.21v5.043h2.058V8.265zm5.225 5.043V8.265h-2.059v5.044z" />
      </Svg>
    </View>
  );
};
export const iconDotMenu = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 29.957 122.88"
        enable-background="new 0 0 29.957 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14.978,0c8.27,0,14.979,6.708,14.979,14.979c0,8.27-6.709,14.976-14.979,14.976 C6.708,29.954,0,23.249,0,14.979C0,6.708,6.708,0,14.978,0L14.978,0z M14.978,92.926c8.27,0,14.979,6.708,14.979,14.979 s-6.709,14.976-14.979,14.976C6.708,122.88,0,116.175,0,107.904S6.708,92.926,14.978,92.926L14.978,92.926z M14.978,46.463 c8.27,0,14.979,6.708,14.979,14.979s-6.709,14.978-14.979,14.978C6.708,76.419,0,69.712,0,61.441S6.708,46.463,14.978,46.463 L14.978,46.463z"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconComment = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 21 22"
        fill="none"
      >
        <Path
          d="M7.175 17.15H6.7C2.9 17.15 1 16.2 1 11.45V6.7C1 2.9 2.9 1 6.7 1H14.3C18.1 1 20 2.9 20 6.7V11.45C20 15.25 18.1 17.15 14.3 17.15H13.825C13.5305 17.15 13.2455 17.2925 13.065 17.53L11.64 19.43C11.013 20.266 9.987 20.266 9.36 19.43L7.935 17.53C7.783 17.321 7.4315 17.15 7.175 17.15Z"
          stroke="#343434"
          stroke-width="2"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <Path
          d="M14.2966 9.55002H14.3051"
          stroke="#343434"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <Path
          d="M10.4957 9.55002H10.5043"
          stroke="#343434"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <Path
          d="M6.69482 9.55002H6.70336"
          stroke="#343434"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </Svg>
    </View>
  );
};
export const iconKeyboard = () => {
  return (
    <View>
      <Svg
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M8 5H16C18.8284 5 20.2426 5 21.1213 5.87868C22 6.75736 22 8.17157 22 11V13C22 15.8284 22 17.2426 21.1213 18.1213C20.2426 19 18.8284 19 16 19H8C5.17157 19 3.75736 19 2.87868 18.1213C2 17.2426 2 15.8284 2 13V11C2 8.17157 2 6.75736 2.87868 5.87868C3.75736 5 5.17157 5 8 5ZM6 10C6.55228 10 7 9.55228 7 9C7 8.44772 6.55228 8 6 8C5.44772 8 5 8.44772 5 9C5 9.55228 5.44772 10 6 10ZM6 13C6.55228 13 7 12.5523 7 12C7 11.4477 6.55228 11 6 11C5.44772 11 5 11.4477 5 12C5 12.5523 5.44772 13 6 13ZM9 13C9.55228 13 10 12.5523 10 12C10 11.4477 9.55228 11 9 11C8.44772 11 8 11.4477 8 12C8 12.5523 8.44772 13 9 13ZM9 10C9.55228 10 10 9.55228 10 9C10 8.44772 9.55228 8 9 8C8.44772 8 8 8.44772 8 9C8 9.55228 8.44772 10 9 10ZM12 10C12.5523 10 13 9.55228 13 9C13 8.44772 12.5523 8 12 8C11.4477 8 11 8.44772 11 9C11 9.55228 11.4477 10 12 10ZM12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13ZM15 10C15.5523 10 16 9.55228 16 9C16 8.44772 15.5523 8 15 8C14.4477 8 14 8.44772 14 9C14 9.55228 14.4477 10 15 10ZM15 13C15.5523 13 16 12.5523 16 12C16 11.4477 15.5523 11 15 11C14.4477 11 14 11.4477 14 12C14 12.5523 14.4477 13 15 13ZM18 10C18.5523 10 19 9.55228 19 9C19 8.44772 18.5523 8 18 8C17.4477 8 17 8.44772 17 9C17 9.55228 17.4477 10 18 10ZM18 13C18.5523 13 19 12.5523 19 12C19 11.4477 18.5523 11 18 11C17.4477 11 17 11.4477 17 12C17 12.5523 17.4477 13 18 13ZM17.75 16C17.75 16.4142 17.4142 16.75 17 16.75H7C6.58579 16.75 6.25 16.4142 6.25 16C6.25 15.5858 6.58579 15.25 7 15.25H17C17.4142 15.25 17.75 15.5858 17.75 16Z"
          fill="#1C274C"
        />
      </Svg>
    </View>
  );
};
export const iconGallery = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M15.75 2.625H5.25C4.55381 2.625 3.88613 2.90156 3.39384 3.39384C2.90156 3.88613 2.625 4.55381 2.625 5.25V15.75C2.625 16.4462 2.90156 17.1139 3.39384 17.6062C3.88613 18.0984 4.55381 18.375 5.25 18.375H15.75C16.4462 18.375 17.1139 18.0984 17.6062 17.6062C18.0984 17.1139 18.375 16.4462 18.375 15.75V5.25C18.375 4.55381 18.0984 3.88613 17.6062 3.39384C17.1139 2.90156 16.4462 2.625 15.75 2.625ZM5.25 4.375H15.75C15.9821 4.375 16.2046 4.46719 16.3687 4.63128C16.5328 4.79538 16.625 5.01794 16.625 5.25V12.565L13.825 10.1763C13.3912 9.81929 12.8468 9.62412 12.285 9.62412C11.7232 9.62412 11.1788 9.81929 10.745 10.1763L4.375 15.4875V5.25C4.375 5.01794 4.46719 4.79538 4.63128 4.63128C4.79538 4.46719 5.01794 4.375 5.25 4.375Z"
          fill="#346359"
        />
        <Path
          d="M7 8.75C7.72487 8.75 8.3125 8.16237 8.3125 7.4375C8.3125 6.71263 7.72487 6.125 7 6.125C6.27513 6.125 5.6875 6.71263 5.6875 7.4375C5.6875 8.16237 6.27513 8.75 7 8.75Z"
          fill="#346359"
        />
      </Svg>
    </View>
  );
};
export const iconGalleryChat = () => {
  return (
    <View>
      <Svg
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M15.75 2.625H5.25C4.55381 2.625 3.88613 2.90156 3.39384 3.39384C2.90156 3.88613 2.625 4.55381 2.625 5.25V15.75C2.625 16.4462 2.90156 17.1139 3.39384 17.6062C3.88613 18.0984 4.55381 18.375 5.25 18.375H15.75C16.4462 18.375 17.1139 18.0984 17.6062 17.6062C18.0984 17.1139 18.375 16.4462 18.375 15.75V5.25C18.375 4.55381 18.0984 3.88613 17.6062 3.39384C17.1139 2.90156 16.4462 2.625 15.75 2.625ZM5.25 4.375H15.75C15.9821 4.375 16.2046 4.46719 16.3687 4.63128C16.5328 4.79538 16.625 5.01794 16.625 5.25V12.565L13.825 10.1763C13.3912 9.81929 12.8468 9.62412 12.285 9.62412C11.7232 9.62412 11.1788 9.81929 10.745 10.1763L4.375 15.4875V5.25C4.375 5.01794 4.46719 4.79538 4.63128 4.63128C4.79538 4.46719 5.01794 4.375 5.25 4.375Z"
          fill="#346359"
        />
        <Path
          d="M7 8.75C7.72487 8.75 8.3125 8.16237 8.3125 7.4375C8.3125 6.71263 7.72487 6.125 7 6.125C6.27513 6.125 5.6875 6.71263 5.6875 7.4375C5.6875 8.16237 6.27513 8.75 7 8.75Z"
          fill="#346359"
        />
      </Svg>
    </View>
  );
};
export const iconGalleryEdit = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M15.75 2.625H5.25C4.55381 2.625 3.88613 2.90156 3.39384 3.39384C2.90156 3.88613 2.625 4.55381 2.625 5.25V15.75C2.625 16.4462 2.90156 17.1139 3.39384 17.6062C3.88613 18.0984 4.55381 18.375 5.25 18.375H15.75C16.4462 18.375 17.1139 18.0984 17.6062 17.6062C18.0984 17.1139 18.375 16.4462 18.375 15.75V5.25C18.375 4.55381 18.0984 3.88613 17.6062 3.39384C17.1139 2.90156 16.4462 2.625 15.75 2.625ZM5.25 4.375H15.75C15.9821 4.375 16.2046 4.46719 16.3687 4.63128C16.5328 4.79538 16.625 5.01794 16.625 5.25V12.565L13.825 10.1763C13.3912 9.81929 12.8468 9.62412 12.285 9.62412C11.7232 9.62412 11.1788 9.81929 10.745 10.1763L4.375 15.4875V5.25C4.375 5.01794 4.46719 4.79538 4.63128 4.63128C4.79538 4.46719 5.01794 4.375 5.25 4.375Z"
          fill="#346359"
        />
        <Path
          d="M7 8.75C7.72487 8.75 8.3125 8.16237 8.3125 7.4375C8.3125 6.71263 7.72487 6.125 7 6.125C6.27513 6.125 5.6875 6.71263 5.6875 7.4375C5.6875 8.16237 6.27513 8.75 7 8.75Z"
          fill="#346359"
        />
      </Svg>
    </View>
  );
};
export const iconSetting = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M20.1 9.2214C18.29 9.2214 17.55 7.9414 18.45 6.3714C18.97 5.4614 18.66 4.3014 17.75 3.7814L16.02 2.7914C15.23 2.3214 14.21 2.6014 13.74 3.3914L13.63 3.5814C12.73 5.1514 11.25 5.1514 10.34 3.5814L10.23 3.3914C9.78 2.6014 8.76 2.3214 7.97 2.7914L6.24 3.7814C5.33 4.3014 5.02 5.4714 5.54 6.3814C6.45 7.9414 5.71 9.2214 3.9 9.2214C2.86 9.2214 2 10.0714 2 11.1214V12.8814C2 13.9214 2.85 14.7814 3.9 14.7814C5.71 14.7814 6.45 16.0614 5.54 17.6314C5.02 18.5414 5.33 19.7014 6.24 20.2214L7.97 21.2114C8.76 21.6814 9.78 21.4014 10.25 20.6114L10.36 20.4214C11.26 18.8514 12.74 18.8514 13.65 20.4214L13.76 20.6114C14.23 21.4014 15.25 21.6814 16.04 21.2114L17.77 20.2214C18.68 19.7014 18.99 18.5314 18.47 17.6314C17.56 16.0614 18.3 14.7814 20.11 14.7814C21.15 14.7814 22.01 13.9314 22.01 12.8814V11.1214C22 10.0814 21.15 9.2214 20.1 9.2214ZM12 15.2514C10.21 15.2514 8.75 13.7914 8.75 12.0014C8.75 10.2114 10.21 8.7514 12 8.7514C13.79 8.7514 15.25 10.2114 15.25 12.0014C15.25 13.7914 13.79 15.2514 12 15.2514Z"
          fill="#757575"
        />
      </Svg>
    </View>
  );
};
export const iconLanguage = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M20.58 19.37L17.59 11.01C17.38 10.46 16.91 10.12 16.37 10.12C15.83 10.12 15.37 10.46 15.14 11.03L12.16 19.37C12.02 19.76 12.22 20.19 12.61 20.33C13 20.47 13.43 20.27 13.57 19.88L14.19 18.15H18.54L19.16 19.88C19.27 20.19 19.56 20.38 19.87 20.38C19.95 20.38 20.04 20.37 20.12 20.34C20.51 20.2 20.71 19.77 20.57 19.38L20.58 19.37ZM14.74 16.64L16.38 12.05L18.02 16.64H14.74ZM12.19 7.85C9.92999 11.42 7.89 13.58 5.41 15.02C5.29 15.09 5.16 15.12 5.04 15.12C4.78 15.12 4.53 14.99 4.39 14.75C4.18 14.39 4.3 13.93 4.66 13.73C6.75999 12.51 8.48 10.76 10.41 7.86H4.12C3.71 7.86 3.37 7.52 3.37 7.11C3.37 6.7 3.71 6.36 4.12 6.36H7.87V4.38C7.87 3.97 8.21 3.63 8.62 3.63C9.02999 3.63 9.37 3.97 9.37 4.38V6.36H13.12C13.53 6.36 13.87 6.7 13.87 7.11C13.87 7.52 13.53 7.86 13.12 7.86H12.18L12.19 7.85ZM12.23 15.12C12.1 15.12 11.97 15.09 11.85 15.02C11.2 14.64 10.57 14.22 9.97999 13.78C9.64999 13.53 9.58 13.06 9.83 12.73C10.08 12.4 10.55 12.33 10.88 12.58C11.42 12.99 12.01 13.37 12.61 13.72C12.97 13.93 13.09 14.39 12.88 14.75C12.74 14.99 12.49 15.12 12.23 15.12Z"
          fill="#444444"
        />
      </Svg>
    </View>
  );
};
export const iconEyeOpen = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 122.879 79.699"
        enable-background="new 0 0 122.879 79.699"
      >
        <G>
          <Path
            d="M0.95,30.01c2.92-3.53,5.98-6.74,9.15-9.63C24.44,7.33,41.46,0.36,59.01,0.01c17.51-0.35,35.47,5.9,51.7,19.29 c3.88,3.2,7.63,6.77,11.24,10.74c1.16,1.28,1.22,3.17,0.23,4.51c-4.13,5.83-8.88,10.82-14.07,14.96 C95.12,59.88,79.34,64.98,63.35,65.06c-15.93,0.07-32.06-4.86-45.8-14.57c-6.14-4.34-11.81-9.63-16.78-15.85 C-0.34,33.24-0.23,31.27,0.95,30.01L0.95,30.01z M61.44,26.46c0.59,0,1.17,0.09,1.71,0.24c-0.46,0.5-0.73,1.17-0.73,1.9 c0,1.56,1.26,2.82,2.82,2.82c0.77,0,1.46-0.3,1.97-0.8c0.2,0.6,0.3,1.24,0.3,1.9c0,3.35-2.72,6.07-6.07,6.07 c-3.35,0-6.07-2.72-6.07-6.07C55.37,29.18,58.09,26.46,61.44,26.46L61.44,26.46z M61.44,10.82c5.99,0,11.42,2.43,15.35,6.36 c3.93,3.93,6.36,9.35,6.36,15.35c0,5.99-2.43,11.42-6.36,15.35c-3.93,3.93-9.35,6.36-15.35,6.36c-5.99,0-11.42-2.43-15.35-6.36 c-3.93-3.93-6.36-9.35-6.36-15.35c0-5.99,2.43-11.42,6.36-15.35C50.02,13.25,55.45,10.82,61.44,10.82L61.44,10.82z M71.89,22.08 c-2.67-2.67-6.37-4.33-10.45-4.33c-4.08,0-7.78,1.65-10.45,4.33c-2.67,2.67-4.33,6.37-4.33,10.45c0,4.08,1.65,7.78,4.33,10.45 c2.67,2.67,6.37,4.33,10.45,4.33c4.08,0,7.78-1.65,10.45-4.33c2.67-2.67,4.33-6.37,4.33-10.45C76.22,28.45,74.56,24.75,71.89,22.08 L71.89,22.08z M14.89,25.63c-2.32,2.11-4.56,4.39-6.7,6.82c4.07,4.72,8.6,8.8,13.45,12.23c12.54,8.85,27.21,13.35,41.69,13.29 c14.42-0.07,28.65-4.67,40.37-14.02c4-3.19,7.7-6.94,11.03-11.25c-2.79-2.91-5.63-5.54-8.51-7.92C91.33,12.51,75,6.79,59.15,7.1 C43.34,7.42,27.93,13.76,14.89,25.63L14.89,25.63z"
            fill="#5C5C5C"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconEditImg = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(12)}
        height={verticalScale(12)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#ffff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeleteProcess = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#ffff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeleteGallery = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(12)}
        height={verticalScale(12)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#ffff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconBirdDay = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 17 20"
        fill="none"
      >
        <Path
          d="M8.5 5.25C9.47125 5.25 10.25 4.4625 10.25 3.5C10.25 3.1675 10.1625 2.86125 9.99625 2.59875L8.5 0L7.00375 2.59875C6.8375 2.86125 6.75 3.1675 6.75 3.5C6.75 4.4625 7.5375 5.25 8.5 5.25ZM12.525 13.9913L11.5887 13.055L10.6438 13.9913C9.50625 15.1288 7.51125 15.1375 6.365 13.9913L5.42875 13.055L4.475 13.9913C3.90625 14.56 3.145 14.875 2.34 14.875C1.70125 14.875 1.115 14.6738 0.625 14.3413V18.375C0.625 18.8563 1.01875 19.25 1.5 19.25H15.5C15.9812 19.25 16.375 18.8563 16.375 18.375V14.3413C15.885 14.6738 15.2988 14.875 14.66 14.875C13.855 14.875 13.0938 14.56 12.525 13.9913ZM13.75 7.875H9.375V6.125H7.625V7.875H3.25C1.7975 7.875 0.625 9.0475 0.625 10.5V11.8475C0.625 12.7925 1.395 13.5625 2.34 13.5625C2.795 13.5625 3.2325 13.3875 3.5475 13.0638L5.42 11.2L7.28375 13.0638C7.93125 13.7113 9.06 13.7113 9.7075 13.0638L11.58 11.2L13.4437 13.0638C13.7675 13.3875 14.1963 13.5625 14.6513 13.5625C15.5963 13.5625 16.3663 12.7925 16.3663 11.8475V10.5C16.375 9.0475 15.2025 7.875 13.75 7.875Z"
          fill="#2B2B2B"
        />
      </Svg>
    </View>
  );
};
export const iconPlusPlant = () => {
  return (
    <View>
      <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 16 16">
        <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <G transform="translate(0.000000, 1.000000)" fill="#84B8A2">
            <Path d="M9.927,11.918 C9.887,11.833 9.86,11.741 9.86,11.639 L9.86,7.483 C9.86,7.145 10.146,6.907 10.448,6.907 L10.469,6.907 C10.77,6.907 11.063,7.145 11.063,7.483 L11.063,10.943 L11.965,10.943 L11.965,8.982 L13.258,8.982 L13.422,5.976 L14.188,5.976 C14.588,5.976 14.913,4.756 14.913,4.756 C14.913,4.386 14.589,4.084 14.188,4.084 L12.26,4.084 L11.225,0.447 C11.074,0.13 10.699,0.00199999998 10.387,0.161 L10.315,0.197 C10.005,0.357 9.876,0.743 10.027,1.06 L10.768,4.083 L4.114,4.083 L4.882,1.064 C5.036,0.75 4.909,0.362 4.601,0.199 L4.531,0.163 C4.22,0.000999999981 3.843,0.125 3.689,0.44 L2.616,4.083 L0.726,4.083 C0.326,4.083 0.000999999931,4.385 0.000999999931,4.755 C0.000999999931,4.755 0.325,5.975 0.726,5.975 L1.362,5.975 L1.811,12.652 C1.811,12.652 1.863,13.961 3.924,13.961 L9.928,13.961 L9.928,11.918 L9.927,11.918 Z M11.969,5 L13.031,5 L13.031,6.062 L11.969,6.062 L11.969,5 L11.969,5 Z M3.094,6.031 L1.912,6.031 L1.912,4.906 L3.094,4.906 L3.094,6.031 L3.094,6.031 Z M5.006,11.742 C5.006,12.092 4.755,12.375 4.447,12.375 L4.424,12.375 C4.113,12.375 3.863,12.092 3.863,11.742 L3.863,7.413 C3.863,7.063 4.113,6.781 4.424,6.781 L4.447,6.781 C4.755,6.781 5.006,7.063 5.006,7.413 L5.006,11.742 L5.006,11.742 Z M8.004,11.547 C8.004,11.881 7.774,12.152 7.49,12.152 L7.469,12.152 C7.185,12.152 6.955,11.881 6.955,11.547 L6.955,7.448 C6.955,7.114 7.184,6.844 7.469,6.844 L7.49,6.844 C7.773,6.844 8.004,7.115 8.004,7.448 L8.004,11.547 L8.004,11.547 Z"></Path>
            <Path d="M16,12.012 L13.992,12.012 L13.992,10.106 L13.055,10.106 L13.055,12.012 L11.052,12.012 L11.052,12.906 L13.055,12.906 L13.055,14.938 L13.992,14.938 L13.992,12.906 L16,12.906 L16,12.012 Z"></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconPlusPlantClick = () => {
  return (
    <View>
      <Svg width={scale(110)} height={verticalScale(110)} viewBox="0 0 16 16">
        <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <G transform="translate(0.000000, 1.000000)" fill="#84B8A2">
            <Path d="M9.927,11.918 C9.887,11.833 9.86,11.741 9.86,11.639 L9.86,7.483 C9.86,7.145 10.146,6.907 10.448,6.907 L10.469,6.907 C10.77,6.907 11.063,7.145 11.063,7.483 L11.063,10.943 L11.965,10.943 L11.965,8.982 L13.258,8.982 L13.422,5.976 L14.188,5.976 C14.588,5.976 14.913,4.756 14.913,4.756 C14.913,4.386 14.589,4.084 14.188,4.084 L12.26,4.084 L11.225,0.447 C11.074,0.13 10.699,0.00199999998 10.387,0.161 L10.315,0.197 C10.005,0.357 9.876,0.743 10.027,1.06 L10.768,4.083 L4.114,4.083 L4.882,1.064 C5.036,0.75 4.909,0.362 4.601,0.199 L4.531,0.163 C4.22,0.000999999981 3.843,0.125 3.689,0.44 L2.616,4.083 L0.726,4.083 C0.326,4.083 0.000999999931,4.385 0.000999999931,4.755 C0.000999999931,4.755 0.325,5.975 0.726,5.975 L1.362,5.975 L1.811,12.652 C1.811,12.652 1.863,13.961 3.924,13.961 L9.928,13.961 L9.928,11.918 L9.927,11.918 Z M11.969,5 L13.031,5 L13.031,6.062 L11.969,6.062 L11.969,5 L11.969,5 Z M3.094,6.031 L1.912,6.031 L1.912,4.906 L3.094,4.906 L3.094,6.031 L3.094,6.031 Z M5.006,11.742 C5.006,12.092 4.755,12.375 4.447,12.375 L4.424,12.375 C4.113,12.375 3.863,12.092 3.863,11.742 L3.863,7.413 C3.863,7.063 4.113,6.781 4.424,6.781 L4.447,6.781 C4.755,6.781 5.006,7.063 5.006,7.413 L5.006,11.742 L5.006,11.742 Z M8.004,11.547 C8.004,11.881 7.774,12.152 7.49,12.152 L7.469,12.152 C7.185,12.152 6.955,11.881 6.955,11.547 L6.955,7.448 C6.955,7.114 7.184,6.844 7.469,6.844 L7.49,6.844 C7.773,6.844 8.004,7.115 8.004,7.448 L8.004,11.547 L8.004,11.547 Z"></Path>
            <Path d="M16,12.012 L13.992,12.012 L13.992,10.106 L13.055,10.106 L13.055,12.012 L11.052,12.012 L11.052,12.906 L13.055,12.906 L13.055,14.938 L13.992,14.938 L13.992,12.906 L16,12.906 L16,12.012 Z"></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconCopyText = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M16 8V5.2C16 4.0799 16 3.51984 15.782 3.09202C15.5903 2.71569 15.2843 2.40973 14.908 2.21799C14.4802 2 13.9201 2 12.8 2H5.2C4.0799 2 3.51984 2 3.09202 2.21799C2.71569 2.40973 2.40973 2.71569 2.21799 3.09202C2 3.51984 2 4.0799 2 5.2V12.8C2 13.9201 2 14.4802 2.21799 14.908C2.40973 15.2843 2.71569 15.5903 3.09202 15.782C3.51984 16 4.0799 16 5.2 16H8M11.2 22H18.8C19.9201 22 20.4802 22 20.908 21.782C21.2843 21.5903 21.5903 21.2843 21.782 20.908C22 20.4802 22 19.9201 22 18.8V11.2C22 10.0799 22 9.51984 21.782 9.09202C21.5903 8.71569 21.2843 8.40973 20.908 8.21799C20.4802 8 19.9201 8 18.8 8H11.2C10.0799 8 9.51984 8 9.09202 8.21799C8.71569 8.40973 8.40973 8.71569 8.21799 9.09202C8 9.51984 8 10.0799 8 11.2V18.8C8 19.9201 8 20.4802 8.21799 20.908C8.40973 21.2843 8.71569 21.5903 9.09202 21.782C9.51984 22 10.0799 22 11.2 22Z"
          stroke="#656565"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </Svg>
    </View>
  );
};
export const iconFileArea = () => {
  return (
    <View>
      <Svg
        shape-rendering="geometricPrecision"
        text-rendering="geometricPrecision"
        image-rendering="optimizeQuality"
        fill-rule="evenodd"
        clip-rule="evenodd"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 392 512.309"
      >
        <Path
          d="M58.883 0h186.242v93.425l.099 1.406c1.618 22.2 9.835 40.061 24.454 52.552 14.088 12.047 33.864 18.745 59.129 19.14l63.193.036v286.86c0 32.331-26.552 58.89-58.883 58.89H58.883C26.545 512.309 0 485.814 0 453.419V58.883C0 26.495 26.495 0 58.883 0zm48.892 412.172c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h141.71c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-141.71zm0-175.532c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h176.45c5.673 0 10.28 4.606 10.28 10.28 0 5.673-4.607 10.28-10.28 10.28h-176.45zm0 84.77c-5.673 0-10.28-4.607-10.28-10.28 0-5.674 4.607-10.28 10.28-10.28h167.102c5.674 0 10.281 4.606 10.281 10.28 0 5.673-4.607 10.28-10.281 10.28H107.775zM265.685 3.159L392 139.816v6.197l-63.03.035c-20.179-.346-35.532-5.327-45.967-14.251-10.288-8.796-16.109-21.853-17.318-38.414V3.159z"
          fill="#ffff"
        />
      </Svg>
    </View>
  );
};
export const iconSaveEdit = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 122.877 101.052"
        enable-background="new 0 0 122.877 101.052"
      >
        <G>
          <Path
            d="M4.43,63.63c-2.869-2.755-4.352-6.42-4.427-10.11c-0.074-3.689,1.261-7.412,4.015-10.281 c2.752-2.867,6.417-4.351,10.106-4.425c3.691-0.076,7.412,1.255,10.283,4.012l24.787,23.851L98.543,3.989l1.768,1.349l-1.77-1.355 c0.141-0.183,0.301-0.339,0.479-0.466c2.936-2.543,6.621-3.691,10.223-3.495V0.018l0.176,0.016c3.623,0.24,7.162,1.85,9.775,4.766 c2.658,2.965,3.863,6.731,3.662,10.412h0.004l-0.016,0.176c-0.236,3.558-1.791,7.035-4.609,9.632l-59.224,72.09l0.004,0.004 c-0.111,0.141-0.236,0.262-0.372,0.368c-2.773,2.435-6.275,3.629-9.757,3.569c-3.511-0.061-7.015-1.396-9.741-4.016L4.43,63.63 L4.43,63.63z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconSavePost = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 122.877 101.052"
        enable-background="new 0 0 122.877 101.052"
      >
        <G>
          <Path
            d="M4.43,63.63c-2.869-2.755-4.352-6.42-4.427-10.11c-0.074-3.689,1.261-7.412,4.015-10.281 c2.752-2.867,6.417-4.351,10.106-4.425c3.691-0.076,7.412,1.255,10.283,4.012l24.787,23.851L98.543,3.989l1.768,1.349l-1.77-1.355 c0.141-0.183,0.301-0.339,0.479-0.466c2.936-2.543,6.621-3.691,10.223-3.495V0.018l0.176,0.016c3.623,0.24,7.162,1.85,9.775,4.766 c2.658,2.965,3.863,6.731,3.662,10.412h0.004l-0.016,0.176c-0.236,3.558-1.791,7.035-4.609,9.632l-59.224,72.09l0.004,0.004 c-0.111,0.141-0.236,0.262-0.372,0.368c-2.773,2.435-6.275,3.629-9.757,3.569c-3.511-0.061-7.015-1.396-9.741-4.016L4.43,63.63 L4.43,63.63z"
            fill="#346359"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconEyeClose = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 122.879 79.699"
        enable-background="new 0 0 122.879 79.699"
      >
        <G>
          <Path
            d="M0.955,37.326c2.922-3.528,5.981-6.739,9.151-9.625C24.441,14.654,41.462,7.684,59.01,7.334 c6.561-0.131,13.185,0.665,19.757,2.416l-5.904,5.904c-4.581-0.916-9.168-1.324-13.714-1.233 c-15.811,0.316-31.215,6.657-44.262,18.533l0,0c-2.324,2.115-4.562,4.39-6.702,6.82c4.071,4.721,8.6,8.801,13.452,12.227 c2.988,2.111,6.097,3.973,9.296,5.586l-5.262,5.262c-2.782-1.504-5.494-3.184-8.12-5.039c-6.143-4.338-11.813-9.629-16.78-15.85 C-0.338,40.563-0.228,38.59,0.955,37.326L0.955,37.326L0.955,37.326z M96.03,0l5.893,5.893L28.119,79.699l-5.894-5.895L96.03,0 L96.03,0z M97.72,17.609c4.423,2.527,8.767,5.528,12.994,9.014c3.877,3.196,7.635,6.773,11.24,10.735 c1.163,1.277,1.22,3.171,0.226,4.507c-4.131,5.834-8.876,10.816-14.069,14.963C95.119,67.199,79.338,72.305,63.352,72.377 c-6.114,0.027-9.798-3.141-15.825-4.576l3.545-3.543c4.065,0.705,8.167,1.049,12.252,1.031c14.421-0.064,28.653-4.668,40.366-14.02 c3.998-3.191,7.706-6.939,11.028-11.254c-2.787-2.905-5.627-5.543-8.508-7.918c-4.455-3.673-9.042-6.759-13.707-9.273L97.72,17.609 L97.72,17.609z M61.44,18.143c2.664,0,5.216,0.481,7.576,1.359l-5.689,5.689c-0.619-0.079-1.248-0.119-1.886-0.119 c-4.081,0-7.775,1.654-10.449,4.328c-2.674,2.674-4.328,6.369-4.328,10.45c0,0.639,0.04,1.268,0.119,1.885l-5.689,5.691 c-0.879-2.359-1.359-4.912-1.359-7.576c0-5.995,2.43-11.42,6.358-15.349C50.02,20.572,55.446,18.143,61.44,18.143L61.44,18.143z M82.113,33.216c0.67,2.09,1.032,4.32,1.032,6.634c0,5.994-2.43,11.42-6.357,15.348c-3.929,3.928-9.355,6.357-15.348,6.357 c-2.313,0-4.542-0.361-6.633-1.033l5.914-5.914c0.238,0.012,0.478,0.018,0.719,0.018c4.081,0,7.775-1.652,10.449-4.326 s4.328-6.369,4.328-10.449c0-0.241-0.006-0.48-0.018-0.72L82.113,33.216L82.113,33.216z"
            fill="#5C5C5C"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconEditPost = () => {
  return (
    <View>
      <Svg
        viewBox="0 0 1024 1024"
        fill="#346359"
        width={scale(18)}
        height={verticalScale(18)}
      >
        <Path d="M880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm-622.3-84c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9z" />
      </Svg>
    </View>
  );
};
export const iconNextArea = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        id="_24x24_On_Light_Next"
        data-name="24x24/On Light/Next"
      >
        <Rect id="view-box" width="24" height="24" fill="#141124" opacity="0" />
        <Path
          id="Shape"
          d="M10.22,9.28a.75.75,0,0,1,0-1.06l2.72-2.72H.75A.75.75,0,0,1,.75,4H12.938L10.22,1.281A.75.75,0,1,1,11.281.22l4,4a.749.749,0,0,1,0,1.06l-4,4a.75.75,0,0,1-1.061,0Z"
          transform="translate(4.25 7.25)"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const iconUserEdit = () => {
  return (
    <View>
      <Svg
        fill="#444444"
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 -64 640 640"
      >
        <Path d="M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h274.9c-2.4-6.8-3.4-14-2.6-21.3l6.8-60.9 1.2-11.1 7.9-7.9 77.3-77.3c-24.5-27.7-60-45.5-99.9-45.5zm45.3 145.3l-6.8 61c-1.1 10.2 7.5 18.8 17.6 17.6l60.9-6.8 137.9-137.9-71.7-71.7-137.9 137.8zM633 268.9L595.1 231c-9.3-9.3-24.5-9.3-33.8 0l-37.8 37.8-4.1 4.1 71.8 71.7 41.8-41.8c9.3-9.4 9.3-24.5 0-33.9z" />
      </Svg>
    </View>
  );
};
export const iconNoPlants = () => {
  return (
    <Svg width={scale(30)} height={verticalScale(30)} viewBox="0 0 24 24">
      <G>
        <Path fill="none" d="M0 0H24V24H0z" />
        <Path
          d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z"
          fill="#D6D6D6"
        />
      </G>
    </Svg>
  );
};
export const iconFilterNon = () => {
  return (
    <View>
      <View>
        <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 24 24">
          <G id="SVGRepo_bgCarrier" stroke-width="0" />

          <G
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          />

          <G id="SVGRepo_iconCarrier">
            <Path
              d="M19.7491 15.41L18.8992 14.56C19.3392 13.89 19.5991 13.1 19.5991 12.24C19.5991 9.89999 17.6991 8 15.3591 8C13.0191 8 11.1191 9.89999 11.1191 12.24C11.1191 14.58 13.0191 16.48 15.3591 16.48C16.2191 16.48 17.0191 16.22 17.6791 15.78L18.5292 16.63C18.6992 16.8 18.9192 16.88 19.1392 16.88C19.3592 16.88 19.5791 16.8 19.7491 16.63C20.0791 16.29 20.0791 15.74 19.7491 15.41Z"
              fill="#ffffff"
            />
            <Path
              opacity="0.4"
              d="M5.41016 2H18.5802C19.6802 2 20.5802 2.90999 20.5802 4.01999V6.23999C20.5802 7.04999 20.0802 8.06 19.5802 8.56L15.2902 12.4C14.6902 12.91 14.2902 13.92 14.2902 14.72V19.06C14.2902 19.67 13.8902 20.47 13.3902 20.78L11.9902 21.69C10.6902 22.5 8.90021 21.59 8.90021 19.97V14.62C8.90021 13.91 8.50016 13 8.10016 12.5L4.31018 8.45999C3.81018 7.94999 3.41016 7.05 3.41016 6.44V4.12C3.42016 2.91 4.32016 2 5.41016 2Z"
              fill="#ffffff"
            />
          </G>
        </Svg>
      </View>
    </View>
  );
};
export const iconPinDetail = () => {
  return (
    <View>
      <Svg
        width={scale(16)}
        height={verticalScale(16)}
        viewBox="0 0 15 18"
        fill="none"
      >
        <Path
          d="M14.7888 5.88293C13.9014 1.82634 10.4958 0 7.50424 0C7.50424 0 7.50424 0 7.49579 0C4.51266 0 1.09857 1.81756 0.211249 5.87415C-0.777484 10.4049 1.89294 14.2419 4.30984 16.6566C5.20562 17.5522 6.35494 18 7.50424 18C8.65353 18 9.80283 17.5522 10.6902 16.6566C13.1071 14.2419 15.7775 10.4137 14.7888 5.88293ZM7.50424 10.282C6.03381 10.282 4.84224 9.0439 4.84224 7.5161C4.84224 5.98829 6.03381 4.75024 7.50424 4.75024C8.97466 4.75024 10.1662 5.98829 10.1662 7.5161C10.1662 9.0439 8.97466 10.282 7.50424 10.282Z"
          fill="#2A2A2A"
        />
      </Svg>
    </View>
  );
};
export const iconTypeVideo = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
        // xmlns="http://www.w3.org/2000/svg"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM14.2222 9H7.55556C7.25 9 7 9.25 7 9.55556V15.1111C7 15.4167 7.25 15.6667 7.55556 15.6667H14.2222C14.5278 15.6667 14.7778 15.4167 14.7778 15.1111V13.1667L17 15.3889V9.27778L14.7778 11.5V9.55556C14.7778 9.25 14.5278 9 14.2222 9ZM13.6667 10.1111V14.5556H8.11111V10.1111H13.6667Z"
          fill="#ffffff"
        />
      </Svg>
    </View>
  );
};
export const iconDeletepay = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={moderateScale(15)}
        height={moderateScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill={"#FFFFFF"}
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconUploadPay = () => {
  return (
    <View>
      <Svg
        width={scale(90)}
        height={verticalScale(90)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8 10C8 7.79086 9.79086 6 12 6C14.2091 6 16 7.79086 16 10V11H17C18.933 11 20.5 12.567 20.5 14.5C20.5 16.433 18.933 18 17 18H16C15.4477 18 15 18.4477 15 19C15 19.5523 15.4477 20 16 20H17C20.0376 20 22.5 17.5376 22.5 14.5C22.5 11.7793 20.5245 9.51997 17.9296 9.07824C17.4862 6.20213 15.0003 4 12 4C8.99974 4 6.51381 6.20213 6.07036 9.07824C3.47551 9.51997 1.5 11.7793 1.5 14.5C1.5 17.5376 3.96243 20 7 20H8C8.55228 20 9 19.5523 9 19C9 18.4477 8.55228 18 8 18H7C5.067 18 3.5 16.433 3.5 14.5C3.5 12.567 5.067 11 7 11H8V10ZM15.7071 13.2929L12.7071 10.2929C12.3166 9.90237 11.6834 9.90237 11.2929 10.2929L8.29289 13.2929C7.90237 13.6834 7.90237 14.3166 8.29289 14.7071C8.68342 15.0976 9.31658 15.0976 9.70711 14.7071L11 13.4142V19C11 19.5523 11.4477 20 12 20C12.5523 20 13 19.5523 13 19V13.4142L14.2929 14.7071C14.6834 15.0976 15.3166 15.0976 15.7071 14.7071C16.0976 14.3166 16.0976 13.6834 15.7071 13.2929Z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconUploadMedia = () => {
  return (
    <View>
      <Svg
        fill="#84B8A2"
        width={scale(90)}
        height={verticalScale(90)}
        viewBox="0 0 24 24"
      >
        <Path d="m9 13 3-4 3 4.5V12h4V5c0-1.103-.897-2-2-2H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h8v-4H5l3-4 1 2z" />
        <Path d="M19 14h-2v3h-3v2h3v3h2v-3h3v-2h-3z" />
      </Svg>
    </View>
  );
};
export const iconSendDisble = () => {
  return (
    <View>
      <Svg
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M16.1391 2.95907L7.10914 5.95907C1.03914 7.98907 1.03914 11.2991 7.10914 13.3191L9.78914 14.2091L10.6791 16.8891C12.6991 22.9591 16.0191 22.9591 18.0391 16.8891L21.0491 7.86907C22.3891 3.81907 20.1891 1.60907 16.1391 2.95907ZM16.4591 8.33907L12.6591 12.1591C12.5091 12.3091 12.3191 12.3791 12.1291 12.3791C11.9391 12.3791 11.7491 12.3091 11.5991 12.1591C11.3091 11.8691 11.3091 11.3891 11.5991 11.0991L15.3991 7.27907C15.6891 6.98907 16.1691 6.98907 16.4591 7.27907C16.7491 7.56907 16.7491 8.04907 16.4591 8.33907Z"
          fill="#DEDEDEDE"
        />
      </Svg>
    </View>
  );
};
export const iconNextDetail = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        id="_24x24_On_Light_Next"
        data-name="24x24/On Light/Next"
      >
        <Rect id="view-box" width="24" height="24" fill="#141124" opacity="0" />
        <Path
          id="Shape"
          d="M10.22,9.28a.75.75,0,0,1,0-1.06l2.72-2.72H.75A.75.75,0,0,1,.75,4H12.938L10.22,1.281A.75.75,0,1,1,11.281.22l4,4a.749.749,0,0,1,0,1.06l-4,4a.75.75,0,0,1-1.061,0Z"
          transform="translate(4.25 7.25)"
          fill="#ffffff"
        />
      </Svg>
    </View>
  );
};
export const iconFilterArea = () => {
  return (
    <View>
      <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 24 24">
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            d="M19.7491 15.41L18.8992 14.56C19.3392 13.89 19.5991 13.1 19.5991 12.24C19.5991 9.89999 17.6991 8 15.3591 8C13.0191 8 11.1191 9.89999 11.1191 12.24C11.1191 14.58 13.0191 16.48 15.3591 16.48C16.2191 16.48 17.0191 16.22 17.6791 15.78L18.5292 16.63C18.6992 16.8 18.9192 16.88 19.1392 16.88C19.3592 16.88 19.5791 16.8 19.7491 16.63C20.0791 16.29 20.0791 15.74 19.7491 15.41Z"
            fill="#84B8A2"
          />
          <Path
            opacity="0.4"
            d="M5.41016 2H18.5802C19.6802 2 20.5802 2.90999 20.5802 4.01999V6.23999C20.5802 7.04999 20.0802 8.06 19.5802 8.56L15.2902 12.4C14.6902 12.91 14.2902 13.92 14.2902 14.72V19.06C14.2902 19.67 13.8902 20.47 13.3902 20.78L11.9902 21.69C10.6902 22.5 8.90021 21.59 8.90021 19.97V14.62C8.90021 13.91 8.50016 13 8.10016 12.5L4.31018 8.45999C3.81018 7.94999 3.41016 7.05 3.41016 6.44V4.12C3.42016 2.91 4.32016 2 5.41016 2Z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconSearchArea = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 20 20"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M12.2183 12.2183C14.2284 10.2082 14.2284 6.94914 12.2183 4.93903C10.2082 2.92893 6.94914 2.92893 4.93903 4.93903C2.92893 6.94914 2.92893 10.2082 4.93903 12.2183C6.94914 14.2284 10.2082 14.2284 12.2183 12.2183ZM13.3101 15.7365C9.97925 17.9433 5.44732 17.5794 2.51263 14.6447C-0.837542 11.2945 -0.837542 5.8628 2.51263 2.51263C5.8628 -0.837542 11.2945 -0.837542 14.6447 2.51263C17.5794 5.44732 17.9433 9.97925 15.7365 13.3101L19.4975 17.0711C20.1675 17.7411 20.1675 18.8274 19.4975 19.4975C18.8274 20.1675 17.7411 20.1675 17.0711 19.4975L13.3101 15.7365Z"
          fill="#6A938D"
        />
      </Svg>
    </View>
  );
};
export const iconQrPayChang = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M21 7.505a1.5 1.5 0 0 1-1.5 1.5h-3a1.5 1.5 0 0 1-1.5-1.5V4.5A1.5 1.5 0 0 1 16.5 3h3A1.5 1.5 0 0 1 21 4.5v3.005zM9 7.5V4.504a1.5 1.5 0 0 0-1.498-1.5l-3-.003A1.5 1.5 0 0 0 3 4.5V7.5A1.5 1.5 0 0 0 4.5 9h3A1.5 1.5 0 0 0 9 7.5zM5 7V5.001l2 .002V7H5zm-.502 8.004 3-.003A1.5 1.5 0 0 1 9 16.5V19.5A1.5 1.5 0 0 1 7.5 21h-3A1.5 1.5 0 0 1 3 19.5v-2.996a1.5 1.5 0 0 1 1.498-1.5zM7 19H5v-1.997l2-.002V19zM19 5v2.005h-2V5h2zm0 11a1 1 0 1 1 2 0v3.5a1.5 1.5 0 0 1-1.498 1.5l-3.5.005a1 1 0 1 1-.003-2L19 19V16zM11 4v7H4a1 1 0 1 0 0 2h8a1 1 0 0 0 1-1V4a1 1 0 1 0-2 0zm2 16a1 1 0 1 1-2 0v-4a1 1 0 1 1 2 0v4zm3-3a1 1 0 0 0 1-1v-3h3a1 1 0 1 0 0-2h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1z"
          fill="#ffff"
        />
      </Svg>
    </View>
  );
};
export const iconDeleteArea = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill={"#FF9934"}
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconSaveDetail = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M3 9C3 6.17157 3 4.75736 3.87868 3.87868C4.75736 3 6.17157 3 9 3H15.3431C16.1606 3 16.5694 3 16.9369 3.15224C17.3045 3.30448 17.5935 3.59351 18.1716 4.17157L19.8284 5.82843C20.4065 6.40649 20.6955 6.69552 20.8478 7.06306C21 7.4306 21 7.83935 21 8.65685V15C21 17.8284 21 19.2426 20.1213 20.1213C19.48 20.7626 18.5534 20.9359 17 20.9827V18L17 17.9384C17.0001 17.2843 17.0001 16.6965 16.9362 16.2208C16.8663 15.7015 16.7042 15.1687 16.2678 14.7322C15.8313 14.2958 15.2985 14.1337 14.7792 14.0638C14.3034 13.9999 13.7157 13.9999 13.0616 14L13 14H10L9.93839 14C9.28427 13.9999 8.69655 13.9999 8.22084 14.0638C7.70149 14.1337 7.16867 14.2958 6.73223 14.7322C6.29579 15.1687 6.13366 15.7015 6.06383 16.2208C5.99988 16.6965 5.99993 17.2843 6 17.9384L6 18V20.9239C5.02491 20.828 4.36857 20.6112 3.87868 20.1213C3 19.2426 3 17.8284 3 15V9ZM15 18V21H9C8.64496 21 8.31221 21 8 20.9983V18C8 17.2646 8.00212 16.8137 8.046 16.4873C8.08457 16.2005 8.13942 16.1526 8.14592 16.1469L8.14645 16.1464L8.14692 16.1459C8.1526 16.1394 8.20049 16.0846 8.48734 16.046C8.81369 16.0021 9.26462 16 10 16H13C13.7354 16 14.1863 16.0021 14.5127 16.046C14.7995 16.0846 14.8474 16.1394 14.8531 16.1459L14.8536 16.1464L14.8541 16.1469C14.8606 16.1526 14.9154 16.2005 14.954 16.4873C14.9979 16.8137 15 17.2646 15 18ZM7 7C6.44772 7 6 7.44772 6 8C6 8.55228 6.44772 9 7 9H12C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7H7Z"
          fill="#757575"
        />
      </Svg>
    </View>
  );
};
export const iconFullScreen = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 122.879 100.426"
        enable-background="new 0 0 122.879 100.426"
      >
        <G>
          <Path
            d="M23.417,50.189V23.298h27.528v9.584H33.001v17.307H23.417L23.417,50.189z M14.279,0h94.323 c3.914,0,7.48,1.609,10.076,4.201l0.01-0.01c2.586,2.586,4.191,6.158,4.191,10.088v71.87c0,3.906-1.605,7.473-4.191,10.066 l-0.029,0.031c-2.596,2.58-6.154,4.18-10.057,4.18H14.279c-3.931,0-7.502-1.605-10.088-4.191c-0.108-0.107-0.209-0.219-0.305-0.334 C1.479,93.336,0,89.9,0,86.148v-71.87c0-3.932,1.605-7.503,4.19-10.088C6.776,1.605,10.346,0,14.279,0L14.279,0z M108.602,9.743 H14.279c-1.242,0-2.375,0.512-3.2,1.336c-0.824,0.825-1.336,1.958-1.336,3.2v71.87c0,1.164,0.436,2.225,1.149,3.02l0.187,0.178 c0.825,0.826,1.958,1.338,3.199,1.338h94.323c1.254,0,2.385-0.506,3.197-1.318l0.02-0.02c0.813-0.813,1.318-1.945,1.318-3.197 v-71.87c0-1.241-0.512-2.375-1.338-3.199l0.01-0.009l-0.01-0.01C110.988,10.248,109.855,9.743,108.602,9.743L108.602,9.743z M99.463,50.237v26.892H71.934v-9.584h17.945V50.237H99.463L99.463,50.237z"
            fill="#fff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeletePost = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
        fill="#346359"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconSettingApp = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M20.1 9.2214C18.29 9.2214 17.55 7.9414 18.45 6.3714C18.97 5.4614 18.66 4.3014 17.75 3.7814L16.02 2.7914C15.23 2.3214 14.21 2.6014 13.74 3.3914L13.63 3.5814C12.73 5.1514 11.25 5.1514 10.34 3.5814L10.23 3.3914C9.78 2.6014 8.76 2.3214 7.97 2.7914L6.24 3.7814C5.33 4.3014 5.02 5.4714 5.54 6.3814C6.45 7.9414 5.71 9.2214 3.9 9.2214C2.86 9.2214 2 10.0714 2 11.1214V12.8814C2 13.9214 2.85 14.7814 3.9 14.7814C5.71 14.7814 6.45 16.0614 5.54 17.6314C5.02 18.5414 5.33 19.7014 6.24 20.2214L7.97 21.2114C8.76 21.6814 9.78 21.4014 10.25 20.6114L10.36 20.4214C11.26 18.8514 12.74 18.8514 13.65 20.4214L13.76 20.6114C14.23 21.4014 15.25 21.6814 16.04 21.2114L17.77 20.2214C18.68 19.7014 18.99 18.5314 18.47 17.6314C17.56 16.0614 18.3 14.7814 20.11 14.7814C21.15 14.7814 22.01 13.9314 22.01 12.8814V11.1214C22 10.0814 21.15 9.2214 20.1 9.2214ZM12 15.2514C10.21 15.2514 8.75 13.7914 8.75 12.0014C8.75 10.2114 10.21 8.7514 12 8.7514C13.79 8.7514 15.25 10.2114 15.25 12.0014C15.25 13.7914 13.79 15.2514 12 15.2514Z"
          fill="#444444"
        />
      </Svg>
    </View>
  );
};
export const iconDeleteAreaPlant = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(14)}
        height={verticalScale(14)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#fff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconCloseSheet = () => {
  return (
    <View>
      <Svg
        width={scale(22)}
        height={verticalScale(22)}
        viewBox="0 0 35 37"
        fill="none"
      >
        <Path
          d="M19.5562 19.3043L25.827 12.6906C26.1016 12.4003 26.2559 12.0066 26.2559 11.596C26.2559 11.1855 26.1016 10.7917 25.827 10.5014C25.5524 10.2111 25.1799 10.048 24.7916 10.048C24.4032 10.048 24.0308 10.2111 23.7562 10.5014L17.4999 17.1306L11.2437 10.5014C10.9691 10.2111 10.5966 10.048 10.2082 10.048C9.81989 10.048 9.44744 10.2111 9.17283 10.5014C8.89822 10.7917 8.74394 11.1855 8.74394 11.596C8.74394 12.0066 8.89822 12.4003 9.17283 12.6906L15.4437 19.3043L9.17283 25.9181C9.03614 26.0614 8.92765 26.2319 8.85361 26.4198C8.77957 26.6077 8.74146 26.8092 8.74146 27.0127C8.74146 27.2162 8.77957 27.4177 8.85361 27.6056C8.92765 27.7934 9.03614 27.9639 9.17283 28.1073C9.3084 28.2518 9.46969 28.3664 9.6474 28.4447C9.82511 28.523 10.0157 28.5633 10.2082 28.5633C10.4008 28.5633 10.5914 28.523 10.7691 28.4447C10.9468 28.3664 11.1081 28.2518 11.2437 28.1073L17.4999 21.4781L23.7562 28.1073C23.8917 28.2518 24.053 28.3664 24.2307 28.4447C24.4084 28.523 24.5991 28.5633 24.7916 28.5633C24.9841 28.5633 25.1747 28.523 25.3524 28.4447C25.5301 28.3664 25.6914 28.2518 25.827 28.1073C25.9637 27.9639 26.0722 27.7934 26.1462 27.6056C26.2203 27.4177 26.2584 27.2162 26.2584 27.0127C26.2584 26.8092 26.2203 26.6077 26.1462 26.4198C26.0722 26.2319 25.9637 26.0614 25.827 25.9181L19.5562 19.3043Z"
          fill="#000000"
        />
      </Svg>
    </View>
  );
};
export const iconLikeSetting = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          d="M8.39062 18.4907V8.33071C8.39062 7.93071 8.51062 7.54071 8.73062 7.21071L11.4606 3.15071C11.8906 2.50071 12.9606 2.04071 13.8706 2.38071C14.8506 2.71071 15.5006 3.81071 15.2906 4.79071L14.7706 8.06071C14.7306 8.36071 14.8106 8.63071 14.9806 8.84071C15.1506 9.03071 15.4006 9.15071 15.6706 9.15071H19.7806C20.5706 9.15071 21.2506 9.47071 21.6506 10.0307C22.0306 10.5707 22.1006 11.2707 21.8506 11.9807L19.3906 19.4707C19.0806 20.7107 17.7306 21.7207 16.3906 21.7207H12.4906C11.8206 21.7207 10.8806 21.4907 10.4506 21.0607L9.17062 20.0707C8.68062 19.7007 8.39062 19.1107 8.39062 18.4907Z"
          fill="#444444"
        />
        <Path
          d="M5.21 6.37891H4.18C2.63 6.37891 2 6.97891 2 8.45891V18.5189C2 19.9989 2.63 20.5989 4.18 20.5989H5.21C6.76 20.5989 7.39 19.9989 7.39 18.5189V8.45891C7.39 6.97891 6.76 6.37891 5.21 6.37891Z"
          fill="#444444"
        />
      </Svg>
    </View>
  );
};
export const iconDeleteAlert = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill={"#FFFFFF"}
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconForwardPost = () => {
  return (
    <View>
      <Svg
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          stroke="#000000"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="m20 12-6.4-7v3.5C10.4 8.5 4 10.6 4 19c0-1.167 1.92-3.5 9.6-3.5V19l6.4-7z"
        />
      </Svg>
    </View>
  );
};
export const iconForwardPostBg = () => {
  return (
    <View>
      <Svg
        width={scale(18)}
        height={verticalScale(18)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill="#7FBDF6"
          stroke="#7FBDF6"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="m20 12-6.4-7v3.5C10.4 8.5 4 10.6 4 19c0-1.167 1.92-3.5 9.6-3.5V19l6.4-7z"
        />
      </Svg>
    </View>
  );
};
export const iconCommentArea = () => {
  return (
    <View>
      <Svg width={scale(15)} height={verticalScale(15)} viewBox="0 0 48 48">
        <G id="Layer_2" data-name="Layer 2">
          <G id="invisible_box" data-name="invisible box">
            <Rect width="48" height="48" fill="none" />
          </G>
          <G id="icons_Q2" data-name="icons Q2">
            <Path
              d="M42,4H6A2,2,0,0,0,4,6V42a2,2,0,0,0,2,2,2,2,0,0,0,1.4-.6L15.2,36H42a2,2,0,0,0,2-2V6A2,2,0,0,0,42,4ZM25.4,25.4A2,2,0,0,1,24,26H16a2,2,0,0,1-2-2,2.1,2.1,0,0,1,2-2h8a2,2,0,0,1,2,2A2,2,0,0,1,25.4,25.4Zm8-8A2,2,0,0,1,32,18H16a2,2,0,0,1-2-2,2.1,2.1,0,0,1,2-2H32a2,2,0,0,1,2,2A2,2,0,0,1,33.4,17.4Z"
              fill="#B3DBC0"
            />
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconSelectPlant = () => {
  return (
    <View>
      <Svg
        width={moderateScale(20)}
        height={moderateScale(20)}
        viewBox="0 0 24 24"
      >
        <G>
          <Path fill="none" d="M0 0H24V24H0z" />
          <Path
            d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z"
            fill="#ffffff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconBankingChang = () => {
  return (
    <View>
      <Svg
        fill="#ffff"
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="-1.5 0 19 19"
      >
        <Path d="M15.442 14.75v.491H.558v-.49a.476.476 0 0 1 .475-.476h.478a.487.487 0 0 1-.003-.048v-.443a.476.476 0 0 1 .475-.475h.713V7.164H1.508a.554.554 0 0 1-.22-1.063L7.78 3.288a.554.554 0 0 1 .44 0L14.712 6.1a.554.554 0 0 1-.22 1.063h-1.188v6.145h.713a.476.476 0 0 1 .475.475v.443a.443.443 0 0 1-.003.048h.478a.476.476 0 0 1 .475.475zM3.804 13.31h2.058V8.264H3.804zm.377-7.254h7.639L8 4.4zm2.79 2.21v5.043h2.058V8.265zm5.225 5.043V8.265h-2.059v5.044z" />
      </Svg>
    </View>
  );
};
export const iconGalleryModal = () => {
  return (
    <View>
      <Svg
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M15.75 2.625H5.25C4.55381 2.625 3.88613 2.90156 3.39384 3.39384C2.90156 3.88613 2.625 4.55381 2.625 5.25V15.75C2.625 16.4462 2.90156 17.1139 3.39384 17.6062C3.88613 18.0984 4.55381 18.375 5.25 18.375H15.75C16.4462 18.375 17.1139 18.0984 17.6062 17.6062C18.0984 17.1139 18.375 16.4462 18.375 15.75V5.25C18.375 4.55381 18.0984 3.88613 17.6062 3.39384C17.1139 2.90156 16.4462 2.625 15.75 2.625ZM5.25 4.375H15.75C15.9821 4.375 16.2046 4.46719 16.3687 4.63128C16.5328 4.79538 16.625 5.01794 16.625 5.25V12.565L13.825 10.1763C13.3912 9.81929 12.8468 9.62412 12.285 9.62412C11.7232 9.62412 11.1788 9.81929 10.745 10.1763L4.375 15.4875V5.25C4.375 5.01794 4.46719 4.79538 4.63128 4.63128C4.79538 4.46719 5.01794 4.375 5.25 4.375Z"
          fill="#ffffff"
        />
        <Path
          d="M7 8.75C7.72487 8.75 8.3125 8.16237 8.3125 7.4375C8.3125 6.71263 7.72487 6.125 7 6.125C6.27513 6.125 5.6875 6.71263 5.6875 7.4375C5.6875 8.16237 6.27513 8.75 7 8.75Z"
          fill="#ffffff"
        />
      </Svg>
    </View>
  );
};
export const iconLocationMarker = () => {
  return (
    <View>
      <Svg
        fill="#fff"
        id="Capa_1"
        width="40px"
        height="40px"
        viewBox="0 0 45.822 45.822"
      >
        <G>
          <G>
            <Path d="M42.316,19.386h-1.639c-1.418-7.163-7.074-12.819-14.236-14.24v-1.64C26.441,1.569,24.85,0,22.915,0    c-1.938,0-3.527,1.569-3.527,3.506v1.639C12.224,6.563,6.564,12.221,5.146,19.386h-1.64C1.571,19.386,0,20.976,0,22.913    c0,1.937,1.571,3.526,3.506,3.526h1.641c1.42,7.164,7.078,12.82,14.241,14.238v1.639c0,1.937,1.59,3.506,3.527,3.506    c1.935,0,3.526-1.57,3.526-3.506v-1.64c7.16-1.42,12.816-7.075,14.236-14.237h1.639c1.938,0,3.506-1.59,3.506-3.526    C45.822,20.976,44.254,19.386,42.316,19.386z M22.911,36.016c-7.238,0-13.104-5.867-13.104-13.104S15.673,9.807,22.911,9.807    c7.237,0,13.104,5.867,13.104,13.104S30.148,36.016,22.911,36.016z" />
            <Circle cx="22.911" cy="22.911" r="3.5" />{" "}
            {/* ปรับ r จาก 5.092 เป็น 3.5 */}
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconButtomMarker = () => {
  return (
    <View>
      <Svg
        fill="#fff"
        id="Capa_1"
        width={scale(24)}
        height={verticalScale(24)}
        viewBox="0 0 45.822 45.822"
      >
        <G>
          <G>
            <Path d="M42.316,19.386h-1.639c-1.418-7.163-7.074-12.819-14.236-14.24v-1.64C26.441,1.569,24.85,0,22.915,0    c-1.938,0-3.527,1.569-3.527,3.506v1.639C12.224,6.563,6.564,12.221,5.146,19.386h-1.64C1.571,19.386,0,20.976,0,22.913    c0,1.937,1.571,3.526,3.506,3.526h1.641c1.42,7.164,7.078,12.82,14.241,14.238v1.639c0,1.937,1.59,3.506,3.527,3.506    c1.935,0,3.526-1.57,3.526-3.506v-1.64c7.16-1.42,12.816-7.075,14.236-14.237h1.639c1.938,0,3.506-1.59,3.506-3.526    C45.822,20.976,44.254,19.386,42.316,19.386z M22.911,36.016c-7.238,0-13.104-5.867-13.104-13.104S15.673,9.807,22.911,9.807    c7.237,0,13.104,5.867,13.104,13.104S30.148,36.016,22.911,36.016z" />
            <Circle cx="22.911" cy="22.911" r="5.092" />
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconShare = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 32 32"
        enable-background="new 0 0 32 32"
        fill="#000000"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />
        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <G id="SVGRepo_iconCarrier">
          <G id="Layer_2" /> <G id="Layer_3" /> <G id="Layer_4" />
          <G id="Layer_5" /> <G id="Layer_6" /> <G id="Layer_7" />
          <G id="Layer_8" /> <G id="Layer_9" /> <G id="Layer_10" />
          <G id="Layer_11" /> <G id="Layer_12" /> <G id="Layer_13" />
          <G id="Layer_14" /> <G id="Layer_15" /> <G id="Layer_16" />
          <G id="Layer_17" /> <G id="Layer_18" /> <G id="Layer_19" />
          <G id="Maps_11_" /> <G id="Maps_10_" /> <G id="Maps_9_" />
          <G id="Maps_8_" /> <G id="Maps_7_" /> <G id="Maps_6_" />
          <G id="Maps_5_" /> <G id="Maps_4_" /> <G id="Maps_3_" />
          <G id="Maps_2_">
            <Path
              d="M16,1C10.49,1,6,5.5,6,11.04c0,5.25,8.34,13.72,9.29,14.67C15.49,25.9,15.74,26,16,26s0.51-0.1,0.71-0.29 C17.66,24.76,26,16.29,26,11.04C26,5.5,21.51,1,16,1z"
              fill="#E74C3C"
            />
            <Path
              d="M20,11.04c0,2.2-1.79,4-4,4s-4-1.8-4-4c0-2.21,1.79-4,4-4S20,8.83,20,11.04z"
              fill="#fff"
            />
            <G>
              <Path
                d="M30,31H2c-0.5522,0-1-0.4478-1-1V20c0-0.5522,0.4478-1,1-1h4.5898c0.5522,0,1,0.4478,1,1s-0.4478,1-1,1H3 v8h26v-8h-3.5898c-0.5527,0-1-0.4478-1-1s0.4473-1,1-1H30c0.5527,0,1,0.4478,1,1v10C31,30.5522,30.5527,31,30,31z"
                fill="#3498db"
              />
            </G>
          </G>
          <G id="Maps_1_" /> <G id="Maps" />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeleteNoti = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(14)}
        height={verticalScale(14)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#fff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconCheckNoti = () => {
  return (
    <View>
      <Svg
        width={scale(24)}
        height={verticalScale(24)}
        viewBox="0 -1.5 20.412 20.412"
        fill="#ffffff"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <G id="check-lists" transform="translate(-1.588 -2.588)">
            <Path
              id="primary"
              d="M7,4,4.33,7,3,5.5"
              fill="none"
              stroke="#ffffff"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
            <Path
              id="primary-2"
              data-name="primary"
              d="M3,11.5,4.33,13,7,10"
              fill="none"
              stroke="#ffffff"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
            <Path
              id="primary-3"
              data-name="primary"
              d="M3,17.5,4.33,19,7,16"
              fill="none"
              stroke="#ffffff"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
            <Path
              id="primary-4"
              data-name="primary"
              d="M11,6H21M11,12H21M11,18H21"
              fill="none"
              stroke="#ffffff"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconCheckRead = () => {
  return (
    <View>
      <Svg width={scale(24)} height={verticalScale(24)} viewBox="0 0 48 48">
        <Circle cx="24" cy="24" r="21" fill="#B3DBC0" />
        <Path
          d="M21.39,31A2,2,0,0,1,20,30.4l-5.39-5.53a2,2,0,0,1,2.87-2.79l4,4.12,9.18-8.65a2,2,0,0,1,2.74,2.91l-10.61,10A2,2,0,0,1,21.39,31Z"
          fill="#ffffff"
        />
      </Svg>
    </View>
  );
};
export const iconUpNoti = () => {
  return (
    <View>
      <Svg
        width={scale(24)}
        height={verticalScale(24)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12 7C12.2652 7 12.5196 7.10536 12.7071 7.29289L19.7071 14.2929C20.0976 14.6834 20.0976 15.3166 19.7071 15.7071C19.3166 16.0976 18.6834 16.0976 18.2929 15.7071L12 9.41421L5.70711 15.7071C5.31658 16.0976 4.68342 16.0976 4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929L11.2929 7.29289C11.4804 7.10536 11.7348 7 12 7Z"
            fill="#000000"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDownNoti = () => {
  return (
    <View>
      <Svg
        width={scale(24)}
        height={verticalScale(24)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.29289 8.29289C4.68342 7.90237 5.31658 7.90237 5.70711 8.29289L12 14.5858L18.2929 8.29289C18.6834 7.90237 19.3166 7.90237 19.7071 8.29289C20.0976 8.68342 20.0976 9.31658 19.7071 9.70711L12.7071 16.7071C12.3166 17.0976 11.6834 17.0976 11.2929 16.7071L4.29289 9.70711C3.90237 9.31658 3.90237 8.68342 4.29289 8.29289Z"
          fill="#000000"
        />
      </Svg>
    </View>
  );
};
export const iconDownPayment = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.29289 8.29289C4.68342 7.90237 5.31658 7.90237 5.70711 8.29289L12 14.5858L18.2929 8.29289C18.6834 7.90237 19.3166 7.90237 19.7071 8.29289C20.0976 8.68342 20.0976 9.31658 19.7071 9.70711L12.7071 16.7071C12.3166 17.0976 11.6834 17.0976 11.2929 16.7071L4.29289 9.70711C3.90237 9.31658 3.90237 8.68342 4.29289 8.29289Z"
          fill="#ffff"
        />
      </Svg>
    </View>
  );
};
export const iconUpPayment = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12 7C12.2652 7 12.5196 7.10536 12.7071 7.29289L19.7071 14.2929C20.0976 14.6834 20.0976 15.3166 19.7071 15.7071C19.3166 16.0976 18.6834 16.0976 18.2929 15.7071L12 9.41421L5.70711 15.7071C5.31658 16.0976 4.68342 16.0976 4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929L11.2929 7.29289C11.4804 7.10536 11.7348 7 12 7Z"
            fill="#ffff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconUpProduct = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12 7C12.2652 7 12.5196 7.10536 12.7071 7.29289L19.7071 14.2929C20.0976 14.6834 20.0976 15.3166 19.7071 15.7071C19.3166 16.0976 18.6834 16.0976 18.2929 15.7071L12 9.41421L5.70711 15.7071C5.31658 16.0976 4.68342 16.0976 4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929L11.2929 7.29289C11.4804 7.10536 11.7348 7 12 7Z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDownProduct = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.29289 8.29289C4.68342 7.90237 5.31658 7.90237 5.70711 8.29289L12 14.5858L18.2929 8.29289C18.6834 7.90237 19.3166 7.90237 19.7071 8.29289C20.0976 8.68342 20.0976 9.31658 19.7071 9.70711L12.7071 16.7071C12.3166 17.0976 11.6834 17.0976 11.2929 16.7071L4.29289 9.70711C3.90237 9.31658 3.90237 8.68342 4.29289 8.29289Z"
          fill="#84B8A2"
        />
      </Svg>
    </View>
  );
};
export const iconStartEnd = () => {
  return (
    <View>
      <Svg
        fill="#679290"
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 24 24"
      >
        <Path d="M16.707,18.707a1,1,0,0,1-1.414-1.414L19.586,13H2a1,1,0,0,1,0-2H19.586L15.293,6.707a1,1,0,0,1,1.414-1.414l6,6a1,1,0,0,1,0,1.414Z" />
      </Svg>
    </View>
  );
};
export const iconEdit = () => {
  return (
    <View>
      <Svg
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 21 21"
        fill="none"
      >
        <Path
          d="M19.6952 21H0.787808C0.35714 21 0 20.6429 0 20.2122C0 19.7815 0.35714 19.4244 0.787808 19.4244H19.6952C20.1259 19.4244 20.483 19.7815 20.483 20.2122C20.483 20.6429 20.1259 21 19.6952 21Z"
          fill="#FF8585"
        />
        <Path
          d="M17.6156 1.5481C15.5778 -0.489691 13.5821 -0.542212 11.4917 1.5481L10.2208 2.8191C10.1157 2.92414 10.0737 3.09221 10.1157 3.23927C10.914 6.02285 13.1409 8.24972 15.9245 9.04801C15.9665 9.05852 16.0085 9.06902 16.0505 9.06902C16.1661 9.06902 16.2711 9.02701 16.3551 8.94297L17.6156 7.672C18.6555 6.64259 19.1597 5.64471 19.1597 4.63631C19.1702 3.5964 18.666 2.58801 17.6156 1.5481Z"
          fill="#FF8585"
        />
        <Path
          d="M14.0336 10.003C13.729 9.85599 13.4348 9.70893 13.1512 9.54087C12.9201 9.40431 12.6996 9.25726 12.479 9.09969C12.3004 8.98415 12.0903 8.81608 11.8907 8.64802C11.8697 8.63751 11.7962 8.57449 11.7122 8.49046C11.3655 8.19632 10.9769 7.81817 10.6302 7.39801C10.5987 7.377 10.5462 7.30347 10.4727 7.20893C10.3676 7.08289 10.1891 6.8728 10.0315 6.63121C9.90547 6.47365 9.75841 6.24256 9.62186 6.01147C9.45379 5.72786 9.30673 5.44425 9.15967 5.15013C9.13741 5.10245 9.11587 5.05501 9.09507 5.00784C8.94003 4.65772 8.48342 4.55536 8.21273 4.82611L2.19548 10.8434C2.05892 10.9799 1.93287 11.2425 1.90136 11.4211L1.33414 15.4442C1.2291 16.1585 1.42868 16.8307 1.86985 17.2824C2.248 17.65 2.7732 17.8496 3.34042 17.8496C3.46647 17.8496 3.59252 17.8391 3.71857 17.8181L7.75215 17.2509C7.94122 17.2194 8.2038 17.0933 8.32985 16.9568L14.3396 10.947C14.6123 10.6744 14.5094 10.2065 14.1548 10.0551C14.1149 10.0381 14.0744 10.0207 14.0336 10.003Z"
          fill="#FF8585"
        />
      </Svg>
    </View>
  );
};
export const iconPlant = () => {
  return (
    <View>
      <Svg
        width={scale(25)}
        height={verticalScale(25)}
        viewBox="0 0 24 24"
        fill="#ffffff"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />
        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <G id="SVGRepo_iconCarrier">
          <G>
            <Path fill="none" d="M0 0H24V24H0z" />
            <Path d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z" />
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconNoImg = () => {
  return (
    <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 24 24">
      <G>
        <Path fill="none" d="M0 0H24V24H0z" />
        <Path
          d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z"
          fill="#D6D6D6"
        />
      </G>
    </Svg>
  );
};
export const iconMarket1 = () => {
  return (
    <Svg
      fill="#fff"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 31.603 31.603"
    >
      <G id="SVGRepo_bgCarrier" stroke-width="0" />
      <G
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <G id="SVGRepo_iconCarrier">
        <G>
          <G>
            <Path d="M7.703,15.973c0,0,5.651-5.625,5.651-10.321C13.354,2.53,10.824,0,7.703,0S2.052,2.53,2.052,5.652 C2.052,10.614,7.703,15.973,7.703,15.973z M4.758,5.652c0-1.628,1.319-2.946,2.945-2.946s2.945,1.318,2.945,2.946 c0,1.626-1.319,2.944-2.945,2.944S4.758,7.278,4.758,5.652z" />{" "}
            <Path d="M28.59,7.643l-0.459,0.146l-2.455,0.219l-0.692,1.106l-0.501-0.16l-1.953-1.76l-0.285-0.915l-0.377-0.977L20.639,4.2 l-1.446-0.283L19.159,4.58l1.418,1.384l0.694,0.817l-0.782,0.408l-0.636-0.188l-0.951-0.396l0.033-0.769l-1.25-0.514L17.27,7.126 l-1.258,0.286l0.125,1.007l1.638,0.316l0.284-1.609l1.353,0.201l0.629,0.368h1.011l0.69,1.384l1.833,1.859l-0.134,0.723 l-1.478-0.189l-2.553,1.289l-1.838,2.205l-0.239,0.976h-0.661l-1.229-0.566l-1.194,0.566l0.297,1.261l0.52-0.602l0.913-0.027 l-0.064,1.132l0.757,0.22l0.756,0.85l1.234-0.347l1.41,0.222l1.636,0.441l0.819,0.095l1.384,1.573l2.675,1.574l-1.729,3.306 l-1.826,0.849l-0.693,1.889l-2.643,1.765l-0.282,1.019c6.753-1.627,11.779-7.693,11.779-14.95 C31.194,13.038,30.234,10.09,28.59,7.643z" />{" "}
            <Path d="M17.573,24.253l-1.12-2.078l1.028-2.146l-1.028-0.311l-1.156-1.159l-2.56-0.573l-0.85-1.779v1.057h-0.375l-1.625-2.203 c-0.793,0.949-1.395,1.555-1.47,1.629L7.72,17.384l-0.713-0.677c-0.183-0.176-3.458-3.315-5.077-7.13 c-0.966,2.009-1.52,4.252-1.52,6.63c0,8.502,6.891,15.396,15.393,15.396c0.654,0,1.296-0.057,1.931-0.135l-0.161-1.864 c0,0,0.707-2.77,0.707-2.863C18.28,26.646,17.573,24.253,17.573,24.253z" />{" "}
            <Path d="M14.586,3.768l1.133,0.187l2.75-0.258l0.756-0.834l1.068-0.714l1.512,0.228l0.551-0.083 c-1.991-0.937-4.207-1.479-6.553-1.479c-1.096,0-2.16,0.128-3.191,0.345c0.801,0.875,1.377,1.958,1.622,3.163L14.586,3.768z M16.453,2.343l1.573-0.865l1.009,0.582l-1.462,1.113l-1.394,0.141L15.55,2.907L16.453,2.343z" />{" "}
          </G>
        </G>
      </G>
    </Svg>
  );
};
export const iconMarket2 = () => {
  return (
    <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 24 24">
      <G>
        <Path fill="none" d="M0 0H24V24H0z" />
        <Path
          d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z"
          fill="#fff"
        />
      </G>
    </Svg>
  );
};
export const iconMaeket3 = () => {
  return (
    <View>
      <Svg
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="0 0 31 25"
        fill="#fff"
      >
        <Path
          d="M30.2402 6.05937L27.0791 1.20312C26.7966 0.767187 26.2949 0.5 25.7591 0.5H6.08148C5.5457 0.5 5.04401 0.767187 4.76151 1.20312L1.60042 6.05937C-0.0312656 8.56719 1.41534 12.0547 4.4644 12.4531C4.68358 12.4812 4.90764 12.4953 5.13169 12.4953C6.57342 12.4953 7.84954 11.8859 8.72627 10.9438C9.603 11.8859 10.884 12.4953 12.3209 12.4953C13.7626 12.4953 15.0387 11.8859 15.9154 10.9438C16.7922 11.8859 18.0732 12.4953 19.51 12.4953C20.9518 12.4953 22.2279 11.8859 23.1046 10.9438C23.9862 11.8859 25.2623 12.4953 26.6992 12.4953C26.9281 12.4953 27.1473 12.4812 27.3665 12.4531C30.4253 12.0594 31.8768 8.57187 30.2402 6.05937ZM26.7089 14C26.2219 14 25.7397 13.9297 25.2721 13.8219V18.5H6.56855V13.8219C6.10096 13.925 5.61876 14 5.13169 14C4.83945 14 4.54233 13.9812 4.25496 13.9437C3.9822 13.9062 3.71431 13.8453 3.45616 13.775V23C3.45616 23.8297 4.15267 24.5 5.01479 24.5H26.8356C27.6977 24.5 28.3942 23.8297 28.3942 23V13.775C28.1312 13.85 27.8682 13.9109 27.5954 13.9437C27.2983 13.9812 27.0061 14 26.7089 14Z"
          fill="white"
        />
      </Svg>
    </View>
  );
};
export const iconNoImgProduct = () => {
  return (
    <Svg viewBox="0 0 24 24">
      <G>
        <Path fill="none" d="M0 0H24V24H0z" />
        <Path
          d="M21 3v2c0 3.866-3.134 7-7 7h-1v1h5v7c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2v-7h5v-3c0-3.866 3.134-7 7-7h3zM5.5 2c2.529 0 4.765 1.251 6.124 3.169C10.604 6.51 10 8.185 10 10v1h-.5C5.358 11 2 7.642 2 3.5V2h3.5z"
          fill="#D6D6D6"
        />
      </G>
    </Svg>
  );
};
export const iconDeleteFarm = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#fff"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconCheckArea = () => {
  return (
    <View>
      <Svg
        fill="#ffffff"
        width={scale(25)}
        height={verticalScale(25)}
        viewBox="0 0 1024 1024"
        stroke="#ffffff"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path d="M351.605 663.268l481.761-481.761c28.677-28.677 75.171-28.677 103.847 0s28.677 75.171 0 103.847L455.452 767.115l.539.539-58.592 58.592c-24.994 24.994-65.516 24.994-90.51 0L85.507 604.864c-28.677-28.677-28.677-75.171 0-103.847s75.171-28.677 103.847 0l162.25 162.25z" />
        </G>
      </Svg>
    </View>
  );
};
export const iconScreenPlus = () => {
  return (
    <View>
      <Svg
        width={moderateScale(20)}
        height={moderateScale(20)}
        viewBox="0 0 32 32"
        fill="#000000"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <G
            id="Page-1"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
            // sketch:type="MSPage"
          >
            <G
              id="Icon-Set-Filled"
              // sketch:type="MSLayerGroup"
              transform="translate(-362.000000, -1037.000000)"
              fill="#ffffff"
            >
              <Path
                d="M390,1049 L382,1049 L382,1041 C382,1038.79 380.209,1037 378,1037 C375.791,1037 374,1038.79 374,1041 L374,1049 L366,1049 C363.791,1049 362,1050.79 362,1053 C362,1055.21 363.791,1057 366,1057 L374,1057 L374,1065 C374,1067.21 375.791,1069 378,1069 C380.209,1069 382,1067.21 382,1065 L382,1057 L390,1057 C392.209,1057 394,1055.21 394,1053 C394,1050.79 392.209,1049 390,1049"
                id="plus"
                // sketch:type="MSShapeGroup"
              ></Path>
            </G>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconScreenClose = () => {
  return (
    <View>
      <Svg
        width={moderateScale(25)}
        height={moderateScale(25)}
        viewBox="0 0 1024 1024"
        // xmlns="http://www.w3.org/2000/svg"
        fill="#000000"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill="#ffffff"
            d="M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496z"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeleteOrange = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill="#FF9900"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconDeletePayment = () => {
  return (
    <View>
      <Svg
        id="Layer_1"
        x="0px"
        y="0px"
        width={scale(15)}
        height={verticalScale(15)}
        viewBox="0 0 109.484 122.88"
        enable-background="new 0 0 109.484 122.88"
      >
        <G>
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
            fill={"#FF9934"}
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconReductNumber = () => {
  return (
    <View>
      <Svg
        width={scale(12)}
        height={verticalScale(12)}
        viewBox="0 -4 12 12"
        id="meteor-icon-kit__solid-minus-xs"
        fill="none"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M1.5 3.5H10.5C11.3284 3.5 12 2.8284 12 2C12 1.1716 11.3284 0.5 10.5 0.5H1.5C0.67157 0.5 0 1.1716 0 2C0 2.8284 0.67157 3.5 1.5 3.5z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconPlusNumber = () => {
  return (
    <View>
      <Svg
        width={scale(12)}
        height={verticalScale(12)}
        viewBox="0 0 16 16"
        id="meteor-icon-kit__solid-plus-s"
        fill="none"
      >
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.5 6.5V2C6.5 1.17157 7.1716 0.5 8 0.5C8.8284 0.5 9.5 1.17157 9.5 2V6.5H14C14.8284 6.5 15.5 7.1716 15.5 8C15.5 8.8284 14.8284 9.5 14 9.5H9.5V14C9.5 14.8284 8.8284 15.5 8 15.5C7.1716 15.5 6.5 14.8284 6.5 14V9.5H2C1.17157 9.5 0.5 8.8284 0.5 8C0.5 7.1716 1.17157 6.5 2 6.5H6.5z"
          fill="#84B8A2"
        />
      </Svg>
    </View>
  );
};
export const iconPlusAddress = () => {
  return (
    <Svg
      width={scale(24)}
      height={verticalScale(24)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        d="M12 7V17M7 12H17M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
        stroke="#84B8A2"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </Svg>
  );
};
export const iconEditAddress = () => {
  return (
    <Svg
      width={scale(13)}
      height={verticalScale(13)}
      viewBox="0 0 21 21"
      fill="none"
    >
      <Path
        d="M19.6952 21H0.787808C0.35714 21 0 20.6429 0 20.2122C0 19.7815 0.35714 19.4244 0.787808 19.4244H19.6952C20.1259 19.4244 20.483 19.7815 20.483 20.2122C20.483 20.6429 20.1259 21 19.6952 21Z"
        fill="#A6A6A6"
      />
      <Path
        d="M17.6156 1.5481C15.5778 -0.489691 13.5821 -0.542212 11.4917 1.5481L10.2208 2.8191C10.1157 2.92414 10.0737 3.09221 10.1157 3.23927C10.914 6.02285 13.1409 8.24972 15.9245 9.04801C15.9665 9.05852 16.0085 9.06902 16.0505 9.06902C16.1661 9.06902 16.2711 9.02701 16.3551 8.94297L17.6156 7.672C18.6555 6.64259 19.1597 5.64471 19.1597 4.63631C19.1702 3.5964 18.666 2.58801 17.6156 1.5481Z"
        fill="#A6A6A6"
      />
      <Path
        d="M14.0336 10.003C13.729 9.85599 13.4348 9.70893 13.1512 9.54087C12.9201 9.40431 12.6996 9.25726 12.479 9.09969C12.3004 8.98415 12.0903 8.81608 11.8907 8.64802C11.8697 8.63751 11.7962 8.57449 11.7122 8.49046C11.3655 8.19632 10.9769 7.81817 10.6302 7.39801C10.5987 7.377 10.5462 7.30347 10.4727 7.20893C10.3676 7.08289 10.1891 6.8728 10.0315 6.63121C9.90547 6.47365 9.75841 6.24256 9.62186 6.01147C9.45379 5.72786 9.30673 5.44425 9.15967 5.15013C9.13741 5.10245 9.11587 5.05501 9.09507 5.00784C8.94003 4.65772 8.48342 4.55536 8.21273 4.82611L2.19548 10.8434C2.05892 10.9799 1.93287 11.2425 1.90136 11.4211L1.33414 15.4442C1.2291 16.1585 1.42868 16.8307 1.86985 17.2824C2.248 17.65 2.7732 17.8496 3.34042 17.8496C3.46647 17.8496 3.59252 17.8391 3.71857 17.8181L7.75215 17.2509C7.94122 17.2194 8.2038 17.0933 8.32985 16.9568L14.3396 10.947C14.6123 10.6744 14.5094 10.2065 14.1548 10.0551C14.1149 10.0381 14.0744 10.0207 14.0336 10.003Z"
        fill="#A6A6A6"
      />
    </Svg>
  );
};
export const iconDeleteAddress = () => {
  return (
    <Svg
      id="Layer_1"
      x="0px"
      y="0px"
      width={scale(13)}
      height={verticalScale(13)}
      viewBox="0 0 109.484 122.88"
      enable-background="new 0 0 109.484 122.88"
    >
      <G>
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M2.347,9.633h38.297V3.76c0-2.068,1.689-3.76,3.76-3.76h21.144 c2.07,0,3.76,1.691,3.76,3.76v5.874h37.83c1.293,0,2.347,1.057,2.347,2.349v11.514H0V11.982C0,10.69,1.055,9.633,2.347,9.633 L2.347,9.633z M8.69,29.605h92.921c1.937,0,3.696,1.599,3.521,3.524l-7.864,86.229c-0.174,1.926-1.59,3.521-3.523,3.521h-77.3 c-1.934,0-3.352-1.592-3.524-3.521L5.166,33.129C4.994,31.197,6.751,29.605,8.69,29.605L8.69,29.605z M69.077,42.998h9.866v65.314 h-9.866V42.998L69.077,42.998z M30.072,42.998h9.867v65.314h-9.867V42.998L30.072,42.998z M49.572,42.998h9.869v65.314h-9.869 V42.998L49.572,42.998z"
          fill="#A6A6A6"
        />
      </G>
    </Svg>
  );
};
export const iconEditFarmName = () => {
  return (
    <Svg
      width={scale(24)}
      height={verticalScale(24)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM10.95 17.51C10.66 17.8 10.11 18.08 9.71 18.14L7.25 18.49C7.16 18.5 7.07 18.51 6.98 18.51C6.57 18.51 6.19 18.37 5.92 18.1C5.59 17.77 5.45 17.29 5.53 16.76L5.88 14.3C5.94 13.89 6.21 13.35 6.51 13.06L10.97 8.6C11.05 8.81 11.13 9.02 11.24 9.26C11.34 9.47 11.45 9.69 11.57 9.89C11.67 10.06 11.78 10.22 11.87 10.34C11.98 10.51 12.11 10.67 12.19 10.76C12.24 10.83 12.28 10.88 12.3 10.9C12.55 11.2 12.84 11.48 13.09 11.69C13.16 11.76 13.2 11.8 13.22 11.81C13.37 11.93 13.52 12.05 13.65 12.14C13.81 12.26 13.97 12.37 14.14 12.46C14.34 12.58 14.56 12.69 14.78 12.8C15.01 12.9 15.22 12.99 15.43 13.06L10.95 17.51ZM17.37 11.09L16.45 12.02C16.39 12.08 16.31 12.11 16.23 12.11C16.2 12.11 16.16 12.11 16.14 12.1C14.11 11.52 12.49 9.9 11.91 7.87C11.88 7.76 11.91 7.64 11.99 7.57L12.92 6.64C14.44 5.12 15.89 5.15 17.38 6.64C18.14 7.4 18.51 8.13 18.51 8.89C18.5 9.61 18.13 10.33 17.37 11.09Z"
        fill="#fff"
      />
    </Svg>
  );
};
export const iconCancleFarmName = () => {
  return (
    <Svg
      width={scale(24)}
      height={verticalScale(24)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM15.36 14.3C15.65 14.59 15.65 15.07 15.36 15.36C15.21 15.51 15.02 15.58 14.83 15.58C14.64 15.58 14.45 15.51 14.3 15.36L12 13.06L9.7 15.36C9.55 15.51 9.36 15.58 9.17 15.58C8.98 15.58 8.79 15.51 8.64 15.36C8.35 15.07 8.35 14.59 8.64 14.3L10.94 12L8.64 9.7C8.35 9.41 8.35 8.93 8.64 8.64C8.93 8.35 9.41 8.35 9.7 8.64L12 10.94L14.3 8.64C14.59 8.35 15.07 8.35 15.36 8.64C15.65 8.93 15.65 9.41 15.36 9.7L13.06 12L15.36 14.3Z"
        fill="#ffff"
      />
    </Svg>
  );
};
export const iconSaveFarmName = () => {
  return (
    <Svg
      width={scale(24)}
      height={verticalScale(24)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7 2C4.23858 2 2 4.23858 2 7V17C2 19.7614 4.23858 22 7 22H17C19.7614 22 22 19.7614 22 17V7C22 4.23858 19.7614 2 17 2H7ZM16.6402 8.2318C17.0645 8.58537 17.1218 9.21593 16.7682 9.64021L11.7682 15.6402C11.5937 15.8497 11.3411 15.9788 11.0691 15.9976C10.797 16.0165 10.5291 15.9234 10.3273 15.74L7.32733 13.0127C6.91868 12.6412 6.88856 12.0087 7.26007 11.6001C7.63157 11.1914 8.26402 11.1613 8.67268 11.5328L10.9002 13.5578L15.2318 8.35984C15.5854 7.93556 16.2159 7.87824 16.6402 8.2318Z"
        fill="#ffff"
      />
    </Svg>
  );
};
export const iconApprove = () => {
  return (
    <Svg
      width={scale(24)}
      height={verticalScale(24)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        d="M9 17H15M9 13H15M9 9H10M13 3H8.2C7.0799 3 6.51984 3 6.09202 3.21799C5.71569 3.40973 5.40973 3.71569 5.21799 4.09202C5 4.51984 5 5.0799 5 6.2V17.8C5 18.9201 5 19.4802 5.21799 19.908C5.40973 20.2843 5.71569 20.5903 6.09202 20.782C6.51984 21 7.0799 21 8.2 21H15.8C16.9201 21 17.4802 21 17.908 20.782C18.2843 20.5903 18.5903 20.2843 18.782 19.908C19 19.4802 19 18.9201 19 17.8V9M13 3L19 9M13 3V7.4C13 7.96005 13 8.24008 13.109 8.45399C13.2049 8.64215 13.3578 8.79513 13.546 8.89101C13.7599 9 14.0399 9 14.6 9H19"
        stroke="#fff"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </Svg>
  );
};
export const iconMobileSystem = () => {
  return (
    <Svg
      width={scale(18)}
      height={verticalScale(18)}
      viewBox="0 0 24 24"
      fill="none"
    >
      <Path
        d="M16.24 2H7.76C5 2 4 3 4 5.81V18.19C4 21 5 22 7.76 22H16.23C19 22 20 21 20 18.19V5.81C20 3 19 2 16.24 2ZM12 19.3C11.04 19.3 10.25 18.51 10.25 17.55C10.25 16.59 11.04 15.8 12 15.8C12.96 15.8 13.75 16.59 13.75 17.55C13.75 18.51 12.96 19.3 12 19.3ZM14 6.25H10C9.59 6.25 9.25 5.91 9.25 5.5C9.25 5.09 9.59 4.75 10 4.75H14C14.41 4.75 14.75 5.09 14.75 5.5C14.75 5.91 14.41 6.25 14 6.25Z"
        fill="#444444"
      />
    </Svg>
  );
};
export const iconUpCCTV = () => {
  return (
    <Svg
      fill="#679290"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 30.021 30.021"
    >
      <G>
        <Path d="M29.069,22.276c-0.791,0.932-1.917,1.409-3.052,1.409c-0.913,0-1.834-0.312-2.587-0.949l-8.42-7.152l-8.42,7.151   c-1.683,1.43-4.208,1.225-5.639-0.459c-1.43-1.686-1.224-4.208,0.46-5.64l11.01-9.351c1.493-1.269,3.686-1.269,5.178,0   l11.011,9.351C30.294,18.068,30.499,20.591,29.069,22.276z" />
      </G>
    </Svg>
  );
};
export const iconRightCCTV = () => {
  return (
    <Svg
      fill="#679290"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 30.02 30.02"
      transform="rotate(180)"
    >
      <G id="SVGRepo_bgCarrier" stroke-width="0" />
      <G
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path d="M22.735,23.43c1.43,1.686,1.224,4.209-0.46,5.64c-0.753,0.641-1.674,0.95-2.587,0.95c-1.136,0-2.261-0.479-3.052-1.41 L7.286,17.6c-1.269-1.493-1.269-3.688,0-5.179l9.351-11.01c1.431-1.684,3.953-1.889,5.639-0.459c1.684,1.43,1.89,3.954,0.46,5.638 l-7.152,8.42L22.735,23.43z" />{" "}
    </Svg>
  );
};
export const iconLeftCCTV = () => {
  return (
    <Svg
      fill="#679290"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 30.02 30.02"
    >
      <G>
        <Path d="M22.735,23.43c1.43,1.686,1.224,4.209-0.46,5.64c-0.753,0.641-1.674,0.95-2.587,0.95c-1.136,0-2.261-0.479-3.052-1.41   L7.286,17.6c-1.269-1.493-1.269-3.688,0-5.179l9.351-11.01c1.431-1.684,3.953-1.889,5.639-0.459c1.684,1.43,1.89,3.954,0.46,5.638   l-7.152,8.42L22.735,23.43z" />
      </G>
    </Svg>
  );
};
export const iconDowntCCTV = () => {
  return (
    <Svg
      fill="#679290"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 30.021 30.021"
    >
      <G>
        <Path d="M28.611,13.385l-11.011,9.352c-0.745,0.633-1.667,0.949-2.589,0.949c-0.921,0-1.842-0.316-2.589-0.949L1.411,13.385   c-1.684-1.43-1.89-3.954-0.46-5.638c1.431-1.684,3.955-1.89,5.639-0.459l8.421,7.151l8.42-7.151   c1.686-1.43,4.209-1.224,5.639,0.459C30.5,9.431,30.294,11.955,28.611,13.385z" />
      </G>
    </Svg>
  );
};
export const iconResetCCTV = () => {
  return (
    <Svg
      fill="#679290"
      id="Capa_1"
      width={scale(20)}
      height={verticalScale(20)}
      viewBox="0 0 40.5 40.5"
    >
      <G>
        <Path d="M40.5,20.25c0,9.374-7.625,17-17,17c-1.656,0-3-1.343-3-3s1.344-3,3-3c6.064,0,11-4.936,11-11c0-6.065-4.936-11-11-11   c-5.756,0-10.486,4.447-10.953,10.086l1.832-1.832c1.171-1.172,3.071-1.172,4.242,0c1.172,1.171,1.172,3.071,0,4.242l-6.75,6.75   c-0.563,0.562-1.326,0.879-2.121,0.879c-0.796,0-1.559-0.316-2.121-0.879l-6.75-6.75c-1.172-1.172-1.172-3.071,0-4.242   c1.172-1.172,3.071-1.172,4.243,0l1.444,1.444c0.669-8.766,8-15.698,16.934-15.698C32.875,3.25,40.5,10.876,40.5,20.25z" />
      </G>
    </Svg>
  );
};
export const iconZoomInCCTV = () => {
  return (
    <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 32 32">
      <G id="Page-1" stroke="none" stroke-width="1" fill="none">
        <G
          id="Icon-Set-Filled"
          transform="translate(-310.000000, -1141.000000)"
          fill="#679290"
        >
          <Path
            d="M328.519,1155.41 L324.522,1155.41 L324.522,1159.41 C324.522,1159.97 324.075,1160.41 323.523,1160.41 C322.972,1160.41 322.524,1159.97 322.524,1159.41 L322.524,1155.41 L318.529,1155.41 C317.978,1155.41 317.53,1154.7 317.53,1154.14 C317.53,1153.59 317.978,1153.41 318.529,1153.41 L322.524,1153.41 L322.524,1149.41 C322.524,1148.85 322.972,1148.41 323.523,1148.41 C324.075,1148.41 324.522,1148.85 324.522,1149.41 L324.522,1153.41 L328.519,1153.41 C329.07,1153.41 329.518,1153.86 329.518,1154.41 C329.518,1154.96 329.07,1155.41 328.519,1155.41 L328.519,1155.41 Z M341.688,1171.25 L333.429,1163.12 C335.592,1160.77 336.92,1157.67 336.92,1154.25 C336.92,1146.93 330.894,1141 323.46,1141 C316.026,1141 310,1146.93 310,1154.25 C310,1161.56 316.026,1167.49 323.46,1167.49 C326.672,1167.49 329.618,1166.38 331.932,1164.53 L340.225,1172.69 C340.629,1173.09 341.284,1173.09 341.688,1172.69 C342.093,1172.3 342.093,1171.65 341.688,1171.25 L341.688,1171.25 Z"
            id="zoom-in"
          ></Path>
        </G>
      </G>
    </Svg>
  );
};
export const iconZoomOutCCTV = () => {
  return (
    <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 32 32">
      <G id="Page-1" stroke="none" stroke-width="1" fill="none">
        <G
          id="Icon-Set-Filled"
          transform="translate(-362.000000, -1141.000000)"
          fill="#679290"
        >
          <Path
            d="M380.689,1155.33 L370.643,1155.33 C370.102,1155.33 369.663,1154.9 369.663,1154.37 C369.663,1153.84 370.102,1153.41 370.643,1153.41 L380.689,1153.41 C381.23,1153.41 381.669,1153.84 381.669,1154.37 C381.669,1154.9 381.23,1155.33 380.689,1155.33 L380.689,1155.33 Z M393.688,1171.25 L385.429,1163.12 C387.592,1160.77 388.92,1157.67 388.92,1154.25 C388.92,1146.93 382.894,1141 375.46,1141 C368.026,1141 362,1146.93 362,1154.25 C362,1161.56 368.026,1167.49 375.46,1167.49 C378.672,1167.49 381.618,1166.38 383.932,1164.53 L392.225,1172.69 C392.629,1173.09 393.284,1173.09 393.688,1172.69 C394.093,1172.3 394.093,1171.65 393.688,1171.25 L393.688,1171.25 Z"
            id="zoom-out"
          ></Path>
        </G>
      </G>
    </Svg>
  );
};
export const iconCapterCCTV = () => {
  return (
    <View>
      <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 -2 32 32">
        <G
          id="Page-1"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <G
            id="Icon-Set-Filled"
            transform="translate(-258.000000, -467.000000)"
            fill="#679290"
          >
            <Path
              d="M286,471 L283,471 L282,469 C281.411,467.837 281.104,467 280,467 L268,467 C266.896,467 266.53,467.954 266,469 L265,471 L262,471 C259.791,471 258,472.791 258,475 L258,491 C258,493.209 259.791,495 262,495 L286,495 C288.209,495 290,493.209 290,491 L290,475 C290,472.791 288.209,471 286,471 Z M274,491 C269.582,491 266,487.418 266,483 C266,478.582 269.582,475 274,475 C278.418,475 282,478.582 282,483 C282,487.418 278.418,491 274,491 Z M274,477 C270.687,477 268,479.687 268,483 C268,486.313 270.687,489 274,489 C277.313,489 280,486.313 280,483 C280,479.687 277.313,477 274,477 L274,477 Z"
              id="camera"
            ></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconBigCCTV = () => {
  return (
    <View>
      <Svg width={scale(20)} height={verticalScale(20)} viewBox="0 0 32 32">
        <G id="Page-1" stroke="none" stroke-width="1" fill="none">
          <G
            id="Icon-Set-Filled"
            transform="translate(-154.000000, -985.000000)"
            fill="#000000"
          >
            <Path
              d="M180,997 C180,997.553 179.552,998 179,998 C178.448,998 178,997.553 178,997 L178.022,994.435 L173.244,999.213 L171.83,997.799 L176.628,993 L174,993 C173.448,993 173,992.553 173,992 C173,991.448 173.448,991 174,991 L178.972,991 C179.251,991 179.502,991.115 179.684,991.301 C179.877,991.465 180,991.704 180,992 L180,997 L180,997 Z M180,1010 C180,1010.3 179.877,1010.54 179.684,1010.7 C179.503,1010.88 179.251,1011 178.972,1011 L174,1011 C173.448,1011 173,1010.55 173,1010 C173,1009.45 173.448,1009 174,1009 L176.628,1009 L171.83,1004.2 L173.244,1002.79 L178.022,1007.57 L178,1005 C178,1004.45 178.448,1004 179,1004 C179.552,1004 180,1004.45 180,1005 L180,1010 L180,1010 Z M166.756,999.213 L161.978,994.435 L162,997 C162,997.553 161.552,998 161,998 C160.448,998 160,997.553 160,997 L160,992 C160,991.704 160.123,991.465 160.316,991.301 C160.498,991.115 160.749,991 161.028,991 L166,991 C166.552,991 167,991.448 167,992 C167,992.553 166.552,993 166,993 L163.372,993 L168.17,997.799 L166.756,999.213 L166.756,999.213 Z M166,1009 C166.552,1009 167,1009.45 167,1010 C167,1010.55 166.552,1011 166,1011 L161.028,1011 C160.749,1011 160.497,1010.88 160.316,1010.7 C160.123,1010.54 160,1010.3 160,1010 L160,1005 C160,1004.45 160.448,1004 161,1004 C161.552,1004 162,1004.45 162,1005 L161.978,1007.57 L166.756,1002.79 L168.17,1004.2 L163.372,1009 L166,1009 L166,1009 Z M182,985 L158,985 C155.791,985 154,986.791 154,989 L154,1013 C154,1015.21 155.791,1017 158,1017 L182,1017 C184.209,1017 186,1015.21 186,1013 L186,989 C186,986.791 184.209,985 182,985 L182,985 Z"
              id="zoom"
            ></Path>
          </G>
        </G>
      </Svg>
    </View>
  );
};
export const iconSaveImages = () => {
  return (
    <View>
      <Svg
        fill="white"
        width={scale(20)}
        height={verticalScale(20)}
        viewBox="-6.5 0 32 32"
      >
        <Path d="M12.188 4.469v4.656h2.438l-4.875 5.875-4.875-5.875h2.563v-4.656h4.75zM16.313 12l2.844 4.5c0.156 0.375 0.344 1.094 0.344 1.531v8.656c0 0.469-0.375 0.813-0.813 0.813h-17.844c-0.469 0-0.844-0.344-0.844-0.813v-8.656c0-0.438 0.156-1.156 0.313-1.531l2.844-4.5c0.156-0.406 0.719-0.75 1.125-0.75h1.281l1.313 1.594h-2.625l-2.531 4.625c-0.031 0-0.031 0.031-0.031 0.063 0 0.063 0 0.094-0.031 0.125h16.156v-0.125c0-0.031-0.031-0.063-0.031-0.094l-2.531-4.594h-2.625l1.313-1.594h1.25c0.438 0 0.969 0.344 1.125 0.75zM7.469 21.031h4.594c0.406 0 0.781-0.375 0.781-0.813 0-0.406-0.375-0.781-0.781-0.781h-4.594c-0.438 0-0.813 0.375-0.813 0.781 0 0.438 0.375 0.813 0.813 0.813z" />
      </Svg>
    </View>
  );
};
export const iconBookMark = () => {
  return (
    <View>
      <Svg
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          opacity="0.5"
          d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
          fill="#D6D6D6"
        />
        <Path
          d="M16 14.0455V11.5488C16 9.40445 16 8.3323 15.4142 7.66615C14.8284 7 13.8856 7 12 7C10.1144 7 9.17157 7 8.58579 7.66615C8 8.3323 8 9.40445 8 11.5488V14.0455C8 15.5937 8 16.3679 8.32627 16.7062C8.48187 16.8675 8.67829 16.9688 8.88752 16.9958C9.32623 17.0522 9.83855 16.5425 10.8632 15.5229C11.3161 15.0722 11.5426 14.8469 11.8046 14.7875C11.9336 14.7583 12.0664 14.7583 12.1954 14.7875C12.4574 14.8469 12.6839 15.0722 13.1368 15.5229L13.1368 15.5229C14.1615 16.5425 14.6738 17.0522 15.1125 16.9958C15.3217 16.9688 15.5181 16.8675 15.6737 16.7062C16 16.3679 16 15.5937 16 14.0455Z"
          fill="#D6D6D6"
        />
      </Svg>
    </View>
  );
};
export const iconBookMarkOn = () => {
  return (
    <View>
      <Svg
        width={scale(40)}
        height={verticalScale(40)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          opacity="0.5"
          d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
          fill="#000000"
        />
        <Path
          d="M16 14.0455V11.5488C16 9.40445 16 8.3323 15.4142 7.66615C14.8284 7 13.8856 7 12 7C10.1144 7 9.17157 7 8.58579 7.66615C8 8.3323 8 9.40445 8 11.5488V14.0455C8 15.5937 8 16.3679 8.32627 16.7062C8.48187 16.8675 8.67829 16.9688 8.88752 16.9958C9.32623 17.0522 9.83855 16.5425 10.8632 15.5229C11.3161 15.0722 11.5426 14.8469 11.8046 14.7875C11.9336 14.7583 12.0664 14.7583 12.1954 14.7875C12.4574 14.8469 12.6839 15.0722 13.1368 15.5229L13.1368 15.5229C14.1615 16.5425 14.6738 17.0522 15.1125 16.9958C15.3217 16.9688 15.5181 16.8675 15.6737 16.7062C16 16.3679 16 15.5937 16 14.0455Z"
          fill="#ffdf6b"
        />
      </Svg>
    </View>
  );
};
export const iconFullscreen = () => {
  return (
    <View>
      <Svg
        width={scale(24)}
        height={verticalScale(24)}
        viewBox="0 0 24 24"
        fill="none"
      >
        <Path
          stroke="#fff"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19h4m0 0v-4m0 4-4-4M9 5H5m0 0v4m0-4 4 4m6-4h4m0 0v4m0-4-4 4M9 19H5m0 0v-4m0 4 4-4"
        />
      </Svg>
    </View>
  );
};
export const iconSavePoltName = () => {
  return (
    <View>
      <Svg
        fill="#ffffff"
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        id="check-mark-circle"
        data-name="Flat Color"
      >
        <G id="SVGRepo_bgCarrier" stroke-width="0" />

        <G
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <G id="SVGRepo_iconCarrier">
          <Circle id="primary" cx="12" cy="12" r="10" fill="#ffffff" />

          <Path
            id="secondary"
            d="M11,15.5a1,1,0,0,1-.71-.29l-3-3a1,1,0,1,1,1.42-1.42L11,13.09l4.29-4.3a1,1,0,0,1,1.42,1.42l-5,5A1,1,0,0,1,11,15.5Z"
            fill="#84B8A2"
          />
        </G>
      </Svg>
    </View>
  );
};
export const iconCanclePoltName = () => {
  return (
    <View>
      <Svg
        fill="#000000"
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        id="cross-circle"
        data-name="Flat Color"
      >
        <Circle id="primary" cx="12" cy="12" r="10" fill="#ffffff" />
        <Path
          id="secondary"
          d="M13.41,12l2.3-2.29a1,1,0,0,0-1.42-1.42L12,10.59,9.71,8.29A1,1,0,0,0,8.29,9.71L10.59,12l-2.3,2.29a1,1,0,0,0,0,1.42,1,1,0,0,0,1.42,0L12,13.41l2.29,2.3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42Z"
          fill="#E74C3C"
        />
      </Svg>
    </View>
  );
};
export const iconEditPoltName = () => {
  return (
    <View>
      <Svg
        fill="#000000"
        width={scale(30)}
        height={verticalScale(30)}
        viewBox="0 0 24 24"
        id="edit-circle-alt"
        data-name="Flat Color"
      >
        <Circle id="primary" cx="12" cy="12" r="10" fill="#ffffff" />
        <Path
          id="secondary"
          d="M20.21,3.79a3,3,0,0,0-4.25,0L10.24,9.51a1,1,0,0,0-.29.73L10,13a1,1,0,0,0,1,1l2.78.05h0a1,1,0,0,0,.7-.29L20.21,8a3,3,0,0,0,0-4.25Z"
          fill="#84B8A2"
        />
      </Svg>
    </View>
  );
};
