import { ScaledSheet } from "react-native-size-matters";
//StyleSheet
import {
  BgColor,
  BgOpacity,
  FonstColor,
  BottonColor,
} from "../styleSheet/style_Custom";

const mod = ScaledSheet.create({
  mod_safe: {
    flex: 1,
    justifyContent: "center",
  },
  mod_center: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  mod_comment: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  mod_end: {
    flex: 1,
    paddingBottom: "15@vs",
    alignItems: "center",
    justifyContent: "flex-end",
    backgroundColor: BgOpacity.Op_0000005,
  },
  mod_profile: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_000000,
  },
  mod_View: {
    borderRadius: 40,
    alignItems: "center",
    paddingVertical: "20@ms",
    backgroundColor: "white",
    paddingHorizontal: "40@ms",
  },
  mod_ViewPayment: {
    borderRadius: 40,
    backgroundColor: "white",
    paddingHorizontal: "40@ms",
  },
  mod_ViewComment: {
    borderRadius: 20,
    alignItems: "center",
    paddingVertical: "20@ms",
    backgroundColor: "white",
    paddingHorizontal: "20@ms",
  },
  button: {
    padding: "10@ms",
    elevation: 2,
    borderRadius: 180,
  },
  buttonClose: {
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  buttonTrue: {
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  textStyle: {
    textAlign: "center",
    color: FonstColor.Tc_000000,
  },
  modalTextFlase: {
    textAlign: "center",
    color: FonstColor.Tc_FF9900,
  },
  modalTextTitleTrue: {
    textAlign: "center",
    color: FonstColor.Tc_666666,
  },
  modalTextTrue: {
    textAlign: "center",
    color: FonstColor.Tc_64B548,
  },

  bgOpacityFlase: {
    width: "120@ms",
    height: "120@ms",
    bottom: "70@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bgOpacityTrue: {
    width: "120@ms",
    height: "120@ms",
    bottom: "70@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_7bae6a5,
  },
  bgInOpacityFlase: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  bgInOpacityTrue: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_9BFF79,
  },
  ImgSucceed: {
    width: "50@ms",
    height: "50@ms",
  },

  //Detail Area
  continuePackAge: {
    width: "90%",
    padding: 20,
    borderRadius: 40,
    backgroundColor: "white",
  },
  flexPackAge: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  flexPackDetail: {
    flexDirection: "row",
    justifyContent: "space-between",
  },

  //Plus Area
  bgOpacityPlus: {
    width: "80@ms",
    height: "80@ms",
    bottom: "25%",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bgInOpacityPlus: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFD39E,
  },
  modalViewPlus: {
    backgroundColor: "white",
    borderRadius: 26,
    paddingHorizontal: "20@ms",
    paddingVertical: "10@ms",
    alignItems: "center",
  },
  modalFullimgvid: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_000000,
  },
  mod_Cancle: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  mod_Agee: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_FF9900,
  },
  mod_AgeeNon: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  mod_AgeeRenew: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_FFD000,
  },
  mod_AgeeFarm: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  mod_ctnMap: {
    width: "90%",
    padding: "20@ms",
    borderRadius: 40,
    backgroundColor: "white",
  },
});

export default mod;
