import fonstStyle, {
  BgColor,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
} from "../styleSheet/style_Custom";
import { Platform, Dimensions } from "react-native";
import { ScaledSheet } from "react-native-size-matters";
const windowHeight = Dimensions.get("window").height;

const txt = ScaledSheet.create({
  txt_ctn: {
    flex: 1,
    height: "300@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  txt_Singnin: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_Register: {
    textAlign: "center",
    color: FonstColor.Tc_84B8A2,
  },
  txt_or: {
    bottom: 12,
    width: "10%",
    textAlign: "center",
    color: FonstColor.Tc_A5A5A5,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  txt_Seation: {
    color: FonstColor.Tc_666666,
  },
  txt_HeaderInput: {
    color: FonstColor.Tc_A6A6A6,
  },
  txt_PasswordSix: {
    color: FonstColor.Tc_E74C3C,
    textAlign: "right",
  },
  txt_InputSignUp: {
    borderWidth: 1,
    padding: "10@ms",
    borderRadius: 180,
    borderColor: BorderColor.Bd_84B8A2,
  },
  txt_InputChangePass: {
    padding: "10@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  txt_SignUp: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_modFlase: {
    textAlign: "center",
    color: FonstColor.Tc_FF9900,
  },
  txt_modTrue: {
    textAlign: "center",
    color: FonstColor.Tc_666666,
  },
  txt_modSuccess: {
    textAlign: "center",
    color: FonstColor.Tc_64B548,
  },
  txt_modReject: {
    textAlign: "center",
    color: FonstColor.Tc_FF9900,
  },
  txt_center: {
    textAlign: "center",
  },
  txt_inputPost: {
    width: "80%",
    padding: "10@ms",
    justifyContent: "center",
  },
  txt_inputReject: {
    width: "100%",
    padding: "10@ms",
    borderWidth: 1,
    borderRadius: 10,
    borderColor: BgColor.Bg_D6D6D6,
  },
  txt_inputProcess: {
    width: null,
    height: 110,
    padding: "10@ms",
    borderRadius: 16,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  txt_post: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_Hrzt: {
    bottom: "25@ms",
    paddingHorizontal: 5,
    textAlign: "center",
  },
  txt_FollowHrzt: {
    bottom: 25,
    textAlign: "center",
  },
  txt_BtnHrzt: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_UnBtnHrzt: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_Vtcal: {
    marginLeft: "10@ms",
  },
  txt_time: {
    marginTop: "2@ms",
    marginLeft: "10@ms",
    color: FonstColor.Tc_666666,
  },
  txt_cardPost: {
    width: "100%",
    marginTop: "10@ms",
  },
  txt_fullTxt: {
    margin: "5@ms",
    textAlign: "right",
    color: FonstColor.Tc_84B8A2,
  },
  txt_imageNumber: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_videoNumber: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_timeFull: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_likeComment: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_horizontalPlus: {
    textAlign: "center",
    color: FonstColor.Tc_A5A5A5,
  },
  txt_vaticalPlus: {
    textAlign: "center",
    color: FonstColor.Tc_A5A5A5,
  },
  txt_editComment: {
    width: "100%",
    borderRadius: 180,
    paddingHorizontal: "10@ms",
    marginBottom: "5@ms",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  txt_editSave: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_dateOption: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_deleteOption: {
    textAlign: "center",
    color: FonstColor.Tc_E74C3C,
  },
  txt_editOption: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_repayOption: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_detailPost: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_Number: {
    textAlign: "center",
    color: FonstColor.Tc_84B8A2,
  },
  txt_tap: {
    textAlign: "center",
    color: FonstColor.Tc_A6A6A6,
  },
  txt_Active: {
    textAlign: "center",
    color: FonstColor.Tc_84B8A2,
  },
  txt_nonActive: {
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_fixText: {
    color: FonstColor.Tc_A6A6A6,
  },
  txt_directed: {
    color: FonstColor.Tc_A5A5A5,
  },
  txt_nonArea: {
    textAlign: "center",
    color: FonstColor.Tc_A6A6A6,
  },
  txt_framArea: {
    color: FonstColor.Tc_A6A6A6,
  },
  txt_orange: {
    color: FonstColor.Tc_FF9900,
  },
  txt_yellow: {
    color: FonstColor.Tc_FFD000,
  },
  txt_red: {
    color: FonstColor.Tc_E74C3C,
  },
  txt_white: {
    color: FonstColor.Tc_FFFFFF,
  },
  txt_616161: {
    color: FonstColor.Tc_616161,
  },
  txt_606060: {
    color: FonstColor.Tc_606060,
  },
  txt_A6A6A6: {
    color: FonstColor.Tc_A6A6A6,
  },
  txt_black: {
    color: FonstColor.Tc_000000,
  },
  txt_346359: {
    color: FonstColor.Tc_346359,
  },
  txt_4FA5F4: {
    color: FonstColor.Tc_4FA5F4,
  },
  txt_6AB252: {
    color: FonstColor.Tc_6AB252,
  },
  txt_titleManage: {
    zIndex: 999,
    alignItems: "center",
    marginTop:
      Platform.OS === "ios" ? windowHeight * 0.075 : windowHeight * 0.055,
  },
  txt_InputSend: {
    width: "85%",
    paddingHorizontal: 10,
  },
  txt_detailArea: {
    width: "90%",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_status: {
    width: "50%",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_green: {
    color: FonstColor.Tc_84B8A2,
  },
  txt_greenPH: {
    color: FonstColor.Tc_00A900,
  },
  txt_headerDetailPay: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_qrCode: {
    zIndex: 999,
    marginTop: 10,
    textAlign: "center",
    color: FonstColor.Tc_606060,
  },
  txt_bottomControl: {
    textAlign: "center",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_editProfile: {
    borderRadius: 5,
    padding: 10,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  txt_manage: {
    marginTop: "5@ms",
    color: FonstColor.Tc_2F4528,
  },
  txt_manageClick: {
    marginTop: "5@ms",
    color: FonstColor.Tc_FFFFFF,
  },
  txt_Stutes1: {
    color: FonstColor.Tc_FFFFFF,
    textAlign: "center",
  },
  txt_Stutes2: {
    color: FonstColor.Tc_679290,
    textAlign: "center",
  },
  txt_dayText: {
    textAlign: "center",
    justifyContent: "center",
  },
  text_PlansName: {
    color: FonstColor.Tc_679290,
    textAlign: "center",
  },
  text_Harvest: {
    top: "15@ms",
    textAlign: "center",
  },
  txt_gray: {
    color: FonstColor.Tc_A5A5A5,
  },
  textStutes: {
    color: FonstColor.Tc_FFFFFF,
    textAlign: "center",
  },
  textStutesNon: {
    color: FonstColor.Tc_679290,
    textAlign: "center",
  },
});

export default txt;
