import {
  BgColor,
  BgOpacity,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
  LgColor,
} from "../styleSheet/style_Custom";
import DeviceInfo from "react-native-device-info";
import { Dimensions, Platform, StyleSheet } from "react-native";
import { ScaledSheet } from "react-native-size-matters";
import { useOrientation } from "./../hooks/useOrientation";
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const { width: screenWidth } = Dimensions.get("window");
const { height: screenHeight } = Dimensions.get("window");
const isTablet = DeviceInfo.isTablet();

const ctn = ScaledSheet.create({
  continue: {
    flex: 1,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  continueMain: {
    flex: 1,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  continueNoti: {
    flex: 1,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  continue_main: {
    flex: 1,
    backgroundColor: BgColor.Bg_B3DBC0,
  },
  continueNon: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_loading: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  ctn_flexRowEnd: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  ctn_BgBack: {
    padding: "5@ms",
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_ffffff5,
  },
  ctn_logoLoging: {
    flex: 1,
    marginTop: "20@s",
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_btmLoging: {
    // padding: "20@ms",
    paddingVertical: "30@ms",
    paddingHorizontal: "20@ms",
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    backgroundColor: BgColor.Bg_FFFFFF,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  ctn_social: {
    flex: 1,
    marginTop: "10@vs",
    alignItems: "center",
  },
  ctn_RabbitSignUp: {
    flex: 1,
    marginTop: "10@ms",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  ctn_inPutSignUp: {
    flex: 1,
    paddingHorizontal: '20@ms'
  },
  ctn_iconEye: {
    bottom: "25@vs",
    left: "90%",
    height: 0,
  },
  ctn_profile: {
    width: "40@ms",
    height: "40@ms",
    borderWidth: 1,
    borderRadius: 180,
    borderColor: BorderColor.Bd_BDBDBD,
  },
  ctn_selectPost: {
    flexDirection: "row",
    marginTop: "10@s",
    padding: "10@ms",
    justifyContent: "space-between",
  },
  ctn_space: {
    flexDirection: "row",
    width: "220@s",
  },
  ctn_spaceBet: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_spaceCenter: {
    flexDirection: "row",
    justifyContent: "center",
  },
  ctn_spaceEnd: {
    justifyContent: "flex-end",
    flexDirection: "row",
    alignItems: "center",
  },
  ctn_spaceWait: {
    marginTop: "5@ms",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_spaceComment: {
    flexDirection: "row",
    width: "180@s",
  },
  ctn_profileHrzt: {
    width: "50@ms",
    height: "50@ms",
    bottom: "25@s",
    borderWidth: 1,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderColor: BorderColor.Bd_BDBDBD,
  },
  ctn_profileVtcal: {
    width: "40@ms",
    height: "40@ms",
    borderWidth: 1,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderColor: BorderColor.Bd_BDBDBD,
  },
  ctn_TxtProfile: {
    width: "200@s",
    flexDirection: "column",
  },
  ctn_MenuDot: {
    top: "10@ms",
    right: "10@ms",
    zIndex: 999,
    bottom: "50@ms",
    position: "absolute",
    alignItems: "flex-end",
    justifyContent: "flex-start",
  },
  ctn_menuDot: {
    flexDirection: "row",
    alignItems: "center",
  },
  ctn_imagePost: {
    // marginTop: "10@vs",
    flexWrap: "wrap",
    flexDirection: "row",
  },
  ctn_videoPost: {
    // marginTop: "10@vs",
    flexWrap: "wrap",
    flexDirection: "row",
  },
  ctn_imgaePlus: {
    width: "100%",
    height: "100%",
    // borderRadius: 8,
    alignItems: "center",
    position: "absolute",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  cnt_iconVideo: {
    padding: "5@ms",
    zIndex: 999,
    position: "absolute",
  },
  ctn_play: {
    flex: 1,
    top: "40%",
    zIndex: 999,
    alignItems: "center",
  },
  ctn_iconPlay: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_video: {
    width: "100%",
    height: "209@vs",
    // borderRadius: 8,
    overflow: "hidden",
    backgroundColor: BgColor.Bg_000000,
  },
  ctn_muted: {
    bottom: "30@ms",
    zIndex: 999,
    alignItems: "flex-end",
  },
  ctn_videoPlus: {
    position: "absolute",
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  ctn_likeComment: {
    // marginTop: "10@ms",
    // paddingBottom: "10@ms",
    flexDirection: "row",
    paddingHorizontal: 10,
  },
  ctn_imgPosting: {
    top: "2@ms",
    left: "70%",
    zIndex: 999,
    borderRadius: 24,
    paddingVertical: 3,
    paddingHorizontal: 5,
    position: "absolute",
    backgroundColor: BgOpacity.Op_E74C3C05,
  },
  ctn_vidPosting: {
    top: "5@ms",
    right: "5@ms",
    zIndex: 999,
    borderRadius: 24,
    paddingVertical: 3,
    paddingHorizontal: 5,
    position: "absolute",
    overflow: "hidden",
    backgroundColor: BgOpacity.Op_E74C3C05,
  },
  ctn_iconfullVid: {
    top: "50%",
    zIndex: 999,
    position: "absolute",
    alignItems: "center",
  },
  ctn_fullVideo: {
    width: windowWidth,
    height: windowHeight,
    borderRadius: 8,
  },
  ctn_mutedFull: {
    right: "50@ms",
    padding: "5@ms",
    bottom: "60@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_shortVideo: {
    right: "20@ms",
    padding: "5@ms",
    bottom: "60@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_horizontalBottom: {
    paddingVertical: "40@ms",
    paddingHorizontal: "20@ms",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_verticalBottom: {
    flex: 1,
    alignItems: "center",
  },
  ctn_videoFull: {
    bottom: 0,
    width: "100%",
    position: "absolute",
    alignItems: "center",
  },
  ctn_KeyBordComment: {
    width: "80%",
    flexDirection: "row",
    paddingHorizontal: "10@ms",
    borderColor: BorderColor.Bd_F4F4F4,
  },
  ctn_KeyBordChat: {
    width: "100%",
    // flexDirection: "row",
    // justifyContent: "space-between",
    paddingHorizontal: "10@ms",
    borderColor: BorderColor.Bd_F4F4F4,
  },
  ctn_Send: {
    justifyContent: "flex-end",
  },
  ctn_comment: {
    width: "90%",
    paddingBottom: "8@ms",
    flexDirection: "row",
    paddingHorizontal: "10@ms",
  },
  ctn_profileComment: {
    width: "30@ms",
    height: "30@ms",
    borderWidth: 1,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderColor: BorderColor.Bd_BDBDBD,
  },
  ctn_nameComtent: {
    width: "100%",
    padding: "10@ms",
    borderRadius: 20,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  ctn_optionComment: {
    flexDirection: "row",
    paddingHorizontal: "45@ms",
  },
  ctn_reComment: {
    width: "90%",
    flexDirection: "row",
    paddingBottom: "8@ms",
    paddingHorizontal: "40@ms",
  },
  ctn_optionReComment: {
    flexDirection: "row",
    paddingHorizontal: "85@ms",
  },
  ctn_editImgVid: {
    marginTop: "10@ms",
  },
  ctn_editDelete: {
    zIndex: 999,
    top: "10@ms",
    alignItems: "flex-end",
  },
  ctn_scrollView: {
    padding: "10@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_scrollProfile: {
    // flex: 1,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  ctn_covenProfile: {
    zIndex: 999,
    top: "150@ms",
    right: "20@ms",
    position: "absolute",
  },
  ctn_editSvg: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ffffff5,
  },
  ctn_optionProfile: {
    marginTop: "150@ms",
    zIndex: 9999,
    position: "absolute",
    paddingHorizontal: "20@ms",
  },
  ctn_optionProfileFriend: {
    marginTop: "100@ms",
    zIndex: 9999,
    position: "absolute",
    paddingHorizontal: "20@ms",
  },
  ctn_mainProfile: {
    width: "106@ms",
    height: "106@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_editMainProfile: {
    top: "35@ms",
    left: "35@ms",
    zIndex: 999,
    position: "absolute",
  },
  ctn_iconSetting: {
    top: "220@ms",
    right: "55@ms",
    zIndex: 9999,
    position: "absolute",
  },
  ctn_iconLogout: {
    top: "220@ms",
    right: "20@ms",
    zIndex: 9999,
    position: "absolute",
    transform: [{ rotate: "180deg" }],
  },
  ctn_nameProfile: {
    marginTop: "10@ms",
    justifyContent: "center",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_spaceNumber: {
    flexDirection: "row",
    justifyContent: "flex-end",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_spaceFriend: {
    right: "10@ms",
    marginTop: "10@ms",
    flexDirection: "row",
    position: "absolute",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_spaceHeader: {
    marginTop: "10@ms",
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_Active: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BottonColor.Bt_DAEEDE,
  },
  ctn_nonActive: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_ActivePost: {
    // width: screenWidth * 0.334,
    // height: screenHeight * 0.15,
     width: screenWidth * 0.334,
    height: screenHeight * 0.15,
  },
  ctn_ActiveVideo: {
    width: screenWidth * 0.334,
    height: screenHeight * 0.2,
  },
  ctn_information: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_unDerIcon: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  ctn_detail: {
    width: "85%",
    flexDirection: "column",
  },
  ctn_editAccount: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_Radio: {
    borderWidth: 2,
    width: "20@ms",
    height: "20@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    borderColor: BorderColor.Bd_84B8A2,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_nonArea: {
    marginTop: "40%",
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_txtRest: {
    marginTop: "10@ms",
    paddingHorizontal: "20@ms",
  },
  ctn_myFarmNon: {
    flexDirection: "row",
    paddingHorizontal: "20@ms",
    justifyContent: "space-between",
  },
  ctn_myFarm: {
    marginTop: "20@ms",
    flexDirection: "row",
    paddingHorizontal: "20@ms",
    justifyContent: "space-between",
  },
  ctn_farmPackAge: {
    marginTop: 10,
    borderRadius: 30,
    alignItems: "center",
    flexDirection: "row",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  ctn_farmPackAgeNon: {
    marginTop: 10,
    height: "80@ms",
    borderRadius: 30,
    flexDirection: "row",
    backgroundColor: BgColor.Bg_F4F4F4,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  ctn_farmPackProfile: {
    padding: "10@ms",
    height: "100@ms",
    borderRadius: 30,
    flexDirection: "row",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_Seeds: {
    paddingHorizontal: "20@ms",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_BottomSheet: {
    height: "250@ms",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_BottomComment: {
    // flex: 1,
    height: 600,
    backgroundColor: BgColor.Bg_FFFFFF,
    // backgroundColor: "red"
  },
  ctn_iconPlans: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_selectArea: {
    padding: "5@ms",
    borderRadius: 16,
    alignItems: "center",
    width: windowWidth * 0.4,
    height: windowHeight * 0.23,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_nextArea: {
    left: "70%",
    bottom: "25@ms",
    position: "absolute",
  },
  ctn_detailArea: {
    flex: 1,
    flexDirection: "row",
  },
  ctn_bgDetailArea: {
    flex: 1,
    backgroundColor: BgColor.Bg_E1E7E0,
  },
  ctn_detailSelect: {
    flex: 3.5,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_bottomArea: {
    width: "95%",
    position: "absolute",
    alignItems: "flex-end",
    justifyContent: "center",
    bottom: windowHeight * 0.025,
  },
  ctn_mapVetGet: {
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    width: windowWidth * 0.8,
    height: windowWidth * 0.6,
    backgroundColor: BgColor.Bg_F4F4F4,
    overflow: "hidden",
  },
  ctn_Status: {
    bottom: 0,
    width: "100%",
    padding: "5@ms",
    position: "absolute",
  },
  ctn_headerDetailPay: {
    zIndex: 1,
    width: "100%",
    position: "absolute",
  },
  ctn_detailPay: {
    flex: "2@ms",
    zIndex: 999,
    // paddingHorizontal: "20@ms",
    // paddingVertical: "10@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
    shadowColor: "#000",
  },
  ctn_btnDetailPay: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_btnDetailPayNon: {
    alignItems: "center",
  },
  ctn_account: {
    width: "100%",
    alignItems: "flex-start",
    paddingVertical: "20@ms",
    paddingHorizontal: "20@ms",
  },
  ctn_bottomQrPay: {
    bottom: "25@ms",
    alignItems: "center",
  },
  ctn_coppy: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_imgBankDelete: {
    zIndex: 999,
    borderRadius: 180,
    marginTop: "10@ms",
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  ctn_txtDistance: {
    padding: "2@ms",
    borderRadius: 180,

    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_control: {
    right: "10@ms",
    padding: "8@ms",
    bottom: Platform.OS === "ios" ? "25@ms" : "10@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    backgroundColor: BgOpacity.Op_C0D576,
  },
  ctn_controlPlus: {
    right: "10@ms",
    bottom: Platform.OS === "ios" ? "25@ms" : "10@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
  },
  ctn_titleDetail: {
    zIndex: 999,
    top: "20@ms",
    width: "50%",
    height: "40@ms",
    borderRadius: 180,
  },
  ctn_farmDetail: {
    width: "95%",
    borderRadius: 30,
    borderWidth: 10,
    borderColor: BgColor.Bg_A3BF59,
    backgroundColor: BgColor.Bg_FFF4E3,
  },
  ctn_thumbWrapper: {
    top: 0,
    width: 30,
    height: 30,
    position: "absolute",
  },
  ctn_slinderPH: {
    width: "100%",
    height: 40,
    borderRadius: 10,
    justifyContent: "center",
    paddingHorizontal: 10,
  },
  ctn_imgSlider: {
    width: 30,
    height: 30,
    position: "absolute",
    top: 0,
    resizeMode: "contain",
  },
  ctn_bgSlider: {
    left: 0,
    right: 0,
    height: 20,
    borderRadius: 5,
    position: "absolute",
  },
  ctn_Slider: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_productProfile: {
    flex: 1,
    alignItems: "center",
    flexDirection: "row",
  },
  ctn_inputComment: {
    alignItems: "center",
    paddingVertical: 10,
    flexDirection: "row",
    paddingHorizontal: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_keyBoard: {
    paddingHorizontal: 20,
    alignItems: "flex-end",
  },
  ctn_Indicator: {
    left: 0,
    right: 0,
    alignItems: "center",
    position: "absolute",
    top: Platform.OS === "ios" ? "60@ms" : "0@ms",
  },
  ctn_txtIndicator: {
    borderRadius: 20,
    paddingHorizontal: 8,
    alignItems: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_nameFarm: {
    width: "100%",
    position: "absolute",
    alignItems: "center",
    // zIndex: 9999,
  },
  ctn_nameFarmLan: {
    width: "100%",
    position: "absolute",
    // alignItems: "center",
    left: "80@ms",
    right: 0,
  },
  ctn_manageFarm: {
    flex: 1,
    paddingBottom: "15@vs",
    alignItems: "center",
    justifyContent: "flex-end",
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_manage: {
    width: "95%",
    height: "450@vs",
    borderWidth: 8,
    borderRadius: 30,
    padding: "10@ms",
    // alignItems: "center",
    borderColor: LgColor.Lg_CFF1FF,
    // backgroundColor: BgColor.Bg_FFF4E3,
  },
  ctn_imgInFrom: {
    width: "95%",
    height: "450@vs",
    borderWidth: 8,
    borderRadius: 30,
    padding: "10@ms",
    // alignItems: "center",
    borderColor: LgColor.Lg_CFF1FF,
    backgroundColor: BgColor.Bg_FFF4E3,
  },
  ctn_bottonTools: {
    width: "100%",
    padding: "5@ms",
    alignItems: "center",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_0C4D3F,
  },
  ctn_Tools1: {
    flex: 2,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_timeWater: {
    flex: 1,
    marginTop: "20@ms",
    // padding: "20@ms",
  },
  ctn_spaceWater: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_daysContainer: {
    marginTop: "10@ms",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ctn_yesNo: {
    padding: "10@ms",
    flexDirection: "row",
  },
  ctn_continueBottomActive2: {
    flexDirection: "row",
    marginTop: windowHeight * 0.001,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_continueBottomActive3: {
    flexDirection: "row",
    marginTop: windowHeight * 0.001,
    alignItems: "center",
    justifyContent: "center",
  },
  continueBottomActive4: {
    marginTop: "10@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_continueBottonStutes: {
    padding: "5@ms",
    width: "100%",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_0C4D3F,
  },
  ctn_WaitList: {
    marginTop: "5@ms",
    padding: "10@ms",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_Finish: {
    marginTop: "5@ms",
    padding: "14@ms",
    borderWidth: 1,
    borderRadius: 20,
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_imgTimeWater: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_SelectPlans: {
    width: "95%",
    height: "450@vs",
    alignItems: "center",
    paddingVertical: "10@ms",
    borderRadius: 30,
    borderWidth: 10,
    borderColor: BgColor.Bg_A3BF59,
    backgroundColor: BgColor.Bg_F2FFC2,
  },
  ctn_DeleteArea: {
    // padding: "14@ms",
    borderRadius: 20,
  },
  ctn_iconDelete: {
    right: 0,
    bottom: 0,
    zIndex: 999,
    position: "absolute",
  },
  ctn_iconDeleteArea: {
    top: 0,
    right: 10,
    bottom: 0,
    zIndex: 999,
    position: "absolute",
    justifyContent: "center",
  },
  ctn_iconDeleteAreafalse: {
    top: 0,
    right: 0,
    bottom: 10,
    zIndex: 999,
    opacity: 0.5,
    position: "absolute",
    justifyContent: "center",
  },
  ctn_iconMapArea: {
    top: 0,
    right: 60,
    bottom: 0,
    zIndex: 999,
    position: "absolute",
    justifyContent: "center",
  },
  ctn_detailDelete: {
    // marginTop: 10,
    padding: "14@ms",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  ctn_headerControlRow: {
    right: "10@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    flexDirection: "row",
    marginTop: windowHeight * 0.075,
  },
  ctn_countArea: {
    right: 0,
    left: 0,
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    marginTop: windowHeight * 0.14,
  },
  ctn_countAreaLan: {
    right: "60@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    marginTop: isTablet ? "15%" : "9%",
  },
  ctn_headerControlRowLan: {
    right: "10@ms",
    padding: 10,
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    flexDirection: "row",
    marginTop: "20@ms",
  },
  ctn_headerControl: {
    right: "10@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    marginTop: windowHeight * 0.075,
  },
  ctn_nonti: {
    zIndex: 1,
    left: "1@ms",
    borderRadius: 180,
    paddingVertical: 2,
    paddingHorizontal: 8,
    position: "absolute",
    backgroundColor: "red",
  },
  ctn_nontiPage: {
    borderRadius: 180,
    paddingVertical: 2,
    paddingHorizontal: 8,
    backgroundColor: "red",
  },
  ctn_nontiRow: {
    zIndex: 1,
    left: "1@ms",
    top: "0@ms",
    borderRadius: 180,
    paddingVertical: 2,
    paddingHorizontal: 8,
    position: "absolute",
    backgroundColor: "red",
  },
  ctn_boxBgHeaer: {
    zIndex: 0,
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_headerActive2: {
    right: "10@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    marginTop: windowHeight * 0.13,
  },
  ctn_headerActive2Lan: {
    right: "14@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    alignItems: "center",
    marginTop: isTablet ? "15%" : "9%",
  },
  ctn_mange: {
    width: isTablet ? screenWidth * 0.275 : screenWidth * 0.26,
    height: isTablet ? screenHeight * 0.2 : screenHeight * 0.17,
    borderRadius: 26,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_mangeSelect: {
    width: screenWidth * 0.26,
    height: screenHeight * 0.17,
    borderRadius: 26,
    paddingVertical: "8@ms",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_E2EBCC,
  },
  ctn_boxSelect: {
    width: "80@ms",
    height: "28@ms",
    borderWidth: 2,
    borderRadius: 180,
    borderColor: BgColor.Bg_FFEA61,
  },
  ctn_renderStutes: {
    flex: 1,
    padding: "10@ms",
    marginTop: "8@ms",
    borderTopRightRadius: 20,
    borderTopLeftRadius: 180,
    borderBottomLeftRadius: 180,
    borderBottomRightRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_bgImgStutes: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    backgroundColor: BgColor.Bg_EEFBF1,
  },
  ctn_InFrom: {
    width: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  ctn_flexInFrom: {
    padding: "10@ms",
    flexDirection: "row",
  },
  ctn_bgPlansInFrom: {
    width: "40@ms",
    height: "40@ms",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_Watering: {
    borderWidth: 1,
    borderRadius: 20,
    marginTop: "10@ms",
    alignItems: "center",
    justifyContent: "center",
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_WateringList: {
    borderWidth: 1,
    borderRadius: 20,
    marginTop: "10@ms",
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_LeftPlans: {
    padding: "5@ms",
    marginTop: "5@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_clickPlans: {
    paddingHorizontal: "5@ms",
    marginTop: "10@ms",
  },
  ctn_checkProduct: {
    // paddingHorizontal: "5@ms",
    // marginTop: "10@ms",
    backgroundColor: "red",
  },
  ctn_confirm_planting: {
    width: "95%",
    padding: "20@ms",
    borderRadius: 30,
    borderWidth: 10,
    borderColor: BgColor.Bg_A3BF59,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_BottomInfrom: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    bottom: "20@ms",
    position: "absolute",
    right: 0,
    left: 0,
  },
  ctn_loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  ctn_deletListWater: {
    width: "100%",
    marginTop: "10@ms",
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  ctn_payMentStutas: {
    width: "100%",
    // marginTop: "10@ms",
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  ctn_noti: {
    left: 15,
    bottom: 10,
    zIndex: 9999,
    width: "24@ms",
    height: "24@ms",
    borderRadius: 180,
    backgroundColor: "red",
    position: "absolute",
    borderColor: BgColor.Bg_FFFFFF,
    borderWidth: 2,
  },
  ctn_areaMinmax: {
    borderRadius: 20,
    paddingVertical: "5@ms",
    paddingHorizontal: 20,
    backgroundColor: BgOpacity.Op_0000005,
  },
  ctn_txtheader: {
    paddingTop: "10@ms",
    paddingBottom: "10@ms",
    paddingHorizontal: "20@ms",
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_leftAction: {
    padding: "30@ms",
    borderTopWidth: 1,
    borderBottomWidth: 1,
    justifyContent: "center",
    borderTopColor: BgColor.Bg_F4F4F4,
    backgroundColor: BgColor.Bg_C2E2CC,
    borderBottomColor: BgColor.Bg_F4F4F4,
  },
  ctn_rightAction: {
    padding: "30@ms",
    borderTopWidth: 1,
    borderBottomWidth: 1,
    justifyContent: "center",
    borderTopColor: BgColor.Bg_F4F4F4,
    backgroundColor: BgColor.Bg_ffad32,
    borderBottomColor: BgColor.Bg_F4F4F4,
  },
  ctn_contentNoti: {
    padding: "20@ms",
    borderTopWidth: 1,
    borderBottomWidth: 1,
    flexDirection: "row",
    borderTopColor: BgColor.Bg_F4F4F4,
    borderBottomColor: BgColor.Bg_F4F4F4,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_checkRead: {
    top: "5@ms",
    left: "8@ms",
    zIndex: 9999,
    position: "absolute",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  ctn_imgNoti: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_txtContent: {
    width: "80%",
    flexDirection: "column",
  },
  ctn_keyboardcomment: {},
  ctn_iconfinish: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_EEFBF1,
  },
  ctn_iconWaterList: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_679290,
  },
  ctn_Cultivation: {
    width: "50%",
    height: "40@ms",
    borderRadius: 180,
  },
  ctn_menuProfile: {
    padding: "10@ms",
    paddingTop: "50@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_notifarm: {
    top: "200@ms",
    zIndex: 1,
    left: "1@ms",
    borderRadius: 180,
    paddingVertical: 2,
    paddingHorizontal: 8,
    position: "absolute",
    backgroundColor: "red",
  },
  ctn_nontiFarmRow: {
    zIndex: 1,
    right: "25@ms",
    top: "0@ms",
    borderRadius: 180,
    paddingVertical: 2,
    paddingHorizontal: 8,
    position: "absolute",
    backgroundColor: "red",
  },
  ctn_checkAreaFalse: {
    top: 10,
    right: 10,
    bottom: 0,
    zIndex: 999,
    position: "absolute",
    justifyContent: "center",
  },
  ctn_fullImages: {
    bottom: 20,
    width: "100%",
    alignItems: "center",
    position: "absolute",
  },
  ctn_OutInfom: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  ctn_bottomSavePlant: {
    bottom: "20@ms",
    position: "absolute",
    alignItems: "center",
    right: 0,
    left: 0,
  },
  ctn_contentProduct: {
    borderRadius: 26,
    alignItems: "center",
    justifyContent: "center",
    width: isTablet ? screenWidth * 0.275 : screenWidth * 0.26,
    height: isTablet ? screenHeight * 0.25 : screenHeight * 0.17,
    backgroundColor: BgColor.Bg_FFF1D8,
  },
  ctn_bottomEditArea: {
    bottom: Platform.OS === "ios" ? "30@ms" : "15@ms",
    position: "absolute",
    alignItems: "center",
    right: 0,
    left: 0,
  },
  ctn_listManageFarm: {
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
    marginTop: "10@ms",
    borderRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  ctn_imgManageFarm: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  ctn_detailMangeFarm: {
    height: screenWidth * 0.65,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: BgColor.Bg_FFFFFF,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  ctn_PlusAddress: {
    paddingHorizontal: "20@ms",
    paddingVertical: "20@ms",
    backgroundColor: BgColor.Bg_F4F4F4,
    borderRadius: 15,
  },
  ctn_noImgProduct: {
    width: "30@ms",
    height: "30@ms",
    alignItems: "center",
  },
  ctn_moreProduct: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  ctn_listAllImg: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 10,
  },
  ctn_contentAddress: {
    borderRadius: 10,
    padding: "5@ms",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  ctn_footerAddress: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: "20@ms",
  },
  ctn_headerOpeeration: {
    zIndex: 2,
    height: Platform.OS === "ios" ? windowHeight * 0.12 : windowHeight * 0.11,
    width: "100%",
    position: "absolute",
    backgroundColor: BgOpacity.Op_D9D9D98,
  },
  ctn_uploadGallery: {
    flex: 1,
    // marginTop: 80,
    alignItems: "center",
    justifyContent: "center",
    // minHeight: Dimensions.get("window").height,
    marginTop: Platform.OS === "ios" ? windowHeight * 0.1 : windowHeight * 0.1,
  },
  ctn_headerManage: {
    // paddingVertical: "20@ms",
    borderBottomLeftRadius: 26,
    borderBottomRightRadius: 26,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  ctn_headerTitle: {
    paddingHorizontal: "10@ms",
    marginTop: windowHeight * 0.075,
  },
  ctn_headerTitleLan: {
    paddingHorizontal: "10@ms",
    marginTop: "20@ms",
    // marginTop:
    //   Platform.OS === "ios" ? windowHeight * 0.050 : windowHeight * 0.045,
  },
  ctn_selectManage: {
    width: "100%",
    padding: 10,
    borderRadius: 180,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_selectManageMenu: {
    width: "100%",
    padding: 10,
    borderRadius: 180,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_goBackPayandRe: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop:
      Platform.OS === "ios" ? windowHeight * 0.06 : windowHeight * 0.02,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_titleHeaderManage: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: BgColor.Bg_EEF5F1,
    padding: "10@ms",
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  ctn_noDataAdmin: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: "10@ms",
  },
  ctn_listManage: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: "10@ms",
  },
  ctn_listProcess: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: "10@ms",
  },
  ctn_bgListNote: {
    padding: "20@ms",
    marginTop: "10@ms",
    flexDirection: "row",
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  ctn_bgFarmNameuser: {
    borderRadius: 8,
    paddingVertical: "4@ms",
    paddingHorizontal: "8@ms",
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  ctn_detailDeliveryTitle: {
    paddingHorizontal: "20@ms",
    paddingVertical: "10@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  ctn_menuMaket: {
    zIndex: 999,
    marginTop: "10@ms",
    paddingHorizontal: "10@ms",
  },
  ctn_selectTapBuySell: {
    zIndex: 0,
    top: -40,
    backgroundColor: BgColor.Bg_679290,
    paddingVertical: "10@ms",
  },
  ctn_saveQr: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  ctn_bookMark: {
    top: 0,
    position: "absolute",
    zIndex: 9999,
  },
  ctn_columnArea: {
    flexDirection: "column",
    padding: "5@ms",
    justifyContent: "center",
  },
  ctn_chat: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    backgroundColor: BgColor.Bg_FFFFFF
  },
});

export default ctn;
