import {
  BgColor,
  BgOpacity,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
} from "../styleSheet/style_Custom";
import { Dimensions, Platform } from "react-native";
import { ScaledSheet } from "react-native-size-matters";
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;

const img = ScaledSheet.create({
  img_Mefarm: {
    width: "250@s",
  },
  img_Rabbit: {
    width: "250@s",
    height: "250@vs",
  },
  img_RabbitSignUp: {
    width: "250@s",
    height: "200@vs",
  },
  img_FaceBook: {
    width: "30@s",
    height: "30@vs",
  },
  img_GooGle: {
    width: "25@s",
    height: "25@vs",
  },
  img_Apple: {
    width: "25@s",
    height: "25@vs",
  },
  img_profile: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
  },
  img_profileFollow: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
  },
  img_cover: {
    width: "120@s",
    height: "80@ms",
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  img_coverNon: {
    width: "120@s",
    height: "80@ms",
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  img_imagePost: {
    width: "100%",
    height: 295,
    overflow: "hidden",
  },
  img_imageEdit: {
    width: "100%",
    height: "180@vs",
    overflow: "hidden",
    marginTop: "2@ms",
  },
  vid_videoPost: {
    width: "100%",
    height: "200@vs",
    borderRadius: 8,
    overflow: "hidden",
  },
  img_iconMuted: {
    right: "20@ms",
    padding: "5@ms",
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_0000005,
  },
  img_iconFullScreen: {
    right: "10@ms",
    padding: "5@ms",
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_0000005,
  },
  img_posting: {
    width: "100@s",
    aspectRatio: 1,
    borderRadius: 16,
  },
  img_postComment: {
    width: "100@s",
    height: "100@ms",
    aspectRatio: 1,
    borderRadius: 16,
  },
  vid_posting: {
    width: "100%",
    height: 250,
    aspectRatio: 1,
    borderRadius: 16,
    overflow: "hidden",
  },
  img_videoComment: {
    width: "100@s",
    height: "100@ms",
    aspectRatio: 1,
    borderRadius: 16,
  },
  img_fullimage: {
    width: "100%",
    height: "100%",
  },
  img_profileComment: {
    width: "30@ms",
    height: "30@ms",
    borderRadius: 180,
  },
  vid_vidPost: {
    width: "100%",
    height: "200@vs",
    borderRadius: 8,
    overflow: "hidden",
  },
  vid_vidEdit: {
    width: "100%",
    height: "180@vs",
    // borderRadius: 8,
    overflow: "hidden",
    marginTop: "2@ms",
  },
  img_coverProfile: {
    width: "100%",
    height: "200@ms",
  },
  img_coverProfileFriend: {
    width: "100%",
    height: "150@ms",
  },
  img_mainProfile: {
    width: "106@ms",
    height: "106@ms",
    borderRadius: 180,
  },
  img_profileModal: {
    width: "320@ms",
    height: "320@ms",
    borderRadius: 180,
  },
  img_coverModal: {
    width: "100%",
    height: "180@ms",
    position: "absolute",
  },
  img_nonArea: {
    width: "195@ms",
    height: "195@ms",
  },
  img_MyFarm: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 24,
  },
  img_MobilePay: {
    width: "30@ms",
    height: "30@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_hourGlass: {
    width: "25@ms",
    height: "25@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_giftStutes: {
    width: "25@ms",
    height: "25@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_Myseeds: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
  },
  img_PromptPay: {
    width: "40@ms",
    height: "20@ms",
  },
  img_iconSearc: {
    right: "20@ms",
    bottom: "12@ms",
    position: "absolute",
  },
  img_selectArea: {
    bottom: "5@ms",
    borderRadius: 16,
    width: windowWidth * 0.4,
    height: windowHeight * 0.23,
  },
  img_selectAreaNon: {
    width: windowWidth * 0.2,
    height: windowHeight * 0.1,
  },
  img_gift: {
    right: 0,
    bottom: 0,
    zIndex: 0,
    width: "50%",
    height: "100%",
    position: "absolute",
  },
  img_qrcode: {
    // width: windowWidth * 0.7,
    width: windowWidth,
    height: windowHeight * 0.55,
  },
  img_bankUpload: {
    zIndex: 0,
    marginTop: "20@ms",
    width: "100%",
    height: windowHeight * 0.55,
  },
  img_plans: {
    width: 32,
    height: 32,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_plansNon: {
    width: 32,
    height: 32,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_finish: {
    width: "34@ms",
    height: "34@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_Checkfinish: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  img_iconEllipse: {
    top: "5@ms",
    left: "5@ms",
    width: "10@s",
    height: "10@vs",
    position: "absolute",
  },
  img_iconEllipse2: {
    top: "5@ms",
    right: "5@ms",
    width: "10@s",
    height: "10@vs",
    position: "absolute",
  },
  img_iconEllipseStatus: {
    top: "2@ms",
    left: "2@ms",
    width: "10@s",
    height: "10@vs",
    position: "absolute",
  },
  img_controlBottom: {
    width: "25@ms",
    height: "25@ms",
  },
  img_succed: {
    width: "450@ms",
    height: "450@ms",
  },
  img_comment: {
    width: "100%",
    height: "150@ms",
    borderRadius: 20,
  },
  img_ellipseManage: {
    top: "10@ms",
    left: "10@ms",
    width: "15@s",
    height: "15@vs",
    position: "absolute",
  },
  img_ellipseContal: {
    top: "10@ms",
    left: "10@ms",
    width: "12@s",
    height: "12@vs",
    position: "absolute",
  },
  img_manage: {
    top: "10@ms",
    width: "40@ms",
    height: "40@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_wood: {
    alignItems: "center",
    justifyContent: "center",
    resizeMode: "cover",
  },
  img_tools1: {
    width: "150@s",
    height: "150@vs",
  },
  img_timeWater: {
    width: "150@ms",
    height: "150@ms",
  },
  img_screenClose: {
    alignItems: "center",
    marginTop: "8@ms",
  },
  img_Ellipse: {
    top: "5@ms",
    left: "5@ms",
    width: "10@s",
    height: "10@vs",
    position: "absolute",
  },
  img_iconDelete: {
    padding: "15@ms",
    borderTopLeftRadius: 25,
    borderBottomRightRadius: 20,
    backgroundColor: BgColor.Bg_FF6F6F,
  },
  img_iconDeleteArea: {
    // padding: "10@ms",
    width: "35@ms",
    height: "35@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF6F6F,
  },
  img_iconPinAreaNon: {
    // padding: "10@ms",
    width: "35@ms",
    height: "35@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_iconPinArea: {
    // padding: "10@ms",
    width: "35@ms",
    height: "35@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_92c4ff,
  },
  img_PalnsInFrom: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_imgHeaer: {
    zIndex: 1,
    width: "40@ms",
    height: "40@ms",
    position: "absolute",
  },
  img_Info: {
    zIndex: 1,
    top: "5@ms",
    left: "5@ms",
    width: "30@ms",
    height: "30@ms",
    position: "absolute",
  },
  img_Heaer: {
    zIndex: 1,
    width: "40@ms",
    height: "40@ms",
    position: "absolute",
  },
  img_noImage: {
    width: "45@ms",
    height: "45@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_Manage: {
    top: "10@ms",
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_Product: {
    width: "45@ms",
    height: "45@ms",
    borderRadius: 180,
  },
  img_Stutes: {
    top: "10@ms",
    width: "35@ms",
    height: "35@ms",
  },
  img_RenderPlans: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_imgNoti: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  img_chckeAreaFalse: {
    width: "35@ms",
    height: "35@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_removeArea: {
    width: "30@ms",
    height: "30@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_mapSelect: {
    width: "35@ms",
    height: "35@ms",
    alignItems: "center",
    justifyContent: "center",
  },
  img_outOfStock: {
    width: "110@ms",
    height: "110@ms",
  },
  img_listManageFram: {
    width: "50@ms",
    height: "50@ms",
  },
  img_imgProduct: {
    width: "50@ms",
    height: "50@ms",
  },
  img_approve: {
    zIndex: 999,
    alignItems: "flex-end",
    position: "absolute",
    right: 10,
    marginTop:
      Platform.OS === "ios" ? windowHeight * 0.075 : windowHeight * 0.055,
  },
  img_noImageSlider: {
    // width,
    height: "250@ms",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  img_barOperation: {
    width: 30,
    height: 30,
  },
  img_barOperationNon: {
    width: 30,
    height: 30,
    opacity: 0.5,
  },
  img_markgroup: {
    width: 25,
    height: 25,
    marginTop: 10,
    right: 5,
  },
});

export default img;
