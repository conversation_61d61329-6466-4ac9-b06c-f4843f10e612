import {
  BgColor,
  BgOpacity,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
  LgColor,
} from "../styleSheet/style_Custom";
import DeviceInfo from "react-native-device-info";
import { Platform, Dimensions } from "react-native";
import { ScaledSheet } from "react-native-size-matters";
import { Table } from "lucide-react-native";
//Dimensions
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const { width: screenWidth } = Dimensions.get("window");
const { height: screenHeight } = Dimensions.get("window");
const isTablet = DeviceInfo.isTablet();

const oth = ScaledSheet.create({
  ling_loging: {
    height: 1,
    width: "100%",
    backgroundColor: BgColor.Bg_ECECEC,
  },
  opt_FlaseLoging: {
    width: "80@ms",
    height: "80@ms",
    bottom: "55@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bg_FlaseLoging: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  opt_TrueLoging: {
    width: "80@ms",
    height: "80@ms",
    bottom: "55@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_7bae6a5,
  },
  bg_TrueLoging: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_9BFF79,
  },
  opt_FlaseCancle: {
    width: "80@ms",
    height: "80@ms",
    bottom: "55@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bg_FlaseCancle: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  opt_Success: {
    width: "80@ms",
    height: "80@ms",
    bottom: "55@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_7bae6a5,
  },
  bg_Success: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_9BFF79,
  },
  card_ctp: {
    width: "100%",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  care_HorizonTal: {
    margin: "2@ms",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_Vartical: {
    marginTop: "1@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_MenuDot: {
    height: null,
    borderWidth: 1,
    width: "150@s",
    borderRadius: 8,
    padding: "10@ms",
    marginTop: "25@ms",
    position: "absolute",
    justifyContent: "center",
    borderColor: BorderColor.Bd_F4F4F4,
    backgroundColor: BgColor.Bg_FFFFFF,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  card_varticalPlus: {
    marginTop: "20@ms",
    borderWidth: 1,
    borderRadius: 180,
    paddingVertical: "8@ms",
    paddingHorizontal: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
    borderColor: BorderColor.Bd_F4F4F4,
  },
  lindTimeFull: {
    right: "80@ms",
    bottom: "60@ms",
    padding: "5@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgOpacity.Op_0000005,
  },
  cardKeyBord: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  cardKeyBordChat: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cardInputKeyBord: {
    width: "100%",
    borderRadius: 20,
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
  },
  cardInputKeyBordChat: {
    width: "90%",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
  },
  bottonCanCel_Comment: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  bottonSave_Comment: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  cardEditDelete: {
    right: "2%",
    top: "5@ms",
    paddingVertical: "11@ms",
    paddingHorizontal: "12@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgOpacity.Op_E74C3C05,
  },
  cardEditProcess: {
    // right: 10,
    top: "5@ms",
    left: (screenWidth - 200) / 3,
    paddingVertical: "11@ms",
    paddingHorizontal: "12@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgOpacity.Op_E74C3C05,
  },
  line_profile: {
    width: "100%",
    height: 1,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  line_vatical: {
    width: 1,
    borderRadius: 180,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  radio_Language: {
    width: "10@ms",
    height: "10@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  cardPludArea: {
    borderRadius: 8,
    alignItems: "center",
    paddingVertical: "8@ms",
    justifyContent: "center",
    paddingHorizontal: "8@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  cardIconArea: {
    borderRadius: 13,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  tap_selectArea: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 10,
    borderWidth: 1.5,
    alignItems: "center",
    justifyContent: "center",
    borderColor: BorderColor.Bd_B3DBC0,
    backgroundColor: BgColor.Bg_B3DBC0,
  },
  tap_selectAreaNon: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 10,
    borderWidth: 1.5,
    alignItems: "center",
    justifyContent: "center",
    borderColor: BorderColor.Bd_B3DBC0,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardSead_area: {
    height: "40@ms",
    borderRadius: 180,
    marginTop: "10@ms",
    flexDirection: "row",
    backgroundColor: BgOpacity.OP_b3dbc02,
  },
  animated_selectArea: {
    alignItems: "center",
    justifyContent: "center",
    width: windowWidth * 0.45,
    height: windowHeight * 0.25,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  card_detailArea: {
    width: "100%",
    bottom: "4@ms",
    padding: "5@ms",
    alignItems: "center",
    position: "absolute",
    flexDirection: "row",
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    justifyContent: "space-between",
    backgroundColor: BgOpacity.Op_b3dbc07,
  },
  card_nextArea: {
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    width: windowWidth * 0.11,
    height: windowHeight * 0.05,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_detailRai: {
    width: "40@ms",
    height: "40@ms",
    marginTop: "20@ms",
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_E3FFEC,
  },
  card_detailRaiNon: {
    width: "40@ms",
    height: "40@ms",
    marginTop: "20@ms",
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_status: {
    padding: "10@ms",
    borderRadius: 40,
    backgroundColor: BgOpacity.Op_8080808,
  },
  card_statusNon: {
    padding: "10@ms",
    borderRadius: 40,
    backgroundColor: BgOpacity.Op_bdbdbd9,
  },
  card_poltArea: {
    marginTop: "10@ms",
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  card_poltAreaSelect: {
    marginTop: "10@ms",
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  card_poltAreaNon: {
    marginTop: "10@ms",
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_FF9900,
  },
  card_cancleVetGet: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  card_celectVetGet: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  card_celectVetGetNon: {
    borderRadius: 180,
    paddingVertical: "5@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  card_goBack: {
    borderRadius: 180,
    paddingVertical: "8@ms",
    paddingHorizontal: "9@ms",
    backgroundColor: BgOpacity.Op_ffffff5,
  },
  card_packAge: {
    marginTop: "10@ms",
    borderRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_control: {
    // marginBottom: "5@ms",
    borderRadius: 180,
    alignItems: "center",
  },
  card_farmDetail: {
    marginTop: "20@ms",
    paddingHorizontal: "20@ms",
  },
  card_PH: {
    borderRadius: 16,
    paddingHorizontal: "10@ms",
    paddingVertical: "20@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  card_numPH: {
    borderRadius: 30,
    padding: "10@ms",
    backgroundColor: BgColor.Bg_B3DBC0,
  },
  line_Strip: {
    width: "5@ms",
    marginHorizontal: "3@ms",
    borderRadius: 30,
  },
  card_nameFarm: {
    width: "200@ms",
    padding: "10@ms",
    paddingVertical: "10@ms",
    borderRadius: "26@ms",
    alignItems: "center",
    overflow: "visible",
    marginTop: windowHeight * 0.075,
    backgroundColor: BgOpacity.Op_C0D576,
  },
  card_nameFarmLan: {
    width: "200@ms",
    padding: "10@ms",
    paddingVertical: "10@ms",
    borderRadius: "26@ms",
    alignItems: "center",
    overflow: "visible",
    marginTop: "20@ms",
    right: isTablet ? "10%" : 0,
    backgroundColor: BgOpacity.Op_C0D576,
  },
  card_InPutEditName: {
    width: "200@ms",
    padding: "10@ms",
    paddingVertical: Platform.OS === "ios" ? 10 : 0,
    borderRadius: "26@ms",
    marginTop: windowHeight * 0.075,
    backgroundColor: BgOpacity.Op_C0D576,
  },
  card_InPutEditNameLan: {
    width: "200@ms",
    right: isTablet ? "10%" : 0,
    paddingHorizontal: 20,
    paddingVertical: Platform.OS === "ios" ? 10 : 0,
    borderRadius: 26,
    marginTop: "20@ms",
    backgroundColor: BgOpacity.Op_C0D576,
  },
  paymentIcon: {
    padding: "10@ms",
    borderRadius: 20,
    paddingVertical: "5@ms",
    paddingHorizontal: "11@ms",
    backgroundColor: BgColor.Bg_B3DBC0,
  },
  boxSelectDay: {
    width: "22@s",
    height: "22@vs",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  boxDay: {
    width: "22@s",
    height: "22@vs",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  lineInFrom: {
    borderWidth: 0.5,
    borderStyle: "dashed",
    borderColor: BgColor.Bg_B3DBC0,
  },
  clickPlans: {
    width: screenWidth * 0.304,
    height: screenHeight * 0.19,
    margin: 1,
    borderRadius: 30,
    alignItems: "center",
  },
  clickPlansLan: {
    width: screenWidth * 0.304,
    height: screenHeight * 0.19,
    margin: 1,
    borderRadius: 30,
    alignItems: "center",
  },
  checkProduct: {
    borderRadius: 20,
    width: screenWidth * 0.28,
    paddingVertical: "10@ms",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  boxSelect: {
    width: "80@ms",
    height: "28@ms",
    borderWidth: 2,
    borderRadius: 180,
    borderColor: BgColor.Bg_FFEA61,
  },
  canCelClose: {
    borderRadius: 180,
    paddingVertical: "10@ms",
    paddingHorizontal: "40@ms",
    backgroundColor: BgColor.Bg_A1BD57,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  finishIcon: {
    width: 60,
    height: 60,
    borderRadius: 180,
    backgroundColor: BgColor.Bg_B3DBC0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  line_notiNon: {
    width: "100%",
    borderWidth: 1,
    borderColor: BgColor.Bg_F4F4F4,
  },
  mutedVideo: {
    position: "absolute",
    right: 10,
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 8,
    top: 10,
    borderRadius: 50,
  },
  mutedVideoProfile: {
    position: "absolute",
    right: 0,
    top: 5,
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 10,
    borderRadius: 50,
  },
  plusNumber: {
    paddingHorizontal: "10@ms",
    paddingVertical: "2@ms",
    backgroundColor: "#F4F4F4",
    borderRadius: 20,
  },
  cardProduct: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardIconUpProduct: {
    width: "15%",
    borderRadius: 20,
    alignItems: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  lineDelivery: {
    width: "100%",
    height: "4@vs",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  listAllImg: {
    // width: "30%",
    // aspectRatio: 1,
    alignItems: "center",
    margin: "5@ms",
  },
  contentAddress: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "10@ms",
  },
  shadowBox: {
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
    borderRadius: 10,
    // elevation: 3,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 1 },
    // shadowOpacity: 0.22,
    // shadowRadius: 2.22,
  },
  input_address: {
    height: "40@vs",
    padding: "10@ms",
    borderRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  provinceall: {
    height: 35,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  addressUser: {
    padding: Platform.OS === "ios" ? "10@ms" : "5@ms",
    borderRadius: 10,
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  provinceData: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  continueData: {
    backgroundColor: BgColor.Bg_FFFFFF,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  dotMapSelect: {
    width: "10@ms",
    height: "10@ms",
    backgroundColor: BgColor.Bg_84B8A2,
    borderRadius: 180,
  },
  dotMap: {
    width: "10@ms",
    height: "10@ms",
    backgroundColor: BgColor.Bg_A5A5A5,
    borderRadius: 180,
  },
  continueSearch: {
    marginTop: "10@ms",
    paddingHorizontal: "20@ms",
  },
  continueSearchManage: {
    paddingHorizontal: "20@ms",
    marginTop: "20@ms",
  },
  continueSearchHistory: {
    paddingHorizontal: "20@ms",
    marginTop: "10@ms",
    marginBottom: "10@ms",
  },
  contentProvinces: {
    backgroundColor: BgColor.Bg_FFFFFF,
    borderRadius: 10,
  },
  contentSearch: {
    borderRadius: 10,
    paddingVertical: Platform.OS === "ios" ? "10@ms" : "0@ms",
    paddingHorizontal: "8@ms",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  contentSearchManage: {
    borderRadius: 10,
    paddingVertical: Platform.OS === "ios" ? "10@ms" : "0@ms",
    paddingHorizontal: "8@ms",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  contentSearchHistory: {
    borderRadius: 10,
    paddingVertical: Platform.OS === "ios" ? "10@ms" : "0@ms",
    paddingHorizontal: "8@ms",
    backgroundColor: BgColor.Bg_F4F4F4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  lineVitical: {
    width: 1,
    height: 20,
    backgroundColor: BgColor.Bg_A5A5A5,
    marginLeft: 5,
  },
  editFramName: {
    left: "5@ms",
    bottom: Platform.OS === "ios" ? "1@ms" : "4@ms",
    position: "absolute",
    zIndex: 1,
  },
  saveEditFramName: {
    right: "5@ms",
    bottom: Platform.OS === "ios" ? "1@ms" : "4@ms",
    position: "absolute",
    zIndex: 9999,
  },
  goBackOperation: {
    zIndex: 888,
    position: "absolute",
    paddingHorizontal: "60@ms",
    marginTop:
      Platform.OS === "ios" ? windowHeight * 0.07 : windowHeight * 0.06,
  },
  goOperation: {
    zIndex: 888,
    position: "absolute",
    right: 10,
    marginTop:
      Platform.OS === "ios" ? windowHeight * 0.07 : windowHeight * 0.06,
  },
  cardPhNumber: {
    flexDirection: "row",
    backgroundColor: BgColor.Bg_B3DBC0,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 180,
  },
  progressmoisrure: {
    width: 10,
    height: 80,
    backgroundColor: "#E0E0E0",
    borderRadius: 5,
    zIndex: 999,
    left: 10,
    position: "absolute",
    overflow: "hidden",
  },
  phStarp: {
    top: -2,
    position: "absolute",
    transform: [{ translateX: -10 }],
  },
  phStarpDown: {
    bottom: -2,
    position: "absolute",
    transform: [{ translateX: -10 }],
  },
  progressM: {
    bottom: 0,
    width: "100%",
    borderRadius: 5,
    position: "absolute",
    backgroundColor: "#515151",
  },
  cardTapManage: {
    padding: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    backgroundColor: BgColor.Bg_346359,
  },
  cardTapManage2: {
    padding: 10,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    backgroundColor: BgColor.Bg_346359,
  },
  cardTapManageNon: {
    padding: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardTapManageNon2: {
    padding: 10,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardTapMenu: {
    padding: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  cardTapMenu2: {
    padding: 10,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  cardTapMenuNon: {
    padding: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardTapMenuNon2: {
    padding: 10,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  bgStyles1: {
    width: 65,
    height: 250,
    position: "absolute",
    top: "10%",
    bottom: 0,
    left: "14%",
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
    transform: [{ rotate: "25deg" }],
  },
  bgStyles2: {
    width: 65,
    height: 250,
    position: "absolute",
    top: "35%",
    bottom: 0,
    left: "22%",
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
    transform: [{ rotate: "25deg" }],
  },
  bgStyles3: {
    width: 80,
    height: 80,
    position: "absolute",
    top: "15%",
    bottom: 0,
    right: "25%",
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
    alignItems: "center",
    justifyContent: "center",
  },
  bgStyles4: {
    width: 50,
    height: 50,
    position: "absolute",
    bottom: "20%",
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
  },
  bgStyles5: {
    width: 25,
    height: 200,
    position: "absolute",
    top: "35%",
    right: 0,
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
    transform: [{ rotate: "-55deg" }],
  },
  bgStyles6: {
    width: 25,
    height: 250,
    position: "absolute",
    top: "35%",
    right: 0,
    zIndex: 0,
    borderRadius: 180,
    backgroundColor: BgOpacity.Op_b3dbc03,
    transform: [{ rotate: "-55deg" }],
  },
  cardApprove: {
    padding: 20,
    zIndex: 999,
    // borderRadius: 20,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  cardSliderApprove: {
    zIndex: 0,
    bottom: 15,
    padding: 20,
    paddingVertical: 30,
    // borderBottomEndRadius: 20,
    // borderBottomStartRadius: 20,
    backgroundColor: BgColor.Bg_679290,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  bgStatus: {
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
  },
  bgStatusPackage: {
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_E5EFE1,
  },
  bgStatusArea: {
    paddingHorizontal: "10@ms",
    paddingVertical: "5@ms",
    borderRadius: 180,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  cardInputManage: {
    width: "100%",
    backgroundColor: BgColor.Bg_FFFFFF,
    paddingVertical: Platform.OS === "ios" ? 8 : 2,
    paddingHorizontal: Platform.OS === "ios" ? 10 : 5,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  cardStatusManage: {
    borderRadius: 5,
    borderWidth: 1,
    paddingHorizontal: 10,
    justifyContent: "center",
    borderColor: BgColor.Bg_E74C3C,
    backgroundColor: BgColor.Bg_FFEEEE,
  },
  cardStatusSuccess: {
    borderRadius: 5,
    borderWidth: 1,
    paddingHorizontal: 10,
    justifyContent: "center",
    borderColor: BgColor.Bg_7BAE6A,
    backgroundColor: BgColor.Bg_E5EFE1,
  },
  cardStatusManageNew: {
    borderRadius: 5,
    borderWidth: 1,
    paddingHorizontal: 10,
    justifyContent: "center",
    borderColor: BgColor.Bg_7FBDF6,
    backgroundColor: BgColor.Bg_E3F2FF,
  },
  cardDrowdown: {
    height: 50,
    padding: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  bgStatusProcess: {
    width: 20,
    height: 20,
    borderRadius: 4,
    marginRight: 10,
  },
  cardBottonProcess: {
    width: 60,
    height: 60,
    right: 20,
    bottom: 30,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  cardBottonDeleteProcess: {
    width: 60,
    height: 60,
    right: 90,
    bottom: 20,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgColor.Bg_FF6F6F,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  cardBottonDelivery: {
    width: "90%",
    bottom: "20@ms",
    borderRadius: 180,
    paddingVertical: "15@ms",
    position: "absolute",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  cardBottonDeliveryNon: {
    width: "90%",
    bottom: "20@ms",
    borderRadius: 180,
    paddingVertical: "15@ms",
    position: "absolute",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_D6D6D6,
  },
  cardMenuMaket: {
    padding: "10@ms",
    backgroundColor: BgColor.Bg_84B8A2,
    borderRadius: 180,
  },
  cardBottonMaket: {
    paddingHorizontal: "40@ms",
    paddingVertical: "12@ms",
    borderRadius: 180,
  },
  cardBottonTapBuySell: {
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  cardHistoryDelivery: {
    padding: 8,
    borderRadius: 5,
    backgroundColor: BgColor.Bg_84B8A2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  bageNoti: {
    position: "absolute",
    zIndex: 9999,
    left: 25,
    top: 0,
  },
  bageNotiAdmin: {
    position: "absolute",
    zIndex: 9999,
    right: 5,
    top: -5,
  },
  bageNotiAdminTap: {
    position: "absolute",
    zIndex: 9999,
    right: 0,
    top: -10,
  },
  loadingPostmedai: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  Indicator: {
    left: 0,
    right: 0,
    bottom: 10,
    position: "absolute",
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  IndicatorCheck: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  bageNotiLan: {
    position: "absolute",
    zIndex: 9999,
    right: 0,
  },
  bgIotA5FAFF: {
    backgroundColor: BgColor.Bg_A5FAFF,
  },
  bgIotFFC581: {
    backgroundColor: BgColor.Bg_FFC581,
  },
  bgIotFDFFB4: {
    backgroundColor: BgColor.Bg_FDFFB4,
  },
  bgIotAECAFF: {
    backgroundColor: BgColor.Bg_AECAFF,
  },
  bgIotFEB7B1: {
    backgroundColor: BgColor.Bg_FEB7B1,
  },
  bgIotFFDAC0: {
    backgroundColor: BgColor.Bg_FFDAC0,
  },
});

export default oth;
