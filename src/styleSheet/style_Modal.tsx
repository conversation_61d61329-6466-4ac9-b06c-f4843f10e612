import { StyleSheet } from "react-native";
import { ScaledSheet } from "react-native-size-matters";
//StyleSheet
import { BgColor, BgOpacity, FonstColor } from "../styleSheet/style_Custom";

const stylesModal = ScaledSheet.create({
  //Loging
  centeredView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_0000005,
  },
  modalView: {
    backgroundColor: "white",
    borderRadius: 50,
    paddingHorizontal: "40@ms",
    paddingVertical: "20@ms",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    padding: "10@ms",
    elevation: 2,
    borderRadius: 180,
  },
  buttonClose: {
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  buttonTrue: {
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  textStyle: {
    textAlign: "center",
    color: FonstColor.Tc_000000,
  },
  modalTextFlase: {
    textAlign: "center",
    color: FonstColor.Tc_FF9900,
  },
  modalTextTitleTrue: {
    textAlign: "center",
    color: FonstColor.Tc_666666,
  },
  modalTextTrue: {
    textAlign: "center",
    color: FonstColor.Tc_64B548,
  },

  bgOpacityFlase: {
    width: "120@ms",
    height: "120@ms",
    bottom: "70@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bgOpacityTrue: {
    width: "120@ms",
    height: "120@ms",
    bottom: "70@s",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_7bae6a5,
  },
  bgInOpacityFlase: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  bgInOpacityTrue: {
    width: "80@ms",
    height: "80@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_9BFF79,
  },
  ImgSucceed: {
    width: "50@ms",
    height: "50@ms",
  },

  //Detail Area
  continuePackAge: {
    width: "90%",
    padding: 20,
    borderRadius: 40,
    backgroundColor: "white",
  },
  flexPackAge: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  flexPackDetail: {
    flexDirection: "row",
    justifyContent: "space-between",
  },

  //Plus Area
  bgOpacityPlus: {
    width: '80@ms',
    height: '80@ms',
    bottom: "25%",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgOpacity.Op_ff99005,
  },
  bgInOpacityPlus: {
    width: '50@ms',
    height: '50@ms',
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFD39E,
  },
  modalViewPlus: {
    backgroundColor: "white",
    borderRadius: 26,
    paddingHorizontal: '20@ms',
    paddingVertical: '10@ms',
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default stylesModal;
