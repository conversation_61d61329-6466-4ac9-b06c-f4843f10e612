import {
  BgColor,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
  BgOpacity,
} from "../styleSheet/style_Custom";
import { Dimensions, Platform } from "react-native";
import DeviceInfo from "react-native-device-info";
import { ScaledSheet } from "react-native-size-matters";
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const isTablet = DeviceInfo.isTablet();

const btn = ScaledSheet.create({
  btn_Login: {
    borderRadius: 180,
    paddingVertical: "14@vs",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_Register: {
    borderRadius: 180,
    paddingVertical: "15@vs",
    backgroundColor: BottonColor.Bt_ECECEC,
  },
  btn_SignUp: {
    bottom: "0@ms",
    borderRadius: 180,
    paddingVertical: "15@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_loginSocial: {
    padding: "10@ms",
    borderRadius: 10,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  btn_FlaseLoging: {
    elevation: 2,
    borderRadius: 180,
    padding: "10@ms",
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_FlaseCancle: {
    elevation: 2,
    borderRadius: 180,
    padding: "10@ms",
    paddingHorizontal: "90@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_selectPost: {
    width: "35@ms",
    height: "35@ms",
    borderWidth: 1,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
    borderColor: BorderColor.Bd_EAEAEA,
  },
    btn_selectPostDis: {
    width: "35@ms",
    height: "35@ms",
    borderWidth: 1,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_D6D6D6,
    borderColor: BorderColor.Bd_EAEAEA,
  },
  btn_Post: {
    borderRadius: 14,
    justifyContent: "center",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_679290,
  },
  btn_cancleComment: {
    borderRadius: 30,
    justifyContent: "center",
    paddingHorizontal: "10@ms",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_FollowHrzt: {
    bottom: "15@ms",
    width: "80@s",
    borderRadius: 20,
    alignItems: "center",
    paddingVertical: "5@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_UnFollowHrzt: {
    bottom: "15@ms",
    width: "80@s",
    borderRadius: 20,
    alignItems: "center",
    paddingVertical: "5@ms",
    backgroundColor: BottonColor.Bt_ECECEC,
  },
  btn_FollowHrztAll: {
    width: "80@s",
    borderRadius: 20,
    bottom: "5@ms",
    alignItems: "center",
    paddingVertical: "5@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_UnFollowHrztAll: {
    width: "80@s",
    borderRadius: 20,
    bottom: "5@ms",
    alignItems: "center",
    paddingVertical: "5@ms",
    backgroundColor: BottonColor.Bt_ECECEC,
  },
  btn_settingApp: {
    padding: 14,
    borderRadius: 14,
    backgroundColor: BgColor.Bg_F4F4F4,
  },
  btn_payMentNon: {
    width: "50@ms",
    height: "50@ms",
    marginRight: "10@ms",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BottonColor.Bt_FFFFFF,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  btn_payMentActive: {
    width: "50@ms",
    height: "50@ms",
    marginRight: "10@ms",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BottonColor.Bt_B3DBC0,
    borderWidth: 2,
    borderColor: BgColor.Bg_F4F4F4,
  },
  btn_conFirm: {
    width: "90%",
    borderRadius: 180,
    marginTop: "20@ms",
    paddingVertical: 15,
    paddingHorizontal: "20@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_selectArea: {
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    width: windowWidth * 0.2,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_nextDetailArea: {
    height: "40@ms",
    borderRadius: 180,
    width: windowWidth * 0.3,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_nextDetailAreaNon: {
    height: "40@ms",
    borderRadius: 180,
    opacity: 0.5,
    width: windowWidth * 0.3,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_costomGoback: {
    zIndex: 9999,
    position: "absolute",
    paddingHorizontal: "10@ms",
    marginTop: windowHeight * 0.075,
  },
  btn_costomGobackLan: {
    // zIndex: 9999,
    position: "absolute",
    paddingHorizontal: "10@ms",
    marginTop: "20@ms",
  },
  btn_goBackOperation: {
    zIndex: 9999,
    position: "absolute",
    paddingHorizontal: "10@ms",
    marginTop:
      Platform.OS === "ios" ? windowHeight * 0.055 : windowHeight * 0.045,
  },
  btn_costomGobackDetailPay: {
    zIndex: 9999,
    position: "absolute",
    paddingHorizontal: 10,
  },
  btn_selectPackage: {
    left: "75%",
    bottom: "10@ms",
    borderRadius: 180,
    position: "absolute",
    paddingVertical: "8@ms",
    paddingHorizontal: "30@ms",
    borderWidth: 4,
    borderColor: "white",
  },
  btn_selectButton: {
    left: "75%",
    bottom: "10@ms",
    borderRadius: 180,
    position: "absolute",
    paddingVertical: "8@ms",
    paddingHorizontal: "30@ms",
    borderWidth: 4,
    borderColor: "white",
  },
  btn_detailPay: {
    width: "90%",
    borderRadius: 180,
    position: "absolute",
    paddingVertical: "15@ms",
    paddingHorizontal: "20@ms",
    bottom: '20@ms',
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_detailPayandRe: {
    width: "90%",
    borderRadius: 180,
    position: "absolute",
    paddingVertical: "15@ms",
    paddingHorizontal: "20@ms",
    bottom: '20@ms',
    backgroundColor: BottonColor.Bt_7FBDF6,
  },
  btn_detailPayandReNon: {
    width: "90%",
    borderRadius: 180,
    opacity: 0.5,
    position: "absolute",
    paddingVertical: "15@ms",
    paddingHorizontal: "20@ms",
    bottom: '20@ms',
    backgroundColor: BottonColor.Bt_7FBDF6,
  },
  btn_detailPayNon: {
    width: "90%",
    borderRadius: 180,
    position: "absolute",
    paddingVertical: "15@ms",
    paddingHorizontal: "20@ms",
    bottom: 10,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_paylater: {
    borderRadius: 180,
    paddingVertical: "12@ms",
    paddingHorizontal: "20@ms",
    backgroundColor: BottonColor.Bt_DAEEDE,
  },
  btn_goUpload: {
    borderRadius: 180,
    paddingVertical: "12@ms",
    paddingHorizontal: "50@ms",
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_imgBankDelete: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 10,
    backgroundColor: BgColor.Bg_FF9900,
  },
  btn_uploadImgBank: {
    width: "90%",
    borderWidth: 3,
    padding: "40@ms",
    marginTop: "10%",
    borderRadius: 40,
    alignItems: "center",
    borderStyle: "dashed",
    justifyContent: "center",
    borderColor: BorderColor.Bd_BDBDBD,
  },
  btn_uploadProcess: {
    width: "100%",
    borderWidth: 3,
    padding: "40@ms",
    borderRadius: 40,
    alignItems: "center",
    borderStyle: "dashed",
    justifyContent: "center",
    borderColor: BorderColor.Bd_BDBDBD,
  },
  btn_bottomMenu: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_bottomMenuAdmin: {
    width: "50@ms",
    height: "50@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    // position: "absolute",
    // bottom: 100,
    backgroundColor: BgColor.Bg_84B8A2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  btn_bottomMenuNon: {
    width: "40@ms",
    height: "40@ms",
    opacity: 0.5,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_closeMod: {
    right: 0,
    zIndex: 999,
    top: "-25@ms",
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    position: "absolute",
  },
  btn_deleteArea: {
    zIndex: 9999,
    right: "-35@ms",
    bottom: "20@ms",
    padding: "10@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgColor.Bg_E74C3C,
  },
  btn_deleteAreaNon: {
    zIndex: 9999,
    right: "-35@ms",
    bottom: "8@ms",
    padding: "10@ms",
    borderRadius: 180,
    position: "absolute",
    backgroundColor: BgColor.Bg_E74C3C,
  },
  btn_cancle: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_selectDelete: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_FF9900,
  },
  btn_selectPictute: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_84B8A2,
  },
  btn_ctnPayment: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  btn_manageFarm: {
    // top: "15@ms",
    zIndex: 0,
    width: "70@s",
    // height: "90@s",
    alignItems: "center",
    borderTopLeftRadius: 180,
    borderTopRightRadius: 180,
  },
  btn_Stutes1: {
    width: "32%",
    borderRadius: 20,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_ffad32,
  },
  btn_Stutes2: {
    width: "32%",
    borderRadius: 20,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  btn_timeWater: {
    width: "140@ms",
    marginTop: "10@ms",
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: "8@ms",
    paddingHorizontal: "5@ms",
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  btn_selectedDay: {
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_unselectedDay: {
    borderWidth: 1,
    borderColor: BgColor.Bg_D6D6D6,
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  btn_dayButton: {
    width: "40@ms",
    height: "40@ms",
    margin: "2@ms",
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  btn_canCelYseNo: {
    borderRadius: 180,
    paddingVertical: "10@ms",
    paddingHorizontal: "40@ms",
    backgroundColor: BgColor.Bg_ffad32,
  },
  btn_conFirmYseNo: {
    borderRadius: 180,
    paddingVertical: "10@ms",
    paddingHorizontal: "40@ms",
    backgroundColor: BgColor.Bg_A1BD57,
  },
  btn_conFirmYseNoDis: {
    opacity: 0.5,
    borderRadius: 180,
    paddingVertical: "10@ms",
    paddingHorizontal: "40@ms",
    backgroundColor: BgColor.Bg_A1BD57,
  },
  btn_makePayment: {
    borderRadius: 180,
    width: "110@ms",
    height: "30@ms",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  btn_makePaymentNon: {
    borderRadius: 180,
    width: "110@ms",
    height: "30@ms",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_boxDetilIot: {
    width: "100@ms",
    height: "100@ms",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_bottonCancle: {
    width: "100@ms",
    height: "40@ms",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  btn_bottonAgree: {
    width: "100@ms",
    height: "40@ms",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_bottonReject: {
    width: "100@ms",
    height: "40@ms",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  btn_bottonDelete: {
    borderRadius: 180,
    paddingVertical: 10,
    paddingHorizontal: 30,
    backgroundColor: BottonColor.Bt_FF9900,
  },
  btn_cancelWater: {
    borderRadius: 180,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_FF6F6F,
  },
  btn_congirmDeleteWater: {
    borderRadius: 180,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_congirmDeleteWaterNon: {
    borderRadius: 180,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_EDEDED,
  },
  btn_bgPackage: {
    padding: 10,
    zIndex: 999,
    borderRadius: 20,
  },
  btn_saveButton: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_saveButtonNon: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    opacity: 0.5,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_boxBottonMenu: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
  },
  btn_cleanButton: {
    width: "40@ms",
    height: "40@ms",
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    // backgroundColor: "white",
  },
  btn_cleanButtonNon: {
    width: "40@ms",
    height: "40@ms",
    opacity: 0.5,
    borderRadius: 180,
    alignItems: "center",
    justifyContent: "center",
    // backgroundColor: "white",
  },
  btn_boxStutes: {
    width: "24%",
    borderRadius: 20,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_ffad32,
  },
  btn_boxStutesNon: {
    width: "24%",
    borderRadius: 20,
    paddingVertical: 5,
    backgroundColor: BgColor.Bg_DAEEDE,
  },
  btn_bottonListManageFarm: {
    width: "90%",
    bottom: "20@ms",
    borderRadius: 180,
    paddingVertical: "15@ms",
    alignItems: "center",
    position: "absolute",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_bottonListProduct: {
    width: "90%",
    bottom: "20@ms",
    borderRadius: 180,
    paddingVertical: "15@ms",
    alignItems: "center",
    position: "absolute",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_bottonListProductNon: {
    width: "90%",
    bottom: "20@ms",
    borderRadius: 180,
    opacity: 0.5,
    paddingVertical: "15@ms",
    alignItems: "center",
    position: "absolute",
    paddingHorizontal: "20@ms",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_checkApprove: {
    width: "30%",
    paddingVertical: 10,
    borderRadius: 20,
    alignItems: "center",
    backgroundColor: BgColor.Bg_7FBDF6,
  },
  btn_btnApproveNon: {
    width: "30%",
    borderRadius: 20,
    paddingVertical: 10,
    alignItems: "center",
    backgroundColor: BgColor.Bg_FFFFFF,
  },
  btn_btnApprove: {
    width: "30%",
    borderRadius: 20,
    paddingVertical: 10,
    alignItems: "center",
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_btnNoApprove: {
    width: "30%",
    borderRadius: 20,
    paddingVertical: 10,
    alignItems: "center",
    backgroundColor: BgColor.Bg_FF9900,
  },
  btn_btnManage: {
    padding: 10,
    borderRadius: 10,
    backgroundColor: BgColor.Bg_84B8A2,
  },
  btn_saveQr: {
    padding: 5,
    backgroundColor: BgColor.Bg_84B8A2,
    borderRadius: 180,
  },
});

export default btn;
