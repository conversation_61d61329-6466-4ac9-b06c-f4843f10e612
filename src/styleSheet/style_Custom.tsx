import { StyleSheet } from "react-native";
import { scale } from "react-native-size-matters";
//Fonst
export const FonstSize = {
  f8: scale(8),
  f10: scale(10),
  f12: scale(12),
  f14: scale(14),
  f16: scale(16),
  f18: scale(18),
  f20: scale(20),
  f22: scale(22),
  f24: scale(24),
  f30: scale(30),
  f32: scale(32),
};

export const FontFamily = {
  bold: "FCMinimalBold",
  light: "FCMinimalLight",
  medium: "FCMinimalMedium",
};

export const FonstColor = {
  Tc_FFFFFF: "#ffffff",
  Tc_616161: "#616161",
  Tc_606060: "#606060",
  Tc_E74C3C: "#E74C3C",
  Tc_FFD000: "#FFD000",
  Tc_FF9900: "#FF9900",
  Tc_4FA5F4: "#4FA5F4",
  Tc_84B8A2: "#84B8A2",
  Tc_6A938D: "#6A938D",
  Tc_679290: "#679290",
  Tc_346359: "#346359",
  Tc_C0D576: "#C0D576",
  Tc_64B548: "#64B548",
  Tc_00A900: "#00A900",
  Tc_6AB252: "#6AB252",
  Tc_2F4528: "#2F4528",
  Tc_A5A5A5: "#A5A5A5",
  Tc_A6A6A6: "#A6A6A6",
  Tc_666666: "#666666",
  Tc_000000: "#000000",
};

export const BgColor = {
  Bg_FFFFFF: "#ffffff",
  Bg_FBFCFC: "#FBFCFC",
  Bg_F4F4F4: "#F4F4F4",
  Bg_ECECEC: "#ECECEC",
  Bg_E3E3E3: "#E3E3E3",
  Bg_EDEDED: "#EDEDED",
  Bg_E1E7E0: "#E1E7E0",
  Bg_EEFBF1: "#EEFBF1",
  Bg_F2FFF5: "#F2FFF5",
  Bg_EEF5F1: "#EEF5F1",
  Bg_E8F8F5: "#E8F8F5",
  Bg_F1FFF4: "#F1FFF4",
  Bg_E5EFE1: "#E5EFE1",
  Bg_E3FFEC: "#E3FFEC",
  Bg_DAEEDE: "#DAEEDE",
  Bg_E5FFDD: "#E5FFDD",
  Bg_E2EBCC: "#E2EBCC",
  Bg_F2FFC2: "#F2FFC2",
  Bg_FFEEEE: "#FFEEEE",
  Bg_FFF4E3: "#FFF4E3",
  Bg_FFF1D8: "#FFF1D8",
  Bg_FFDAC0: "#FFDAC0",
  Bg_FEB7B1: "#FEB7B1",
  Bg_FFE1C3: "#FFE1C3",
  Bg_FFD39E: "#FFC59B",
  Bg_EEC175: "#EEC175",
  Bg_FFC581: "#FFC581",
  Bg_ffad32: "#ffad32",
  Bg_FF9900: "#FF9900",
  Bg_FF9934: "#FF9934",
  Bg_DE8B2A: "#DE8B2A",
  Bg_FF6F6F: "#FF6F6F",
  Bg_E74C3C: "#E74C3C",
  Bg_b59584: "#b59584",
  Bg_ffdf6b: "#ffdf6b",
  Bg_FFEA61: "#FFEA61",
  Bg_FDFFB4: "#FDFFB4",
  Bg_E1E9E9: "#E1E9E9",
  Bg_C7CEEA: "#C7CEEA",
  Bg_AECAFF: "#AECAFF",
  Bg_E3F2FF: "#E3F2FF",
  Bg_7FBDF6: "#7FBDF6",
  Bg_92c4ff: "#92c4ff",
  Bg_3498db: "#0000FF",
  Bg_A5FAFF: "#A5FAFF",
  Bg_7BAE6A: "#7BAE6A",
  Bg_9BFF79: "#9BFF79",
  Bg_C0D576: "#C0D576",
  Bg_A1BD57: "#A1BD57",
  Bg_A3BF59: "#A3BF59",
  Bg_C2E2CC: "#C2E2CC",
  Bg_B5EAD6: "#B5EAD6",
  Bg_B3DBC0: "#B3DBC0",
  Bg_84B8A2: "#84B8A2",
  Bg_64BAA8: "#64BAA8",
  Bg_6A938D: "#6A938D",
  Bg_679290: "#679290",
  Bg_316358: "#316358",
  Bg_346359: "#346359",
  Bg_0C4D3F: "#0C4D3F",
  Bg_D6D6D6: "#D6D6D6",
  Bg_A5A5A5: "#A5A5A5",
  Bg_666666: "#666666",
  Bg_000000: "#000000",
};

export const LgColor = {
  Lg_FFFFFF: "#FFFFFF",
  Lg_E7E7E7: "#E7E7E7",
  Lg_D6D6D6: "#D6D6D6",
  Lg_B0B0B0: "#B0B0B0",
  Lg_BDBDBD: "#BDBDBD",
  Lg_353535: "#353535",
  Lg_F2FFF5: "#F2FFF5",
  Lg_FFF1D8: "#FFF1D8",
  Lg_fff1d8: "#fff1d8",
  Lg_78994A: "#78994A",
  Lg_A1BD57: "#A1BD57",
  Lg_C0D576: "#C0D576",
  Lg_B8EFB2: "#B8EFB2",
  Lg_CFF1FF: "#CFF1FF",
  Lg_A0E3FF: "#A0E3FF",
  Lg_5ACEFF: "#5ACEFF",
  Lg_B8DFE6: "#B8DFE6",
  Lg_FFDAE1: "#FFDAE1",
  Lg_C5BBDE: "#C5BBDE",
  Lg_FFE2BF: "#FFE2BF",
  Lg_FFC581: "#FFC581",
  Lg_FFB155: "#FFB155",
  Lg_FF8A00: "#FF8A00",
  Lg_ec7063: "#ec7063",
  Lg_e74c3c: "#e74c3c",
  Lg_cb4335: "#cb4335",
};

export const linearDis = ["#D6D6D6", "#B0B0B0", "#BDBDBD"];
export const linearSky = ["#CFF1FF", "#A0E3FF", "#5ACEFF"];
export const linearPink = ["#ffdae1", "#ffdde4", "#ffe1e7"];
export const lineOrange = ["#FFC581", "#FFB155", "#FF8A00"];
export const linearGreen = ["#C0D576", "#A1BD57", "#78994A"];
export const linearPurple = ["#d0c8e4", "#cac1e1", "#c5bbde"];
export const linearDanger = ["#ec7063", "#e74c3c", "#cb4335"];

export const colorsPH = [
  "#FF5E5E",
  "#FF915E",
  "#FFB45E",
  "#FFDC5E",
  "#DFFF5E",
  "#CCFF5E",
  "#90EB56",
  "#7EEB56",
  "#56E86C",
  "#56E898",
  "#5EDFFF",
  "#5E91FF",
  "#7E5EFF",
  "#9C5EFF",
];

export const BgOpacity = {
  Op_0000005: "rgba(0,0,0,0.5)",
  Op_ff99005: "rgba(255,153,0,0.5)",
  Op_E74C3C05: "rgba(231,76,60,0.5)",
  Op_E74C3C03: "rgba(231,76,60,0.3)",
  Op_eecb2f2: "rgba(238, 203,47,0.3)",
  Op_b3dbc07: "rgba(179,219,192,0.7)",
  Op_b3dbc05: "rgba(179,219,192,0.5)",
  Op_8080808: "rgba(106,147,141,0.8)",
  Op_b3dbc03: "rgba(179,219,192,0.3)",
  Op_8080802: "rgba(106,147,141,0.2)",
  OP_b3dbc02: "rgba(179,219,192,0.2)",
  Op_7bae6a5: "rgba(123,174,106,0.5)",
  Op_bdbdbd9: "rgba(189,189,189,0.9)",
  Op_D9D9D98: "rgba(217,217,217,0.8)",
  Op_ffffff5: "rgba(255,255,255,0.5)",
  Op_C0D576: "rgba(200, 213, 118, 0.5)",
  OP_3498db03: "rgba(52, 152, 219,0.3)",
  Op_FFDAE1: "rgba(255, 218, 225, 0.8)",
  Op_C5BBDE: "rgba(197, 187, 222, 0.8)",
};

export const BottonColor = {
  Bt_FFFFFF: "#ffffff",
  Bt_ECECEC: "#ECECEC",
  Bt_DAEEDE: "#DAEEDE",
  Bt_B3DBC0: "#B3DBC0",
  Bt_84B8A2: "#84B8A2",
  Bt_7FBDF6: "#7FBDF6",
  Bt_FFD000: "#FFD000",
  Bt_FF9900: "#FF9900",
  Bt_A5A5A5: "#A5A5A5",
  Bt_666666: "#666666",
  Bt_000000: "#000000",
};

export const BorderColor = {
  Bd_B3DBC0: "#B3DBC0",
  Bd_84B8A2: "#84B8A2",
  Bd_EECB2F: "#EECB2F",
  Bd_EAEAEA: "#EAEAEA",
  Bd_F4F4F4: "#F4F4F4",
  Bd_BDBDBD: "#BDBDBD",
  Bd_A5A5A5: "#A5A5A5",
  Bd_666666: "#666666",
  Bd_000000: "#000000",
};

export const iconColor = {
  Ic_FFFFFF: "#FFFFFF",
  Ic_000000: "#000000",
  Ic_32711D: "#32711D",
  Ic_346359: "#346359",
};

const fonstStyle = StyleSheet.create({
  f8_light: {
    fontSize: FonstSize.f8,
    fontFamily: FontFamily.light,
  },
  f8_medium: {
    fontSize: FonstSize.f8,
    fontFamily: FontFamily.medium,
  },
  f8_bold: {
    fontSize: FonstSize.f8,
    fontFamily: FontFamily.bold,
  },
  f10_light: {
    fontSize: FonstSize.f10,
    fontFamily: FontFamily.light,
  },
  f10_medium: {
    fontSize: FonstSize.f10,
    fontFamily: FontFamily.medium,
  },
  f10_bold: {
    fontSize: FonstSize.f10,
    fontFamily: FontFamily.bold,
  },
  f12_light: {
    fontSize: FonstSize.f12,
    fontFamily: FontFamily.light,
  },
  f12_medium: {
    fontSize: FonstSize.f12,
    fontFamily: FontFamily.medium,
  },
  f12_bold: {
    fontSize: FonstSize.f12,
    fontFamily: FontFamily.bold,
  },
  f14_light: {
    fontSize: FonstSize.f14,
    fontFamily: FontFamily.light,
  },
  f14_medium: {
    fontSize: FonstSize.f14,
    fontFamily: FontFamily.medium,
  },
  f14_bold: {
    fontSize: FonstSize.f14,
    fontFamily: FontFamily.bold,
  },
  f16_light: {
    fontSize: FonstSize.f16,
    fontFamily: FontFamily.light,
  },
  f16_medium: {
    fontSize: FonstSize.f16,
    fontFamily: FontFamily.medium,
  },
  f16_bold: {
    fontSize: FonstSize.f16,
    fontFamily: FontFamily.bold,
  },
  f18_light: {
    fontSize: FonstSize.f18,
    fontFamily: FontFamily.light,
  },
  f18_medium: {
    fontSize: FonstSize.f18,
    fontFamily: FontFamily.medium,
  },
  f18_bold: {
    fontSize: FonstSize.f18,
    fontFamily: FontFamily.bold,
  },
  f20_light: {
    fontSize: FonstSize.f20,
    fontFamily: FontFamily.light,
  },
  f20_medium: {
    fontSize: FonstSize.f20,
    fontFamily: FontFamily.medium,
  },
  f20_bold: {
    fontSize: FonstSize.f20,
    fontFamily: FontFamily.bold,
  },
  f22_light: {
    fontSize: FonstSize.f22,
    fontFamily: FontFamily.light,
  },
  f22_medium: {
    fontSize: FonstSize.f22,
    fontFamily: FontFamily.medium,
  },
  f22_bold: {
    fontSize: FonstSize.f22,
    fontFamily: FontFamily.bold,
  },
  f24_light: {
    fontSize: FonstSize.f24,
    fontFamily: FontFamily.light,
  },
  f24_medium: {
    fontSize: FonstSize.f24,
    fontFamily: FontFamily.medium,
  },
  f24_bold: {
    fontSize: FonstSize.f24,
    fontFamily: FontFamily.bold,
  },
  f30_light: {
    fontSize: FonstSize.f30,
    fontFamily: FontFamily.light,
  },
  f30_medium: {
    fontSize: FonstSize.f30,
    fontFamily: FontFamily.medium,
  },
  f30_bold: {
    fontSize: FonstSize.f30,
    fontFamily: FontFamily.bold,
  },
  f32_light: {
    fontSize: FonstSize.f32,
    fontFamily: FontFamily.light,
  },
  f32_medium: {
    fontSize: FonstSize.f32,
    fontFamily: FontFamily.medium,
  },
  f32_bold: {
    fontSize: FonstSize.f32,
    fontFamily: FontFamily.bold,
  },
});

export default fonstStyle;
