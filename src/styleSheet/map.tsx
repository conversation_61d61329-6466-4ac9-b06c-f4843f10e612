import {
  BgColor,
  FonstSize,
  FonstColor,
  BottonColor,
  BorderColor,
} from "../styleSheet/style_Custom";
import { ScaledSheet } from "react-native-size-matters";
import { Platform, StyleSheet, Dimensions } from "react-native";

const map = ScaledSheet.create({
  containerMap: {
    flex: 1,
    alignItems: "center",
    justifyContent: "flex-end",
  },
  containerDetailmap: {
    flex: 1,
    height: 200,
    marginTop: 10,
    borderRadius: 20,
    overflow: "hidden",
    alignItems: "center",
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapPlot: {
    borderRadius: 25,
    ...StyleSheet.absoluteFillObject,
  },
});

export default map;
