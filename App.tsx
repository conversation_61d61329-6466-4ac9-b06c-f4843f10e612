import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Platform, StyleSheet, PermissionsAndroid } from "react-native";
import "react-native-url-polyfill/auto";
import { MMKV } from "react-native-mmkv";
import messaging from "@react-native-firebase/messaging";
import { SafeAreaProvider } from "react-native-safe-area-context";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { NavigationContainer, DefaultTheme } from "@react-navigation/native";
import notifee, { AuthorizationStatus, EventType } from "@notifee/react-native";
//Login Page
import Singnin from "./src/screen/logingScreen/singnin";
import Select_login from "./src/screen/logingScreen/select_login";
//Home Page
import EditPost from "./src/screen/homeScreen/editPost";
import DetailPost from "./src/screen/homeScreen/detailPost";
//Area Page
import Plusarea from "./src/screen/areaScreen/plusArea";
import DetailPay from "./src/screen/areaScreen/detailPay";
import SelectArea from "./src/screen/areaScreen/selectArea";
import DetailArea from "./src/screen/areaScreen/detailArea";
//PlantDetailScreen
import PlantList from "./src/screen/plantDetailScreen/plantList";
import PlantDetail from "./src/screen/plantDetailScreen/plantDetail";
//ManageFram Page
import ManageFarmUser from "./src/screen/manageFarm/manageFarmUser";
//Manage Control
import ListManageFarm from "./src/screen/manageFarm/manageControl/manageFarm/listManageFarm";
import DetailMangeFarm from "./src/screen/manageFarm/manageControl/manageFarm/detailMangeFarm";
//Operation
import Operation from "./src/screen/manageFarm/manageControl/operation/Operation";
//Delivery
import Delivery from "./src/screen/manageFarm/delivery/delivery";
import PlusAddress from "./src/screen/manageFarm/delivery/plusAddress";
import AddressList from "./src/screen/manageFarm/delivery/addressList";
import BuildAddress from "./src/screen/manageFarm/delivery/buildAddress";
import DetailDelivery from "./src/screen/manageFarm/delivery/detailDelivery";
//PayMent Page
import QrCodepay from "./src/screen/payMent/qrCodepay";
import UpLoadpay from "./src/screen/payMent/upLoadpay";
//Setting Page
import SettingApp from "./src/screen/settingScreen/settingApp";
import Version from "./src/screen/settingScreen/version_app/version";
import System from "./src/screen/settingScreen/setting_system/system";
import Language from "./src/screen/settingScreen/setting_system/language/language";
import Contact_us from "./src/screen/settingScreen/help_support/contact_us/contact_us";
import Permission from "./src/screen/settingScreen/setting_system/Permission/permission";
import menu_help_support from "./src/screen/settingScreen/help_support/menu_help_support";
import Menu_edit_account from "./src/screen/settingScreen/edit_account/menu_edit_account";
import Conected from "./src/screen/settingScreen/edit_account/connected_account/conected";
import Privacy_policy from "./src/screen/settingScreen/help_support/privacy_policy/privacy_policy";
import Change_password from "./src/screen/settingScreen/edit_account/change_password/change_password";
import Icon_references from "./src/screen/settingScreen/help_support/Icon_references/icon_references";
//Profile
import Contact from "./src/screen/homeScreen/contact";
import Profilefriend from "./src/screen/profileScreen/profileFriend";
//Admin
import AdminPage from "./src/screen/adminScreen/adminPage";
import DetailProcess from "./src/screen/adminScreen/detailProcess";
import HistoryManage from "./src/screen/adminScreen/historyManage";
import HistoryPayRequest from "./src/screen/adminScreen/historyPayRequest";
//Goble Page
import Bottom_Tab from "./src/components/bottomTab/bottom_Tab";
import { Provider } from "react-redux";
import reduxStore from "./src/Redux_Store/store";
//Screens
import RedirectScreen from "./src/screens/RedirectScreen";
//Chat
import ChatSystem from "./src/screen/chatScreen/chatSystem";
import ChatHistoryAdmin from "./src/screen/chatScreen/chatHistoryAdmin";
//DrawMap
import drawMap from "./src/components/drawMap/drawMap";

export default function App() {
  const Stack = createNativeStackNavigator();
  const [isInitialScreen, setInitialScreen] = useState<string>("");
  const [fcmToken, setFcmToken] = useState<string>("");
  const storage = new MMKV();

  // Get FCM Token
  const getToken = async () => {
    try {
      if (Platform.OS === "ios") {
        await messaging().requestPermission();
      }
      const token = await messaging().getToken();
      // await AsyncStorage.setItem("fcmToken", token);
      await storage.set("fcmToken", token || "");
      setFcmToken(token);
      // console.log("FCM Token:", token);
    } catch (error) {
      console.error("Error fetching FCM token:", error);
    }
  };
  // Request Notification Permissions (iOS and Android)
  const requestNotificationPermissions = async (): Promise<boolean> => {
    try {
      if (Platform.OS === "ios") {
        const settings = await notifee.requestPermission();
        if (settings.authorizationStatus < AuthorizationStatus.AUTHORIZED) {
          console.log("iOS: Notification permission denied");
          return false;
        }
        return true;
      } else {
        const permission: any =
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS;
        const status = await PermissionsAndroid.request(permission);
        if (status === PermissionsAndroid.RESULTS.GRANTED) {
          console.log("Android: Notification permission granted");
          return true;
        } else {
          console.log("Android: Notification permission denied");
          return false;
        }
      }
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return false;
    }
  };
  // Check Login Status and Set Initial Screen
  useEffect(() => {
    const fetchData = async () => {
      try {
        setInitialScreen("Select_login");
        const firstLogin = storage.getString("successLogin");

        if (firstLogin === "Login Successfully") {
          setInitialScreen("RedirectScreen");
        }
      } catch (error) {
        console.error("Error fetching login status:", error);
        setInitialScreen("Select_login");
      }
    };
    fetchData();
  }, []);
  // Request Notification Permissions
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const granted = await requestNotificationPermissions();
        await AsyncStorage.setItem(
          "notificationPermission",
          granted ? "granted" : "denied"
        );
        if (!granted) {
          console.log("Notification permission not granted.");
        }
      } catch (error) {
        console.error("Error checking notification permission:", error);
      }
    };
    checkPermissions();
    getToken();
  }, []);

  useEffect(() => {
    const unsubscribeForeground = messaging().onMessage(
      async (remoteMessage) => {
        console.log("ได้รับข้อความในโหมด foreground:", remoteMessage);
        console.log("Platform.OS:", Platform.OS); // ตรวจสอบค่า Platform.OS

        const { notification, data } = remoteMessage;
        const { title, body } = notification || {};
        const { goTo, invoiceNumber, type } = data || {};

        try {
          if (Platform.OS === "ios") {
            console.log("ทำงานใน iOS");
            await notifee.displayNotification({
              title: title || "การแจ้งเตือนใหม่",
              body: body || "แจ้งเตือนใหม่",
              ios: {
                foregroundPresentationOptions: {
                  alert: true,
                  badge: true,
                  sound: true,
                  banner: true,
                  list: true,
                },
                categoryId: "default",
                threadId: "default",
                critical: true,
                criticalVolume: 1.0,
                sound: "default",
              },
              android: {
                channelId: "default",
                pressAction: {
                  id: "default",
                },
              },
              data: {
                goTo,
                invoiceNumber,
                type,
              },
            });
          } else {
            console.log("ทำงานใน Android");
            Alert.alert(title || "การแจ้งเตือนใหม่", body || "แจ้งเตือนใหม่", [
              { text: "ตกลง" },
            ]);
          }
        } catch (error) {
          console.error("ข้อผิดพลาดในการแสดงการแจ้งเตือน:", error);
        }
      }
    );

    return () => unsubscribeForeground();
  }, []);

  // useEffect(() => {
  //   const clearCache = async () => {
  //     try {
  //       const keys = await AsyncStorage.getAllKeys();
  //       const keysToKeep = [
  //         "successLogin",
  //         "userIdLogin",
  //         "fcmToken",
  //         "editAddress",
  //         "tokenLogin",
  //         "totalNotifications",
  //         "ACTIVE_BUTTON_KEY",
  //         "en",
  //         "th",
  //       ];
  //       const keysToDelete = keys.filter((key) => !keysToKeep.includes(key));
  //       if (keysToDelete.length > 0) {
  //         await AsyncStorage.multiRemove(keysToDelete);
  //       }
  //     } catch (error) {}
  //   };
  //   const subscription = AppState.addEventListener(
  //     "change",
  //     async (nextAppState) => {
  //       // console.log("🔄 AppState Changed:", nextAppState);
  //       if (nextAppState === "inactive" || nextAppState === "background") {
  //         await clearCache();
  //       }
  //     }
  //   );

  //   return () => {
  //     subscription.remove();
  //   };
  // }, []);

  const MyTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      background: "rgb(255, 255, 255)",
    },
  };

  return (
    <Provider store={reduxStore}>
      <GestureHandlerRootView>
        <SafeAreaProvider>
          <NavigationContainer theme={MyTheme}>
            <Stack.Navigator
              initialRouteName={isInitialScreen}
              screenOptions={{ headerShown: false }}
            >
              <Stack.Screen
                name="Select_login"
                component={Select_login}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="Singnin"
                component={Singnin}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="Bottom_Tab"
                component={Bottom_Tab}
                options={{
                  headerShown: false, // ปิด header ของ Stack รอบนอก
                }}
              />
              <Stack.Screen name="DetailPost" component={DetailPost} />
              <Stack.Screen name="SettingApp" component={SettingApp} />
              <Stack.Screen name="System" component={System} />
              <Stack.Screen name="Language" component={Language} />
              <Stack.Screen name="Plusarea" component={Plusarea} />
              <Stack.Screen name="SelectArea" component={SelectArea} />
              <Stack.Screen name="DetailArea" component={DetailArea} />
              <Stack.Screen name="DetailPay" component={DetailPay} />
              <Stack.Screen name="QrCodepay" component={QrCodepay} />
              <Stack.Screen name="UpLoadpay" component={UpLoadpay} />
              <Stack.Screen name="ManageFarmUser" component={ManageFarmUser} />
              <Stack.Screen name="Delivery" component={Delivery} />
              <Stack.Screen name="PlusAddress" component={PlusAddress} />
              <Stack.Screen name="AddressList" component={AddressList} />
              <Stack.Screen name="BuildAddress" component={BuildAddress} />
              <Stack.Screen name="DetailDelivery" component={DetailDelivery} />
              <Stack.Screen name="listManageFarm" component={ListManageFarm} />
              <Stack.Screen
                name="DetailMangeFarm"
                component={DetailMangeFarm}
              />
              <Stack.Screen name="Permission" component={Permission} />
              <Stack.Screen
                name="menu_help_support"
                component={menu_help_support}
              />
              <Stack.Screen
                name="Icon_references"
                component={Icon_references}
              />
              <Stack.Screen name="Privacy_policy" component={Privacy_policy} />
              <Stack.Screen name="Contact_us" component={Contact_us} />
              <Stack.Screen
                name="Menu_edit_account"
                component={Menu_edit_account}
              />
              <Stack.Screen
                name="Change_password"
                component={Change_password}
              />
              <Stack.Screen name="Conected" component={Conected} />
              <Stack.Screen name="Contact" component={Contact} />
              <Stack.Screen name="EditPost" component={EditPost} />
              <Stack.Screen name="Operation" component={Operation} />
              <Stack.Screen name="Profilefriend" component={Profilefriend} />
              <Stack.Screen
                name="AdminPage"
                component={AdminPage}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="DetailProcess"
                component={DetailProcess}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="HistoryPayRequest"
                component={HistoryPayRequest}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="HistoryManage"
                component={HistoryManage}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen name="Version" component={Version} />
              <Stack.Screen
                name="RedirectScreen"
                component={RedirectScreen}
                options={{ headerShown: false }}
              />
              <Stack.Screen name="drawMap" component={drawMap} />
              <Stack.Screen
                name="ChatSystem"
                component={ChatSystem}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="PlantDetail"
                component={PlantDetail}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="PlantList"
                component={PlantList}
                options={{
                  orientation: "portrait",
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="ChatHistoryAdmin"
                component={ChatHistoryAdmin}
              />
            </Stack.Navigator>
          </NavigationContainer>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </Provider>
  );
}
