# macOS
.DS_Store

# Xcode (iOS)
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData/
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
build/
.idea/
.gradle/
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
*.tmp

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots/
**/fastlane/test_output/

# Bundle artifacts
*.jsbundle
*.map
*.pack

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Testing
/coverage/
*.lcov

# Build files
*.aab
*.apk
*.ipa

# VSCode settings
.vscode/
*.code-workspace

# Assets and Generated Files
android/app/src/main/assets/index.android.bundle
android/app/src/main/assets/sourcemap.js

# Firebase
google-services.json
android/app/google-services.json
ios/GoogleService-Info.plist

# Temporary files
*.swp
*.lock
*.bak
*.tmp
*.orig

# Logs
logs/
*.log
log/

# Environment variables
.env
.env.*
.env.local

# Debugging
debug/
*.stackdump
*.crash
*.trace

# Miscellaneous
Thumbs.db
*.rspec
*.sqlite3
*.sqlite
*.db

# Additional Config
.idea/
android/.gradle/
